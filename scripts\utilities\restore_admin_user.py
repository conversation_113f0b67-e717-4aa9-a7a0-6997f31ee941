#!/usr/bin/env python3
"""
Script <NAME_EMAIL> as admin user in firm
"""

import os
from google.cloud import firestore
from google.cloud.firestore import SERVER_TIMESTAMP
from firebase_admin import auth as firebase_auth
from firebase_admin import initialize_app, credentials
from dotenv import load_dotenv
import json

def restore_admin_user():
    """Restore <EMAIL> as admin in the specified firm"""
    
    # Load environment variables
    load_dotenv()
    
    # Initialize Firebase Admin SDK
    cred_path = os.getenv('FIREBASE_CREDENTIALS_PATH')
    if not cred_path or not os.path.exists(cred_path):
        print("❌ Firebase credentials file not found")
        return
    
    # Initialize Firebase app if not already initialized
    try:
        cred = credentials.Certificate(cred_path)
        initialize_app(cred)
    except ValueError:
        # App already initialized
        pass
    
    # Initialize Firestore client
    db = firestore.Client()
    
    # User details
    email = "<EMAIL>"
    firm_id = "83a0939b-dcbb-4725-b1db-5e0acc5e8607"
    
    print(f"🔍 Looking up Firebase user for {email}...")
    
    try:
        # Get Firebase user by email
        firebase_user = firebase_auth.get_user_by_email(email)
        user_id = firebase_user.uid
        display_name = firebase_user.display_name or email.split("@")[0]
        
        print(f"✅ Found Firebase user: {user_id}")
        print(f"   Display name: {display_name}")
        
        # Check if firm exists
        print(f"\n🔍 Checking if firm {firm_id} exists...")
        firm_ref = db.collection("FIRMS").document(firm_id)
        firm_doc = firm_ref.get()
        
        if not firm_doc.exists:
            print(f"❌ Firm {firm_id} not found!")
            return
        
        firm_data = firm_doc.to_dict()
        print(f"✅ Found firm: {firm_data.get('name', 'Unknown')}")
        
        # Check if user already exists in FIRM_USERS
        print(f"\n🔍 Checking if user already exists in FIRM_USERS...")
        existing_users = db.collection("FIRM_USERS")\
            .where("user_id", "==", user_id)\
            .where("firm_id", "==", firm_id)\
            .limit(1)\
            .stream()
        
        existing_user = None
        for user_doc in existing_users:
            existing_user = user_doc
            break
        
        if existing_user:
            print(f"⚠️  User already exists in FIRM_USERS: {existing_user.id}")
            user_data = existing_user.to_dict()
            print(f"   Current role: {user_data.get('role', 'unknown')}")
            print(f"   Current status: {user_data.get('status', 'unknown')}")
            
            # Update to ensure admin role and active status
            update_data = {
                "role": "firm_admin",
                "status": "active",
                "updated_at": SERVER_TIMESTAMP
            }
            existing_user.reference.update(update_data)
            print(f"✅ Updated user to firm_admin with active status")
            
        else:
            # Create new firm user document
            print(f"\n📝 Creating new FIRM_USERS document...")
            
            firm_user_data = {
                "user_id": user_id,
                "firm_id": firm_id,
                "email": email,
                "display_name": display_name,
                "role": "firm_admin",
                "assigned_client_ids": [],
                "status": "active",
                "created_at": SERVER_TIMESTAMP,
                "updated_at": SERVER_TIMESTAMP,
            }
            
            # Add to FIRM_USERS collection
            firm_user_ref = db.collection("FIRM_USERS").add(firm_user_data)
            doc_id = firm_user_ref[1].id
            
            print(f"✅ Created FIRM_USERS document: {doc_id}")
        
        print(f"\n🎉 Successfully restored {email} as firm_admin in firm {firm_id}")
        
        # Display summary
        print(f"\n📋 Summary:")
        print(f"   Email: {email}")
        print(f"   User ID: {user_id}")
        print(f"   Firm ID: {firm_id}")
        print(f"   Role: firm_admin")
        print(f"   Status: active")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    restore_admin_user() 