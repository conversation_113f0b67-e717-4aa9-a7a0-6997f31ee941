import os
import json
import httpx
import time # For synchronous sleep in token refresh if needed, though asyncio.sleep is preferred for async parts
import asyncio
import logging
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional
import base64

from .base_client import BaseAccountingClient
from .firestore_token_storage import FirestoreTokenStorage

# Define custom exception for Xero API errors
class XeroApiException(Exception):
    def __init__(self, message, status_code=None, response_data=None):
        super().__init__(message)
        self.status_code = status_code
        self.response_data = response_data

class XeroApiClient(BaseAccountingClient):
    """
    Xero API Client implementing BaseAccountingClient.
    Handles OAuth 2.0 authentication, token refresh, and API requests using httpx.
    """

    #Synchronous __init__
    def __init__(self, platform_org_id: str, tenant_id: str, config: Optional[Dict[str, Any]] = None):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.info(f"XeroApiClient synchronous __init__ called for platform_org_id='{platform_org_id}', tenant_id='{tenant_id}'")

        self.platform_org_id = platform_org_id  # This is the Xero Tenant ID (Xero Org ID)
        self.app_tenant_id = tenant_id          # This is our application's tenant ID

        # Initialize attributes that will be set by token loading
        self.access_token: Optional[str] = None
        self.refresh_token: Optional[str] = None
        self.token_expires_at: Optional[datetime] = None
        self.id_token: Optional[str] = None # If you use OpenID Connect

        # Load configuration
        app_config = config if config is not None else {}
        self.client_id = app_config.get("XERO_CLIENT_ID", os.getenv("XERO_CLIENT_ID"))
        self.client_secret = app_config.get("XERO_CLIENT_SECRET", os.getenv("XERO_CLIENT_SECRET"))
        self.redirect_uri = app_config.get("XERO_REDIRECT_URI", os.getenv("XERO_REDIRECT_URI_REST_API")) # Using REST API redirect URI
        self.gcp_project_id = app_config.get("GCP_PROJECT_ID", os.getenv("GCP_PROJECT_ID"))
        self.secret_name_prefix = app_config.get("XERO_OAUTH_SECRET_NAME_PREFIX", os.getenv("XERO_OAUTH_SECRET_NAME_PREFIX", "xero-oauth-tokens"))
        self.token_url = app_config.get("XERO_TOKEN_URL", "https://identity.xero.com/connect/token") # Added for refresh_access_token
        self.api_url = app_config.get("XERO_API_URL", "https://api.xero.com/api.xro/2.0") # Added for _httpx_request and other api calls

        # Initialize timeout attribute - AGGRESSIVE OPTIMIZATION: Reduce default timeout
        self.timeout = float(app_config.get("HTTP_TIMEOUT", os.getenv("HTTP_TIMEOUT", 10.0)))

        # Initialize Firestore token storage (cost-effective alternative to Secret Manager)
        encryption_key = app_config.get("TOKEN_ENCRYPTION_KEY", os.getenv("TOKEN_ENCRYPTION_KEY"))
        self.token_storage = FirestoreTokenStorage(self.gcp_project_id, encryption_key)

        if not all([self.client_id, self.client_secret, self.redirect_uri, self.gcp_project_id]):
            self.logger.error("XeroApiClient is missing critical configuration (client_id, client_secret, redirect_uri, or gcp_project_id).")
            # Consider raising an error here if these are essential for all operations
            # For now, just logging, as some methods might not need them immediately.

        self.logger.info(f"XeroApiClient config loaded: client_id set: {'yes' if self.client_id else 'no'}, secret_set: {'yes' if self.client_secret else 'no'}, timeout: {self.timeout}")
        self.logger.info(f"Synchronous __init__ completed for platform_org_id='{platform_org_id}', tenant_id='{self.app_tenant_id}'. Token loading will be done separately.")

    @classmethod
    async def create(cls, platform_org_id: str, tenant_id: str, config: Optional[Dict[str, Any]] = None):
        """
        Asynchronous factory method to create and initialize a XeroApiClient instance.
        """
        instance = cls(platform_org_id, tenant_id, config)
        instance.logger.info(f"XeroApiClient.create: Post-sync-init, pre-token-load for platform_org_id='{instance.platform_org_id}', app_tenant_id='{instance.app_tenant_id}'")
        try:
            await instance._load_tokens_from_secret()
            instance.logger.info(f"XeroApiClient.create: Post-token-load for platform_org_id='{instance.platform_org_id}', app_tenant_id='{instance.app_tenant_id}'. Access token loaded: {'Yes' if instance.access_token else 'No'}")
        except Exception as e:
            instance.logger.error(f"XeroApiClient.create: Failed to load tokens during instance creation for platform_org_id='{instance.platform_org_id}', app_tenant_id='{instance.app_tenant_id}'. Error: {e}", exc_info=True)
            # Depending on requirements, you might re-raise or handle this (e.g., instance is created but without tokens)
            # For now, it will log and the instance will have no tokens.
        return instance

    @property
    def oauth_secret_name(self) -> Optional[str]:
        """
        Property that returns the OAuth secret name used for this client.
        This is used by external code to reference the secret in Secret Manager.
        """
        return self._construct_secret_id()

    def _construct_secret_id(self) -> Optional[str]:
        """Constructs the unique SECRET ID (not the full path) based on app_tenant_id and platform_org_id."""
        if not self.app_tenant_id or not self.platform_org_id:
            self.logger.warning(f"Cannot construct secret ID: app_tenant_id ('{self.app_tenant_id}') or platform_org_id ('{self.platform_org_id}') is missing.")
            return None
        # Replace characters not allowed in secret IDs if necessary.
        # Allowed characters: letters, numbers, hyphens (-), and underscores (_). Max length 255 chars.
        # Assuming app_tenant_id and platform_org_id are generally safe or will be sanitized before this point if needed.
        safe_app_tenant_id = self.app_tenant_id.replace(":", "-").replace("/", "-") # Example basic sanitization
        safe_platform_org_id = self.platform_org_id.replace(":", "-").replace("/", "-")

        constructed_id = f"{self.secret_name_prefix}_{safe_app_tenant_id}_{safe_platform_org_id}"
        if len(constructed_id) > 255:
            self.logger.error(f"Constructed secret ID '{constructed_id[:50]}...' is too long ({len(constructed_id)} chars). Max 255.")
            # Implement truncation or hashing if this becomes an issue. For now, log and return None or raise.
            return None
        return constructed_id

    async def _load_tokens_from_secret(self) -> bool:
        """Load tokens from Firestore instead of Secret Manager (cost-effective)"""
        if not self.platform_org_id or self.platform_org_id == "OAUTH_CALLBACK_PLACEHOLDER":
            self.logger.info(f"Token loading skipped: platform_org_id is '{self.platform_org_id}', not loading tokens for this context (app_tenant_id: {self.app_tenant_id}).")
            return False

        if not self.app_tenant_id:
            self.logger.error(f"Cannot load tokens: app_tenant_id is missing (platform_org_id='{self.platform_org_id}').")
            return False

        self.logger.info(f"Attempting to load tokens from Firestore for app_tenant_id='{self.app_tenant_id}', platform_org_id='{self.platform_org_id}'")

        try:
            token_data = await self.token_storage.load_tokens(self.app_tenant_id, self.platform_org_id)

            if not token_data:
                self.logger.info(f"No tokens found in Firestore for app_tenant_id='{self.app_tenant_id}', platform_org_id='{self.platform_org_id}'. This is normal if tokens haven't been stored yet.")
                return False

            self.access_token = token_data.get("access_token")
            self.refresh_token = token_data.get("refresh_token")
            self.token_expires_at = token_data.get("expires_at")

            if self.access_token and self.refresh_token and self.token_expires_at:
                self.logger.info(f"Successfully loaded tokens from Firestore for Xero Tenant ID {self.platform_org_id}.")
            else:
                self.logger.info(f"Incomplete token set found in Firestore for Xero Tenant ID {self.platform_org_id}. AccessToken: {bool(self.access_token)}, RefreshToken: {bool(self.refresh_token)}, ExpiresAt: {bool(self.token_expires_at)}")

            return True
        except Exception as e:
            self.logger.error(f"Failed to load tokens from Firestore for app_tenant_id='{self.app_tenant_id}', platform_org_id='{self.platform_org_id}': {e}", exc_info=True)
            return False

    async def _save_tokens_to_secret(self) -> bool:
        """Save tokens to Firestore instead of Secret Manager (cost-effective)"""
        if not self.access_token or not self.refresh_token or not self.token_expires_at:
            self.logger.error("Attempted to save tokens, but essential token data is missing from the client instance.")
            return False

        if self.platform_org_id == "OAUTH_CALLBACK_PLACEHOLDER" or not self.platform_org_id:
            self.logger.error(f"Cannot save tokens with placeholder or missing platform_org_id: '{self.platform_org_id}' for app_tenant_id '{self.app_tenant_id}'.")
            return False

        if not self.app_tenant_id:
             self.logger.error(f"Cannot save tokens: app_tenant_id is not set for platform_org_id '{self.platform_org_id}'.")
             return False

        try:
            success = await self.token_storage.save_tokens(
                self.app_tenant_id,
                self.platform_org_id,
                self.access_token,
                self.refresh_token,
                self.token_expires_at
            )

            if success:
                self.logger.info(f"Successfully saved tokens to Firestore for Xero Tenant ID {self.platform_org_id}, App Tenant ID {self.app_tenant_id}.")
            else:
                self.logger.error(f"Failed to save tokens to Firestore for Xero Tenant ID {self.platform_org_id}")

            return success
        except Exception as e:
            self.logger.error(f"Failed to save tokens to Firestore for Xero Tenant ID {self.platform_org_id}: {e}", exc_info=True)
            return False

    async def refresh_access_token(self) -> str:
        """
        Refreshes the Xero access token using the stored refresh token.
        Saves the new tokens.
        """
        if not self.refresh_token:
            self.logger.error("No refresh token available to refresh Xero access token.")
            raise ValueError("No refresh token available for Xero.")
        if not self.client_id or not self.client_secret:
            self.logger.error("Client ID or Client Secret not configured for Xero token refresh.")
            raise ValueError("Client ID or Secret not configured.")

        data = {
            "grant_type": "refresh_token",
            "refresh_token": self.refresh_token,
            "client_id": self.client_id,
            "client_secret": self.client_secret,
        }
        headers = {"Content-Type": "application/x-www-form-urlencoded"}

        async with httpx.AsyncClient() as client:
            try:
                self.logger.info(f"Attempting to refresh Xero access token for Tenant ID: {self.platform_org_id}")
                response = await client.post(self.token_url, data=data, headers=headers)
                response.raise_for_status()  # Raises HTTPStatusError for 4xx/5xx

                new_tokens = response.json()
                self.access_token = new_tokens["access_token"]

                if "refresh_token" in new_tokens:
                    self.refresh_token = new_tokens["refresh_token"]

                expires_in = new_tokens["expires_in"]
                aware_now = datetime.now(timezone.utc) # Get aware current time
                expires_at_dt = aware_now + timedelta(seconds=expires_in) # Calculate expiry datetime
                # Convert to ISO string with Z and store
                self.token_expires_at = expires_at_dt

                self.logger.info(f"Successfully refreshed Xero access token for Tenant ID: {self.platform_org_id}. New expiry: {self.token_expires_at}")
                await self._save_tokens_to_secret()
                return self.access_token
            except httpx.HTTPStatusError as e:
                self.logger.error(f"Failed to refresh Xero token for Tenant ID {self.platform_org_id}: {e.response.status_code} - {e.response.text}")
                # Potentially handle specific errors e.g. invalid_grant (refresh token expired/revoked)
                raise Exception(f"Xero token refresh failed: {e.response.text}") from e
            except Exception as e:
                self.logger.error(f"An unexpected error occurred during Xero token refresh for Tenant ID {self.platform_org_id}: {e}")
                raise

    async def get_current_access_token(self) -> str:
        """
        Ensures a valid Xero access token is available, refreshing if necessary.
        Returns the valid access token.
        """
        if self.access_token and self.token_expires_at:
            try:
                # Perform the comparison using aware objects
                if datetime.now(timezone.utc) < self.token_expires_at - timedelta(minutes=5):
                    self.logger.debug(f"Using existing Xero access token for Tenant ID: {self.platform_org_id}")
                    return self.access_token
                else:
                    self.logger.info(f"Xero access token for Tenant ID {self.platform_org_id} is expired or nearing expiry (expires at {self.token_expires_at}). Refreshing.")
            except ValueError as e:
                 # Handle case where the stored object is invalid
                 self.logger.warning(f"Could not parse stored token expiry object: {e}. Attempting refresh.")
                 # Fall through to refresh logic below
        else:
            self.logger.info(f"No valid Xero access token or expiry object found for Tenant ID {self.platform_org_id}. Attempting refresh.")

        # If checks above fail or token is expired, refresh
        return await self.refresh_access_token()

    async def is_authenticated(self) -> bool:
        """
        Check if the client is authenticated with valid tokens.
        Returns True if we have valid access and refresh tokens, False otherwise.
        """
        try:
            # Check if we have the basic tokens
            if not self.access_token or not self.refresh_token:
                self.logger.debug(f"Authentication check failed: Missing tokens for Tenant ID {self.platform_org_id}")
                return False

            # Check if we have a valid expiry time
            if not self.token_expires_at:
                self.logger.debug(f"Authentication check failed: Missing token expiry for Tenant ID {self.platform_org_id}")
                return False

            # Check if token is not expired (with 5 minute buffer)
            if datetime.now(timezone.utc) >= self.token_expires_at - timedelta(minutes=5):
                self.logger.debug(f"Authentication check: Token expired or expiring soon for Tenant ID {self.platform_org_id}")
                # Try to refresh the token
                try:
                    await self.refresh_access_token()
                    return True
                except Exception as e:
                    self.logger.warning(f"Authentication check: Token refresh failed for Tenant ID {self.platform_org_id}: {e}")
                    return False

            self.logger.debug(f"Authentication check passed for Tenant ID {self.platform_org_id}")
            return True

        except Exception as e:
            self.logger.error(f"Error during authentication check for Tenant ID {self.platform_org_id}: {e}")
            return False

    async def _httpx_request(
        self,
        method: str,
        url: str,
        params: Optional[Dict[str, Any]] = None,
        json_data: Optional[Dict[str, Any]] = None,
        is_attachment: bool = False,
        timeout: float = 10.0,
        max_retries: int = 2,
        extra_headers: Optional[Dict[str, str]] = None, # NEW: For additional headers like If-Modified-Since
        expected_status_codes: Optional[List[int]] = None # NEW: For expected status codes
    ) -> httpx.Response:
        """
        Private helper to make requests to Xero API using httpx with rate limit handling.
        Automatically includes Authorization and Xero-Tenant-Id headers.
        """
        current_access_token = await self.get_current_access_token()

        headers = {
            'Authorization': f'Bearer {current_access_token}',
            'Xero-Tenant-Id': self.platform_org_id, # platform_org_id is Xero Tenant ID
            'Accept': 'application/octet-stream' if is_attachment else 'application/json'
        }
        if method.upper() in ["POST", "PUT"] and json_data:
             headers['Content-Type'] = 'application/json'

        if extra_headers: # NEW: Merge extra headers
            headers.update(extra_headers)

        async with httpx.AsyncClient(timeout=timeout) as client:
            for attempt in range(max_retries):
                try:
                    self.logger.debug(f"Making {method} request to {url} (Attempt {attempt + 1})")
                    response = await client.request(method, url, headers=headers, params=params, json=json_data)

                    if response.status_code == 401: # Unauthorized
                        self.logger.warning(f"Xero API request to {url} failed with 401 Unauthorized. Attempting token refresh.")
                        current_access_token = await self.refresh_access_token() # Force refresh
                        headers['Authorization'] = f'Bearer {current_access_token}' # Update header
                        # Retry immediately with the new token in the next loop iteration
                        if attempt < max_retries -1: # Avoid extra sleep if it's the last attempt
                            await asyncio.sleep(0.5) # Reduced delay before retrying with new token
                            continue
                        else: # Last attempt failed even after refresh
                             response.raise_for_status()

                    if response.status_code == 429:  # Rate limit
                        retry_after = int(response.headers.get('Retry-After', '60'))
                        problem = response.headers.get('X-Rate-Limit-Problem', 'Rate limit exceeded')
                        self.logger.warning(f"[RATE LIMIT] Hit Xero {problem} for {url}. Waiting {retry_after} seconds...")
                        await asyncio.sleep(retry_after)
                        continue  # Retry the request

                    min_remaining = response.headers.get('X-MinLimit-Remaining')
                    if min_remaining is not None and int(min_remaining) < 5:
                        self.logger.warning(f"[API USAGE] Only {min_remaining} Xero API calls left this minute (URL: {url}).")

                    if expected_status_codes and response.status_code not in expected_status_codes:
                        self.logger.error(f"Unexpected status code {response.status_code} for {url}. Expected one of: {expected_status_codes}. Response: {response.text}")
                        response.raise_for_status()

                    response.raise_for_status()  # Raise HTTPStatusError for other bad responses (4xx or 5xx)
                    return response

                except httpx.HTTPStatusError as e:
                    # If 401 was not caught above or refresh failed leading to another 401/error
                    self.logger.error(f"HTTP error {e.response.status_code} for {e.request.url} - Attempt {attempt + 1}/{max_retries}. Response: {e.response.text}")
                    if e.response.status_code == 401 and attempt < max_retries -1 : # if it's 401 and not the last attempt, it might have been due to refresh failure
                        self.logger.info(f"Retrying after 401, assuming token was refreshed by now or will be by get_current_access_token.")
                        # allow loop to retry, get_current_access_token will be called again
                    elif attempt == max_retries - 1:
                        raise
                    await asyncio.sleep(0.5 + attempt) # Reduced linear backoff instead of exponential
                except httpx.RequestError as e: # Network errors
                    self.logger.error(f"Request error for {e.request.url} - Attempt {attempt + 1}/{max_retries}: {e}")
                    if attempt == max_retries - 1:
                        raise
                    await asyncio.sleep(0.5 + attempt) # Reduced linear backoff instead of exponential
                except Exception as e: # Catch-all for unexpected errors during request processing
                    self.logger.error(f"Unexpected error during API request to {url} (Attempt {attempt+1}): {e}")
                    if attempt == max_retries - 1:
                        raise
                    await asyncio.sleep(0.5 + attempt)

        raise Exception(f"Max retries exceeded for Xero API request to {url}")

    async def get_records(
        self,
        record_type: str,
        ids: Optional[List[str]] = None,
        where_filter: Optional[str] = None,
        order: Optional[str] = None,
        if_modified_since: Optional[str] = None,
        page: Optional[int] = None, # Added for direct page fetching if needed by other methods
    ) -> List[Dict[str, Any]]:
        """
        Fetches records of a specific type (e.g., Invoices, Accounts, Contacts).
        Handles pagination automatically if 'page' is not specified.
        """
        # Ensuring token validity is handled by _httpx_request -> get_current_access_token()
        # await self._ensure_token_valid() # This line was causing an AttributeError and is removed.
        all_records: List[Dict[str, Any]] = []
        current_page = page if page is not None else 1

        url_segment = record_type
        base_url = f"{self.api_url}/{url_segment}"

        params: Dict[str, Any] = {}
        if ids:
            params["IDs"] = ",".join(ids)
        if where_filter:
            params["where"] = where_filter
        if order:
            params["order"] = order

        headers = {}
        if if_modified_since and not ids: # If-Modified-Since typically for lists, not specific IDs
            headers["If-Modified-Since"] = if_modified_since
            self.logger.info(f"Using If-Modified-Since: {if_modified_since}")
        else:
            self.logger.info(f"If-Modified-Since: N/A (either not provided or fetching specific IDs)")


        while True:
            params["page"] = current_page
            self.logger.info(
                f"Fetching {record_type} page {current_page} with filter: '{where_filter}', order: '{order}', IDs: {ids if ids else 'None'}, If-Modified-Since: {headers.get('If-Modified-Since', 'N/A')}"
            )

            try:
                response_obj = await self._httpx_request(
                    method="GET",
                    url=base_url,
                    params=params,
                    extra_headers=headers,
                    expected_status_codes=[200, 304],
                )

                # Handle 304 Not Modified specifically
                if response_obj.status_code == 304:
                    self.logger.info(f"304 Not Modified for {record_type} page {current_page} (If-Modified-Since). No new data. Stopping pagination.")
                    break

                response_data_dict = response_obj.json()

                if not response_data_dict:
                    self.logger.warning(f"Empty or invalid JSON data for {record_type} page {current_page} despite HTTP success. Stopping pagination.")
                    break

                records_on_this_page = response_data_dict.get(record_type)

                # --- DEBUG LOGGING FOR ACCOUNTS ---
                if record_type == "Accounts" and current_page > 1:
                    self.logger.info(f"DEBUG_COA_SYNC: Page {current_page} raw response_data_dict keys: {list(response_data_dict.keys()) if response_data_dict else 'None'}")
                    if records_on_this_page is not None:
                        self.logger.info(f"DEBUG_COA_SYNC: Page {current_page} - len(records_on_this_page): {len(records_on_this_page)}")
                        if current_page > 2 and len(records_on_this_page) > 0 :
                             self.logger.info(f"DEBUG_COA_SYNC: Page {current_page} - First record sample: {records_on_this_page[0] if records_on_this_page else 'N/A'}")
                    else:
                        self.logger.info(f"DEBUG_COA_SYNC: Page {current_page} - 'records_on_this_page' (i.e., response_data_dict.get('{record_type}')) is None.")
                # --- END DEBUG LOGGING ---

                if records_on_this_page is None:
                    self.logger.info(
                        f"No key '{record_type}' in JSON or value is None on page {current_page}. Concluding fetch."
                    )
                    break
                if not records_on_this_page and isinstance(records_on_this_page, list):
                     self.logger.info(
                        f"Empty list for '{record_type}' on page {current_page}. Concluding fetch."
                    )
                     break

                all_records.extend(records_on_this_page)

                if page is not None: # If a specific page was requested, fetch only that page
                    self.logger.info(f"Fetched specifically requested page {page} for {record_type}.")
                    break

                # If it's "Accounts", assume not paginated by default and break after first successful fetch.
                # This is because Xero API typically returns all accounts in one go for /Accounts.
                if record_type == "Accounts":
                    self.logger.info(f"Assuming '{record_type}' endpoint is not paginated or all records were returned on page {current_page}. Concluding fetch for Accounts.")
                    break

                # Standard pagination break for other record types: if the list is empty
                # This condition should ideally also check if len(records_on_this_page) < expected_page_size if Xero provided that info.
                if not records_on_this_page and isinstance(records_on_this_page, list):
                     self.logger.info(
                        f"Empty list for '{record_type}' on page {current_page}. Concluding fetch."
                    )
                     break

                current_page += 1

            except httpx.HTTPStatusError as e:
                if e.response.status_code == 304: # Not Modified
                    self.logger.info(f"304 Not Modified for {record_type} page {current_page} using If-Modified-Since. No new data.")
                else:
                    # For other HTTP errors, log and re-raise to be handled by the caller or break.
                    self.logger.error(f"HTTP error {e.response.status_code} fetching {record_type} page {current_page}: {e.response.text}", exc_info=True)
                break # Stop pagination on any HTTP error or 304
            except Exception as e:
                self.logger.error(f"Unexpected error fetching {record_type} page {current_page}: {e}", exc_info=True)
                break # Stop pagination on unexpected error

        self.logger.info(f"Total {len(all_records)} {record_type} fetched.")
        return all_records

    async def get_record_detail(
        self,
        record_type: str,
        record_id: str,
    ) -> Optional[Dict[str, Any]]:
        """Fetches detailed information for a single record."""
        url = f"{self.api_url}/{record_type}/{record_id}"
        self.logger.info(f"Fetching {record_type} ID {record_id} from Xero for Tenant ID {self.platform_org_id}")
        try:
            response = await self._httpx_request("GET", url)
            response_data = response.json()
            records_list = response_data.get(record_type)
            if isinstance(records_list, list) and records_list:
                return records_list[0]
            elif records_list is not None: # Key exists but isn't a list (unexpected)
                self.logger.warning(f"Unexpected response structure for {record_type}. Expected a list under the '{record_type}' key, got {type(records_list)}. Data: {response_data}")
                return None
            else: # Key doesn't exist
                self.logger.info(f"No '{record_type}' key found in the response. Returning None. Response keys: {response_data.keys()}")
                return None
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 404:
                self.logger.warning(f"{record_type} with ID {record_id} not found (404). Returning None.")
                return None
            else:
                self.logger.error(f"HTTP error fetching {record_type} ID {record_id}: {e}")
                raise
        except Exception as e:
            self.logger.error(f"Failed to get {record_type} ID {record_id} from Xero for Tenant ID {self.platform_org_id}: {e}")
            raise

    async def download_attachment_content(
        self,
        attachment_url: str # This is the direct, absolute URL from Xero
    ) -> bytes:
        """Downloads the content of an attachment given its direct URL from Xero."""
        self.logger.info(f"Downloading attachment from Xero URL: {attachment_url} for Tenant ID {self.platform_org_id}")
        try:
            # The attachment_url from Xero is absolute and used directly.
            # _httpx_request will add Auth and Xero-Tenant-Id headers.
            response = await self._httpx_request("GET", attachment_url, is_attachment=True)
            return response.content
        except Exception as e:
            self.logger.error(f"Failed to download attachment from Xero ({attachment_url}) for Tenant ID {self.platform_org_id}: {e}")
            raise

    # Example of how you might add a Xero-specific utility method if needed
    async def get_organisation_details(self) -> Dict[str, Any]:
        """Fetches details for the currently connected Xero organisation."""
        url = f"{self.api_url}/Organisation" # Corrected endpoint for singular Organisation
        self.logger.info(f"Fetching Organisation details from Xero for Tenant ID {self.platform_org_id}")
        try:
            response = await self._httpx_request("GET", url)
            organisations = response.json().get("Organisations", []) # Response is {"Organisations": [{...}]}
            if organisations:
                return organisations[0]
            return {}
        except Exception as e:
            self.logger.error(f"Failed to get Organisation details from Xero for Tenant ID {self.platform_org_id}: {e}")
            raise

    async def create_manual_journal(self, journal_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Creates a new manual journal in Xero.

        Args:
            journal_data: A dictionary containing the journal details.
                          Expected structure (example from our PROPOSED_JOURNALS):
                          {
                              "narration": "Monthly Amortization Expense",
                              "journalDate": "2024-07-31T00:00:00Z", # ISO Format DateTime as string
                              "status": "DRAFT", # or "POSTED"
                              "lines": [
                                  {
                                      "accountCode": "EXP400",
                                      "description": "Amortization for Inv-123, Software",
                                      "amount": 100.00,
                                      "isDebit": True,
                                      "isCredit": False
                                  },
                                  {
                                      "accountCode": "ASS150",
                                      "description": "Amortization for Inv-123, Software",
                                      "amount": 100.00,
                                      "isDebit": False,
                                      "isCredit": True
                                  }
                              ],
                              # Optional fields from PROPOSED_JOURNALS if needed by Xero:
                              # "lineAmountTypes": "NoTax",
                              # "showOnCashBasisReports": True,
                              # "currencyCode": "NZD" # If Xero requires it at journal level
                          }

        Returns:
            A dictionary containing the API response from Xero, or None if an error occurs.
        """
        endpoint = "ManualJournals"
        xero_journal_lines = []

        # Convert journalDate from ISO string (potentially with Z) to YYYY-MM-DD for Xero
        journal_date_iso = journal_data.get("journalDate")
        xero_journal_date_str = None
        if journal_date_iso:
            try:
                dt_obj = datetime.fromisoformat(journal_date_iso.replace("Z", "+00:00"))
                xero_journal_date_str = dt_obj.strftime("%Y-%m-%d")
            except ValueError:
                self.logger.error(f"Invalid journalDate format: {journal_date_iso}. Expected ISO format.")
                # Handle error appropriately, maybe raise or return an error response
                return {"error": "InvalidDateFormat", "message": f"Invalid journalDate format: {journal_date_iso}"}


        for line in journal_data.get("lines", []):
            xero_line = {
                "AccountCode": line.get("accountCode"),
                "Description": line.get("description"),
                # "TaxType": "NONE" # Xero usually infers TaxType from AccountCode settings or LineAmountTypes.
                                  # For NoTax journals, often not needed or specific values like "NONE", "EXEMPTOUTPUT"
                                  # We'll start without explicitly setting it per line if LineAmountTypes is NoTax.
            }
            amount = line.get("amount", 0.0)

            # Strategy 1: Use LineAmount (positive for debit, negative for credit)
            if line.get("isDebit"):
                xero_line["LineAmount"] = amount
            elif line.get("isCredit"):
                xero_line["LineAmount"] = -amount
            else:
                self.logger.warning(f"Journal line has no debit/credit flag: {line}. Skipping amount.")
                # Or, if Xero requires balanced lines and this implies 0:
                # xero_line["LineAmount"] = 0.0

            # TODO: Add TrackingCategories if present in line and needed
            # Example:
            # if line.get("trackingCategories"):
            #     xero_line["Tracking"] = [] # Map our format to Xero's TrackingCategory format
            #     for tc_data in line.get("trackingCategories"):
            #         xero_line["Tracking"].append({
            #             "TrackingCategoryID": tc_data.get("trackingCategoryID"), # or Name
            #             "TrackingOptionID": tc_data.get("trackingOptionID")    # or Option
            #         })

            xero_journal_lines.append(xero_line)

        payload = {
            "Narration": journal_data.get("narration"),
            "Date": xero_journal_date_str,
            "Status": journal_data.get("status", "DRAFT").upper(), # Xero status codes are usually uppercase
            "LineAmountTypes": journal_data.get("lineAmountTypes", "NoTax"), # Consistent with common practice for these journals
            "ShowOnCashBasisReports": journal_data.get("showOnCashBasisReports", True),
            "JournalLines": xero_journal_lines
        }

        # Remove None date if parsing failed and we didn't return error earlier
        if payload["Date"] is None and journal_date_iso is not None:
             self.logger.error(f"Journal date parsing failed for {journal_date_iso}, cannot create journal without a date.")
             return {"error": "MissingDate", "message":"Journal date is required and couldn't be parsed."}


        self.logger.debug(f"Creating manual journal with payload: {json.dumps(payload, indent=2)}")

        try:
            response_json = await self._httpx_request(
                method="POST", # Use POST to create new journals
                endpoint=endpoint,
                body_data={"ManualJournals": [payload]} # Xero typically expects an array for create/update
            )
            self.logger.info(f"Successfully created manual journal. Response: {response_json}")
            return response_json
        except httpx.HTTPStatusError as e:
            response_text = "No response text"
            try:
                response_text = e.response.text
            except Exception:
                pass # Keep default
            self.logger.error(
                f"HTTP error creating manual journal: {e.response.status_code} - {response_text}",
                exc_info=True,
            )
            try:
                return e.response.json()
            except json.JSONDecodeError:
                return {"error": "HTTPStatusError", "status_code": e.response.status_code, "text": response_text}
        except Exception as e:
            self.logger.error(f"Unexpected error creating manual journal: {e}", exc_info=True)
            return {"error": "Exception", "message": str(e)}

    async def exchange_auth_code_for_tokens(self, auth_code: str, redirect_uri: str) -> Dict[str, Any]:
        """
        Exchanges an authorization code for access, refresh, and ID tokens.
        Corresponds to Step 3 of the Xero OAuth 2.0 documentation.

        Args:
            auth_code: The authorization code received from Xero callback.
            redirect_uri: The redirect URI that was used in the initial auth request.
                         This MUST match the one registered with the Xero app.

        Returns:
            A dictionary containing the token response from Xero (e.g., access_token, refresh_token, expires_in, id_token).

        Raises:
            XeroApiException: If the token exchange fails.
        """
        if not self.client_id or not self.client_secret:
            self.logger.error("Client ID or Client Secret is not configured for token exchange.")
            raise XeroApiException("Xero client credentials (client_id/client_secret) are not configured.")

        token_url = "https://identity.xero.com/connect/token"

        # Prepare Authorization header: Basic base64(client_id:client_secret)
        auth_string = f"{self.client_id}:{self.client_secret}"
        auth_header_value = "Basic " + base64.b64encode(auth_string.encode('utf-8')).decode('utf-8')

        headers = {
            "Authorization": auth_header_value,
            "Content-Type": "application/x-www-form-urlencoded"
        }

        payload = {
            "grant_type": "authorization_code",
            "code": auth_code,
            "redirect_uri": redirect_uri
        }

        self.logger.info(f"Exchanging authorization code for tokens for app_tenant_id: {self.app_tenant_id}, using redirect_uri: {redirect_uri}")

        try:
            # Use shorter timeout for token exchange
            token_timeout = min(self.timeout, 8.0)  # Max 8 seconds for token exchange
            async with httpx.AsyncClient(timeout=token_timeout) as client:
                response = await client.post(token_url, headers=headers, data=payload)
                response.raise_for_status() # Raises HTTPStatusError for 4xx/5xx responses
                token_data = response.json()
                self.logger.info("Successfully exchanged auth code for tokens.")
                # Log some non-sensitive parts of the token data for confirmation
                self.logger.debug(f"Received token data - Access token present: {'access_token' in token_data}, Refresh token present: {'refresh_token' in token_data}, Expires in: {token_data.get('expires_in')}")
                return token_data
        except httpx.HTTPStatusError as e_http:
            self.logger.error(f"HTTP error during token exchange: {e_http.response.status_code} - {e_http.response.text}", exc_info=True)
            error_detail = "Failed to exchange authorization code for tokens."
            try:
                error_body = e_http.response.json()
                if isinstance(error_body, dict) and 'error' in error_body:
                    error_detail += f" Xero error: {error_body['error']}"
                    if 'error_description' in error_body:
                         error_detail += f" - {error_body['error_description']}"
            except ValueError: # If response is not JSON
                error_detail += f" Raw response: {e_http.response.text[:200]}" # Log snippet of raw response
            raise XeroApiException(error_detail, status_code=e_http.response.status_code, response_data=e_http.response.text)
        except Exception as e:
            self.logger.error(f"Unexpected error during token exchange: {e}", exc_info=True)
            raise XeroApiException(f"An unexpected error occurred during token exchange: {str(e)}")

    async def get_xero_connections(self, access_token: str) -> List[Dict[str, Any]]:
        """
        Fetches the list of Xero connections (tenants) available to the authenticated user
        using a provided access token.
        Step 5 in Xero OAuth docs: Call https://api.xero.com/connections
        This method does NOT use the instance's stored access token or platform_org_id for the call itself,
        as it's designed to be called with a fresh token immediately after exchange, before a specific
        tenant connection is established for storage.
        """
        connections_url = "https://api.xero.com/connections"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/json"
        }

        self.logger.info(f"Fetching Xero connections from {connections_url} using the provided access token (app_tenant_id context: {self.app_tenant_id}).")

        try:
            # Note: This is a direct httpx call, not using self._httpx_request,
            # because self._httpx_request is designed for calls *after* a Xero-Tenant-Id is established
            # and uses self.get_current_access_token(). Here, we are using a fresh token
            # specifically for the connections endpoint, which doesn't require a Xero-Tenant-Id header.
            # Use a shorter timeout (5 seconds) for the connections endpoint to prevent hanging
            connection_timeout = httpx.Timeout(5.0)
            async with httpx.AsyncClient(timeout=connection_timeout) as client:
                self.logger.info(f"Making request to Xero connections endpoint with 5 second timeout...")
                response = await client.get(connections_url, headers=headers)
                response.raise_for_status() # Raises HTTPStatusError for 4xx/5xx responses
                connections_data = response.json()
                self.logger.info(f"Successfully fetched {len(connections_data)} Xero connection(s).")
                if connections_data and isinstance(connections_data, list):
                    for conn in connections_data:
                        self.logger.debug(f"Connection found: TenantId={conn.get('tenantId')}, Name={conn.get('tenantName')}, Type={conn.get('tenantType')}, AuthEventId={conn.get('authEventId')}")
                return connections_data if isinstance(connections_data, list) else []
        except httpx.TimeoutException as e_timeout:
            self.logger.error(f"Timeout error fetching Xero connections (5 second limit exceeded): {e_timeout}", exc_info=True)
            raise XeroApiException("Timeout while fetching Xero connections. The Xero API may be experiencing delays.", status_code=408)
        except httpx.HTTPStatusError as e_http:
            self.logger.error(f"HTTP error fetching Xero connections: {e_http.response.status_code} - {e_http.response.text}", exc_info=True)
            raise XeroApiException("Failed to fetch Xero connections.", status_code=e_http.response.status_code, response_data=e_http.response.text)
        except Exception as e:
            self.logger.error(f"Unexpected error fetching Xero connections: {e}", exc_info=True)
            raise XeroApiException(f"An unexpected error occurred while fetching Xero connections: {str(e)}")

    async def store_raw_tokens_and_details(
        self,
        access_token: str,
        refresh_token: str,
        expires_in_seconds: int,
        id_token: Optional[str] = None
    ) -> bool:
        self.logger.info(f"Storing raw tokens for platform_org_id='{self.platform_org_id}', app_tenant_id='{self.app_tenant_id}'.")
        if not access_token or not refresh_token or expires_in_seconds is None:
            self.logger.error("Attempted to store incomplete raw tokens (missing access_token, refresh_token, or expires_in_seconds).")
            return False

        self.access_token = access_token
        self.refresh_token = refresh_token
        # Calculate and store token_expires_at
        aware_now = datetime.now(timezone.utc)
        expires_at_dt = aware_now + timedelta(seconds=expires_in_seconds)
        self.token_expires_at = expires_at_dt

        # Log the ID token if present, but it's not typically stored long-term in the same way as access/refresh tokens
        if id_token:
            self.id_token = id_token # Store if available
            self.logger.debug(f"ID token received (length: {len(id_token)}). Will not be directly stored by _save_tokens_to_secret unless added to its payload.")

        self.logger.info(f"Raw tokens populated for Xero Tenant ID {self.platform_org_id}. Access token expires at: {self.token_expires_at}")

        await self._save_tokens_to_secret() # Let this handle the actual saving
        return True

    async def save_raw_tokens_to_firestore(self, token_data: dict):
        """
        Populates client instance token attributes from raw token data and saves them to Firestore.
        This is typically called after the OAuth callback when the specific Xero Tenant ID (platform_org_id)
        is known, and the client has been instantiated with that platform_org_id.

        Args:
            token_data: A dictionary containing 'access_token', 'refresh_token', 'expires_in',
                        and optionally 'id_token', as received from Xero.
        """
        self.logger.info(f"Preparing to save raw tokens to Firestore for platform_org_id='{self.platform_org_id}'")

        access_token = token_data.get("access_token")
        refresh_token = token_data.get("refresh_token")
        expires_in = token_data.get("expires_in") # This is 'expires_in' (seconds), not 'expires_at'
        id_token = token_data.get("id_token")

        if not all([access_token, refresh_token, expires_in is not None]):
            self.logger.error("Cannot save raw tokens: 'access_token', 'refresh_token', or 'expires_in' missing from token_data.")
            raise ValueError("Incomplete token data provided for saving.")

        # Populate the instance attributes
        self.access_token = access_token
        self.refresh_token = refresh_token
        self.token_expires_at = datetime.now(timezone.utc) + timedelta(seconds=int(expires_in))
        if id_token:
            self.id_token = id_token

        self.logger.info(f"Client token attributes populated. Access token expires at: {self.token_expires_at}. Now calling _save_tokens_to_secret.")

        # Now that instance attributes are set, call the existing save method
        await self._save_tokens_to_secret()

    async def revoke_tokens(self) -> bool:
        """
        Revokes the current refresh token with Xero and removes the tokens from Firestore.
        This effectively disconnects the Xero connection.

        Returns:
            bool: True if the revocation was successful, False otherwise.
        """
        if not self.refresh_token:
            self.logger.warning(f"No refresh token available to revoke for Xero Tenant ID {self.platform_org_id}.")
            return False

        if not self.client_id or not self.client_secret:
            self.logger.error("Client ID or Client Secret not configured for Xero token revocation.")
            return False

        # Xero's token revocation endpoint
        revocation_url = "https://identity.xero.com/connect/revocation"

        # Prepare Authorization header: Basic base64(client_id:client_secret)
        auth_string = f"{self.client_id}:{self.client_secret}"
        auth_header_value = "Basic " + base64.b64encode(auth_string.encode('utf-8')).decode('utf-8')

        headers = {
            "Authorization": auth_header_value,
            "Content-Type": "application/x-www-form-urlencoded"
        }

        payload = {
            "token": self.refresh_token,
            "token_type_hint": "refresh_token"
        }

        self.logger.info(f"Attempting to revoke Xero refresh token for Tenant ID: {self.platform_org_id}")

        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(revocation_url, headers=headers, data=payload)

                # Per OAuth 2.0 spec, the revocation endpoint should return 200 OK for successful revocation
                # or if the token was invalid (to prevent token enumeration attacks)
                if response.status_code == 200:
                    self.logger.info(f"Successfully revoked Xero refresh token for Tenant ID: {self.platform_org_id}")

                    # Clear tokens from instance
                    self.access_token = None
                    self.refresh_token = None
                    self.token_expires_at = None
                    self.id_token = None

                    # Delete the tokens from Firestore
                    try:
                        await self.token_storage.delete_tokens(self.app_tenant_id, self.platform_org_id)
                        self.logger.info(f"Deleted tokens from Firestore for {self.app_tenant_id}/{self.platform_org_id}")
                    except Exception as e:
                        self.logger.error(f"Failed to delete tokens from Firestore: {e}", exc_info=True)
                        # Continue even if token deletion fails, as the token has been revoked with Xero

                    return True
                else:
                    self.logger.error(f"Failed to revoke Xero token: {response.status_code} - {response.text}")
                    return False
        except Exception as e:
            self.logger.error(f"Error revoking Xero token: {e}", exc_info=True)
            return False

    async def check_connection_status(self) -> Dict[str, Any]:
        """
        Checks the status of the Xero connection by attempting to make a simple API call.

        Returns:
            Dict with connection status information:
            {
                "status": "active" | "disconnected" | "error",
                "message": "Detailed status message",
                "last_token_refresh": ISO datetime string or None,
                "token_expires_at": ISO datetime string or None,
                "organisation_name": "Name of the Xero organisation" or None
            }
        """
        result = {
            "status": "unknown",
            "message": "",
            "last_token_refresh": None,
            "token_expires_at": None,
            "organisation_name": None
        }

        # Check if we have tokens
        if not self.access_token or not self.refresh_token:
            result["status"] = "disconnected"
            result["message"] = "No access or refresh token available"
            return result

        # Check token expiry
        if self.token_expires_at:
            result["token_expires_at"] = self.token_expires_at.isoformat()

        try:
            # Try to get organisation details as a simple API test
            org_details = await self.get_organisation_details()

            if org_details and "Name" in org_details:
                result["status"] = "active"
                result["message"] = "Connection is active"
                result["organisation_name"] = org_details["Name"]
            else:
                result["status"] = "error"
                result["message"] = "Could not retrieve organisation details"

            return result
        except Exception as e:
            error_message = str(e)

            # Check for specific error types that indicate disconnection
            if "401" in error_message or "unauthorized" in error_message.lower():
                result["status"] = "disconnected"
                result["message"] = "Authentication failed - connection may be revoked or expired"
            else:
                result["status"] = "error"
                result["message"] = f"Error checking connection: {error_message}"

            return result

    async def get_authorization_url(self, state: str) -> str:
        """
        Generates the Xero OAuth 2.0 Authorization URL.

        Args:
            state: An opaque value used to maintain state between the request and callback.
                   This will be our internal client_id.

        Returns:
            The fully constructed Xero authorization URL.
        """
        if not self.client_id or not self.redirect_uri:
            self.logger.error("Cannot generate authorization URL: XERO_CLIENT_ID or XERO_REDIRECT_URI is not configured.")
            raise ValueError("Client ID or Redirect URI is not configured for Xero authorization.")

        auth_base_url = "https://login.xero.com/identity/connect/authorize"
        params = {
            "response_type": "code",
            "client_id": self.client_id,
            "redirect_uri": self.redirect_uri,
            "scope": os.getenv("XERO_SCOPES", "accounting.transactions accounting.settings offline_access openid profile email accounting.contacts.read accounting.attachments"), # Ensure all needed scopes are here
            "state": state
        }
        # Use urllib.parse for standard URL encoding
        import urllib.parse
        query_string = urllib.parse.urlencode(params)
        url = f"{auth_base_url}?{query_string}"
        self.logger.info(f"Generated Xero Authorization URL: {url}")
        return url

    async def get_attachments(self, transaction_id: str, transaction_type: str = "Invoices") -> List[Dict[str, Any]]:
        """
        Gets attachments for a specific transaction (Invoice, Bill, etc.)
        
        Args:
            transaction_id: The Xero transaction ID
            transaction_type: The transaction type (Invoices, Bills, BankTransactions, etc.)
        
        Returns:
            List of attachment metadata dictionaries
        """
        try:
            self.logger.info(f"Fetching attachments for {transaction_type}/{transaction_id}")
            
            # Xero API endpoint for attachments
            url = f"{self.api_url}/{transaction_type}/{transaction_id}/Attachments"
            
            response = await self._httpx_request("GET", url)
            
            if response.status_code == 200:
                data = response.json()
                attachments = data.get("Attachments", [])
                self.logger.info(f"Found {len(attachments)} attachments for {transaction_type}/{transaction_id}")
                return attachments
            elif response.status_code == 404:
                self.logger.info(f"No attachments found for {transaction_type}/{transaction_id}")
                return []
            else:
                self.logger.warning(f"Unexpected response getting attachments for {transaction_type}/{transaction_id}: {response.status_code}")
                return []
                
        except Exception as e:
            self.logger.error(f"Error fetching attachments for {transaction_type}/{transaction_id}: {e}")
            return []

    async def download_attachment(self, transaction_id: str, attachment_id: str, transaction_type: str = "Invoices") -> Optional[bytes]:
        """
        Downloads attachment content for a specific transaction and attachment
        
        Args:
            transaction_id: The Xero transaction ID
            attachment_id: The Xero attachment ID
            transaction_type: The transaction type (Invoices, Bills, BankTransactions, etc.)
        
        Returns:
            Attachment content as bytes, or None if failed
        """
        try:
            self.logger.info(f"Downloading attachment {attachment_id} for {transaction_type}/{transaction_id}")
            
            # Xero API endpoint for downloading attachment content
            url = f"{self.api_url}/{transaction_type}/{transaction_id}/Attachments/{attachment_id}"
            
            response = await self._httpx_request("GET", url, is_attachment=True)
            
            if response.status_code == 200:
                content = response.content
                self.logger.info(f"Successfully downloaded attachment {attachment_id} ({len(content)} bytes)")
                return content
            else:
                self.logger.warning(f"Failed to download attachment {attachment_id}: HTTP {response.status_code}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error downloading attachment {attachment_id} for {transaction_type}/{transaction_id}: {e}")
            return None