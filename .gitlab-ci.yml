stages:
  - build
  - test
  - deploy

variables:
  NODE_VERSION: "18"
  FIREBASE_HOSTING_URL: "https://drcr-d660a.web.app"

# Cache dependencies between jobs
cache:
  paths:
    - node_modules/

build:
  stage: build
  image: node:${NODE_VERSION}
  script:   
    # Install dependencies and build
    - echo "Installing dependencies..."
    - npm ci
    - echo "Building application..."
    - npm run build
    
    # Show build stats
    - echo "Build completed! Bundle analysis:"
    - find dist -type f -exec du -h {} \; | sort -hr | head -n 10
  artifacts:
    paths:
      - dist/
      - firebase.json
      - .firebaserc
    expire_in: 1 week
  only:
    - main

lint:
  stage: test
  image: node:${NODE_VERSION}
  script:
    - npm ci
    - npm run lint --format=json --output-file=eslint-report.json || true
  artifacts:
    paths:
      - eslint-report.json
    reports:
      codequality: eslint-report.json
    when: always
    expire_in: 1 week
  allow_failure: true
  only:
    - main

deploy_production:
  stage: deploy
  image: node:${NODE_VERSION}
  dependencies:
    - build
  script:
    - chmod +x ./ci/deploy-firebase.sh
    - ./ci/deploy-firebase.sh
  environment:
    name: production
    url: ${FIREBASE_HOSTING_URL}
  only:
    - main
