#!/bin/sh
# Script for importing frontend project

echo "Current directory: $(pwd)"
echo "Listing root directory:"
ls -la

# Создаем директорию drcr_front в родительской папке
echo "Creating drcr_front directory in parent folder..."
mkdir -p ../drcr_front

# Клонируем репозиторий с фронтендом
echo "Cloning frontend repository..."
git clone https://oauth2:${GITLAB_TOKEN}@gitlab.com/drcrlabs/drcr_front.git ../drcr_front || {
  echo "Failed to clone using GITLAB_TOKEN, trying without authentication..."
  git clone https://gitlab.com/drcrlabs/drcr_front.git ../drcr_front || {
    echo "Failed to clone repository. Creating basic structure..."
    
    # Создаем базовую структуру проекта
    mkdir -p ../drcr_front/src
    mkdir -p ../drcr_front/public
    
    # Создаем package.json
    echo '{
      "name": "drcr_front",
      "private": true,
      "version": "0.0.0",
      "type": "module",
      "scripts": {
        "dev": "vite",
        "build": "tsc -b && vite build",
        "lint": "eslint .",
        "preview": "vite preview"
      },
      "dependencies": {
        "react": "^19.1.0",
        "react-dom": "^19.1.0"
      },
      "devDependencies": {
        "@types/react": "^18.2.15",
        "@types/react-dom": "^18.2.7",
        "@typescript-eslint/eslint-plugin": "^6.0.0",
        "@typescript-eslint/parser": "^6.0.0",
        "@vitejs/plugin-react": "^4.0.3",
        "eslint": "^8.45.0",
        "eslint-plugin-react-hooks": "^4.6.0",
        "eslint-plugin-react-refresh": "^0.4.3",
        "typescript": "^5.0.2",
        "vite": "^4.4.5"
      }
    }' > ../drcr_front/package.json
    
    # Создаем index.html
    echo '<!doctype html>
    <html lang="en">
      <head>
        <meta charset="UTF-8" />
        <link rel="icon" type="image/svg+xml" href="/vite.svg" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>DRCR Frontend</title>
      </head>
      <body>
        <div id="root"></div>
        <script type="module" src="/src/main.tsx"></script>
      </body>
    </html>' > ../drcr_front/index.html
    
    # Создаем vite.config.ts
    echo 'import { defineConfig } from "vite";
    import react from "@vitejs/plugin-react";
    import path from "path";

    export default defineConfig({
      plugins: [react()],
      resolve: {
        alias: {
          "@": path.resolve(__dirname, "./src"),
        },
      },
    });' > ../drcr_front/vite.config.ts
    
    # Создаем firebase.json
    echo '{
      "hosting": {
        "public": "dist",
        "ignore": [
          "firebase.json",
          "**/.*",
          "**/node_modules/**"
        ],
        "rewrites": [
          {
            "source": "**",
            "destination": "/index.html"
          }
        ]
      }
    }' > ../drcr_front/firebase.json
    
    # Создаем .firebaserc
    echo '{
      "projects": {
        "default": "drcr-d660a"
      }
    }' > ../drcr_front/.firebaserc
    
    # Создаем базовый React компонент
    mkdir -p ../drcr_front/src
    echo 'import { StrictMode } from "react";
    import { createRoot } from "react-dom/client";
    import App from "./App";

    createRoot(document.getElementById("root")!).render(
      <StrictMode>
        <App />
      </StrictMode>
    );' > ../drcr_front/src/main.tsx
    
    echo 'function App() {
      return (
        <div className="App">
          <header className="App-header">
            <h1>DRCR Frontend</h1>
            <p>Welcome to the DRCR Frontend application</p>
          </header>
        </div>
      );
    }

    export default App;' > ../drcr_front/src/App.tsx
  }
}

echo "Listing parent directory:"
ls -la ..

echo "Listing drcr_front directory:"
ls -la ../drcr_front

echo "✅ Frontend project imported successfully!"