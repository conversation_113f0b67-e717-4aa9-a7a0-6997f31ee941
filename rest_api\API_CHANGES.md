# API Changes: Consolidating Invoice Endpoints

## Overview

The DRCR API has been updated to consolidate the `/invoices` endpoints into the `/transactions` endpoints. This change simplifies the API and reduces redundancy, as invoices are simply a specific type of transaction (ACCPAY_INVOICE).

## Changes Made

1. **Removed `/invoices` Endpoints**:
   - Removed the `/invoices/` endpoint
   - Removed the `/invoices/{transaction_id}` endpoint

2. **Enhanced `/transactions` Endpoints**:
   - Added a new `/transactions/` endpoint with pagination and filtering
   - Added `transaction_type` parameter to filter by transaction type
   - Enhanced the `/transactions/{transaction_id}` endpoint to include related data

## How to Access Invoices

To access invoices specifically, use the `/transactions` endpoints with the `transaction_type` parameter set to `ACCPAY_INVOICE`.

### Examples

#### List Invoices

```
GET /transactions/?client_id=123&transaction_type=ACCPAY_INVOICE
```

This will return all ACCPAY invoices for the specified client.

#### Get Invoice Details

```
GET /transactions/456?include_related=true
```

This will return the details of the transaction with ID 456, including related schedules and attachments.

## Benefits

1. **Simplified API**: Fewer endpoints to maintain and document
2. **Consistent Interface**: All transaction types are accessed through the same endpoints
3. **Flexible Filtering**: The `transaction_type` parameter allows for filtering by any transaction type
4. **Reduced Code Duplication**: Eliminates duplicate code between the invoice and transaction endpoints

## Migration Guide

If you were using the `/invoices` endpoints, update your code as follows:

| Old Endpoint | New Endpoint |
|--------------|--------------|
| `GET /invoices/?client_id=123` | `GET /transactions/?client_id=123&transaction_type=ACCPAY_INVOICE` |
| `GET /invoices/456` | `GET /transactions/456?include_related=true` |
