import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loader2, Building2, CheckCircle, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';
import { ApiClient } from '@/lib/api';

const apiClient = new ApiClient();

interface XeroOrganization {
  tenant_id: string;
  tenant_name: string;
  is_already_connected: boolean;
  connection_type: string;
}

interface OrganizationData {
  client_id: string;
  organizations: XeroOrganization[];
  reconnections_processed?: number;
  message?: string;
}

export function XeroOrganizationSelector() {
  const { clientId } = useParams<{ clientId: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [connecting, setConnecting] = useState<string | null>(null);
  const [organizationData, setOrganizationData] = useState<OrganizationData | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (clientId) {
      loadAvailableOrganizations();
    }
  }, [clientId]);

  const loadAvailableOrganizations = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await apiClient.getAvailableXeroOrganizations(clientId!);
      setOrganizationData(data);
    } catch (error: any) {
      console.error('Failed to load available organizations:', error);
      if (error.response?.status === 404) {
        setError('No pending OAuth session found. Please restart the connection process.');
      } else {
        setError('Failed to load available organizations. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleConnectOrganization = async (tenantId: string, tenantName: string) => {
    try {
      setConnecting(tenantId);
      console.log(`XeroOrganizationSelector: Connecting organization ${tenantName} (${tenantId}) for client ${clientId}`);

      const result = await apiClient.connectSelectedXeroOrganization(clientId!, tenantId);
      console.log('XeroOrganizationSelector: Connection successful:', result);

      toast.success(`Successfully connected to ${tenantName}`);

      // Redirect to the entity configuration page
      navigate(`/clients/${clientId}/xero/configure?entities=${result.entity.entity_id}`);
    } catch (error: any) {
      console.error('XeroOrganizationSelector: Failed to connect organization:', error);
      console.error('XeroOrganizationSelector: Error type:', typeof error);
      console.error('XeroOrganizationSelector: Error constructor:', error.constructor.name);

      if (error && typeof error === 'object') {
        console.error('XeroOrganizationSelector: Error details:', {
          message: error.message,
          name: error.name,
          code: error.code,
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          headers: error.response?.headers,
          config: error.config ? {
            url: error.config.url,
            method: error.config.method,
            baseURL: error.config.baseURL
          } : undefined
        });
      }

      if (error.response?.status === 410) {
        toast.error('OAuth session expired. Please restart the connection process.');
        navigate(`/clients/${clientId}/entities`);
      } else if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
        toast.error('Connection is taking longer than expected. Please wait and try again if it fails.');
      } else {
        toast.error('Failed to connect to organization. Please try again.');
      }
    } finally {
      setConnecting(null);
    }
  };

  const handleRestartConnection = () => {
    navigate(`/clients/${clientId}/entities`);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Loading available organizations...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-destructive" />
              Connection Error
            </CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={handleRestartConnection}>
              Restart Connection Process
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!organizationData || (organizationData.organizations.length === 0 && !organizationData.message)) {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <Card>
          <CardHeader>
            <CardTitle>No New Organizations Found</CardTitle>
            <CardDescription>
              No new Xero organizations were found for your account to connect.
              If you reconnected an existing organization, that process completed automatically.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={handleRestartConnection}>
              Go Back to Entities
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Select Xero Organization</h1>
        <p className="text-muted-foreground">
          Choose which Xero organization you want to connect to this client.
        </p>
      </div>

      {/* Show reconnection message if any organizations were automatically reconnected */}
      {organizationData?.message && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            <p className="text-green-800 font-medium">
              {organizationData.message}
            </p>
          </div>
        </div>
      )}

      {/* Only show the organization list if there are organizations to select */}
      {organizationData.organizations.length > 0 && (
        <div className="grid gap-4">
          {organizationData.organizations.map((org) => (
            <Card key={org.tenant_id} className="relative">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Building2 className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <CardTitle className="text-lg">{org.tenant_name}</CardTitle>
                      <CardDescription>
                        {org.connection_type} • ID: {org.tenant_id.substring(0, 8)}...
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {org.is_already_connected && (
                      <Badge variant="secondary" className="flex items-center gap-1">
                        <CheckCircle className="h-3 w-3" />
                        Already Connected
                      </Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between items-center">
                  <p className="text-sm text-muted-foreground">
                    {org.is_already_connected
                      ? 'This organization is already connected to this client.'
                      : 'Click to connect this organization to your client.'}
                  </p>
                  <Button
                    onClick={() => handleConnectOrganization(org.tenant_id, org.tenant_name)}
                    disabled={org.is_already_connected || connecting === org.tenant_id}
                    variant={org.is_already_connected ? 'secondary' : 'default'}
                  >
                    {connecting === org.tenant_id ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        Connecting...
                      </>
                    ) : org.is_already_connected ? (
                      'Already Connected'
                    ) : (
                      'Connect Organization'
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      <div className="mt-6 text-center">
        <Button variant="outline" onClick={handleRestartConnection}>
          { (organizationData?.message && organizationData.organizations.length === 0) 
            ? "Continue to Client Entities" 
            : "Cancel and Go Back" }
        </Button>
      </div>
    </div>
  );
}