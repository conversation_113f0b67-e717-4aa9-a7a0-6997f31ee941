import { api } from '@/lib/api';
import type { Account } from '@/lib/api';
// Import enums that are used as values directly, and others as types
import {
  ConnectionStatus, // Used as a value
  EntityType,     // Potentially used for casting/validation if not just type
  EntityStatus    // Potentially used for casting/validation if not just type
} from '@/types/entity.types';
import type {
  EntitySummary,
  EntitySettings,
  EntityResponse,
  EntityCreate,
  EntityUpdate,
  EntityListResponse,
  EntityDashboardData,
  EntityHealthMetrics,
  ConnectionTestResult,
  SyncStatus,
  OAuthInitiateResponse,
  OAuthCallbackData,
  CompleteConnectionResponse,
  EntityFilters,
  EntityEnums,
  CreateEntityResponse,
  UpdateEntityResponse,
  DeleteEntityResponse,
  EntityWizardStep1,
  EntityWizardStep2,
  EntityWizardStep3
  // ConnectionStatus removed from here as it's now a direct import
} from '@/types/entity.types';

export interface EntityConnectionStatus {
  entity_id: string;
  entity_name: string;
  type: string;
  connection_status: {
    status: string;
    last_checked?: string;
    error_message?: string;
  };
}

// Define a type for the raw entity structure from the backend
interface RawBackendEntity {
  entity_id: string;
  entity_name: string;
  type: string; // Or specific EntityType if backend guarantees it
  status: string; // Or specific EntityStatus
  connection_details?: {
    status?: string;
    last_sync?: string;
    error_message?: string;
    // other fields if present
  };
  // Include other top-level fields the backend might send for EntitySummary
  pending_items_count?: number;
  error_count?: number;
  health_score?: number;
  // Any other fields like created_at, updated_at, xero_tenant_id etc. if needed elsewhere,
  // but EntitySummary doesn't strictly require them.
}

interface RawBackendResponse {
  client_id: string;
  entities: RawBackendEntity[];
  total: number;
  // Add pagination fields if your backend list endpoint for entities returns them
  // pagination?: { current_page: number; page_size: number; total_items: number; total_pages: number; };
}

export class EntitiesService {
  /**
   * Get entities for a specific client with enhanced filtering
   */
  static async getEntitiesForClient(clientId: string, filters?: EntityFilters): Promise<EntityListResponse> {
    try {
      const params = new URLSearchParams();
      // client_id is now in the path, no longer needed in params for this specific endpoint
      // params.append('client_id', clientId); 

      if (filters?.page) params.append('page', filters.page.toString());
      if (filters?.limit) params.append('limit', filters.limit.toString());
      if (filters?.type) params.append('type', filters.type);
      // Ensure backend for /clients/{clientId}/entities supports these or remove if not
      if (filters?.status) params.append('status', filters.status); 
      if (filters?.connection_status) params.append('connection_status', filters.connection_status);
      if (filters?.search) params.append('search', filters.search);
      if (filters?.sort_by) params.append('sort_by', filters.sort_by);
      if (filters?.sort_order) params.append('sort_order', filters.sort_order);

      const queryString = params.toString();
      const endpoint = `/clients/${clientId}/entities${queryString ? '?' + queryString : ''}`;
      
      // Expecting the backend to return something like RawBackendResponse
      const response = await api.get<RawBackendResponse>(endpoint);
      
      const rawEntities = response.entities || [];

      const transformedEntities: EntitySummary[] = rawEntities.map((rawEntity: RawBackendEntity) => {
        let mappedConnectionStatus: ConnectionStatus = ConnectionStatus.DISCONNECTED; // Default
        const backendConnStatus = rawEntity.connection_details?.status;

        if (backendConnStatus) {
          if (backendConnStatus.toLowerCase() === 'active' || backendConnStatus.toLowerCase() === 'connected') {
            mappedConnectionStatus = ConnectionStatus.CONNECTED;
          } else if (backendConnStatus.toLowerCase() === 'disconnected') {
            mappedConnectionStatus = ConnectionStatus.DISCONNECTED;
          } else if (backendConnStatus.toLowerCase() === 'error') {
            mappedConnectionStatus = ConnectionStatus.ERROR;
          } else if (backendConnStatus.toLowerCase() === 'pending') {
            mappedConnectionStatus = ConnectionStatus.PENDING;
          } else if (backendConnStatus.toLowerCase() === 'expired') {
            mappedConnectionStatus = ConnectionStatus.EXPIRED;
          } else if (backendConnStatus.toLowerCase() === 'syncing') {
            mappedConnectionStatus = ConnectionStatus.SYNCING;
          }
          // Add more explicit mappings if backend uses other terms
        }
        
        return {
          entity_id: rawEntity.entity_id,
          entity_name: rawEntity.entity_name,
          // Assuming rawEntity.type and rawEntity.status are compatible with EntityType and EntityStatus enums
          // Add casting or mapping if necessary, e.g., rawEntity.type as EntityType
          type: rawEntity.type as any, // Cast to any or perform proper mapping to EntityType
          status: rawEntity.status as any, // Cast to any or perform proper mapping to EntityStatus
          connection_status: mappedConnectionStatus,
          last_sync: rawEntity.connection_details?.last_sync,
          error_message: rawEntity.connection_details?.error_message,
          pending_items_count: rawEntity.pending_items_count,
          error_count: rawEntity.error_count,
          health_score: rawEntity.health_score,
        };
      });
      
      // Assuming the backend for /clients/{client_id}/entities might not have pagination
      // matching EntityListResponse. Adjust as needed.
      // If the /clients/{clientId}/entities endpoint doesn't return pagination, construct it or adapt.
      return {
        entities: transformedEntities,
        // pagination: response.pagination // Or handle if backend's pagination structure differs
        // For now, if /clients/{clientId}/entities doesn't send pagination, this will be undefined.
        // The component might need to handle undefined pagination.
        // Or, if total is sent:
         pagination: response.total !== undefined ? {
            current_page: filters?.page || 1,
            page_size: filters?.limit || rawEntities.length, // Or a default
            total_items: response.total,
            total_pages: filters?.limit ? Math.ceil(response.total / filters.limit) : 1,
         } : undefined,
      };

    } catch (error) {
      console.error('Error fetching entities for client:', error);
      throw new Error('Failed to fetch entities');
    }
  }

  /**
   * Get entity details including settings and health metrics
   */
  static async getEntity(entityId: string): Promise<EntityResponse> {
    try {
      return await api.get<EntityResponse>(`/entities/${entityId}`);
    } catch (error) {
      console.error('Error fetching entity details:', error);
      throw new Error('Failed to fetch entity details');
    }
  }

  /**
   * Create a new entity using wizard data
   */
  static async createEntityFromWizard(
    step1: EntityWizardStep1,
    step2: EntityWizardStep2,
    step3: EntityWizardStep3,
    clientId: string
  ): Promise<string> {
    try {
      const entityData: EntityCreate = {
        client_id: clientId,
        entity_name: step1.entity_name,
        type: step1.type,
        description: step1.description,
        settings: {
          ...step3.settings,
          auto_sync_enabled: step3.auto_sync_enabled,
          sync_frequency: step3.sync_frequency
        }
      };

      const response = await api.post<CreateEntityResponse>('/entities', entityData);

      // If OAuth connection is required, initiate it
      if (step2.connection_method === 'oauth' && step2.oauth_provider) {
        await this.initiateOAuthConnection(response.entity_id, step2.oauth_provider);
      }

      return response.entity_id;
    } catch (error) {
      console.error('Error creating entity:', error);
      throw new Error('Failed to create entity');
    }
  }

  /**
   * Update entity details
   */
  static async updateEntity(entityId: string, updates: EntityUpdate): Promise<void> {
    try {
      await api.put<UpdateEntityResponse>(`/entities/${entityId}`, updates);
    } catch (error) {
      console.error('Error updating entity:', error);
      throw new Error('Failed to update entity');
    }
  }

  /**
   * Delete an entity
   */
  static async deleteEntity(entityId: string): Promise<void> {
    try {
      await api.delete<DeleteEntityResponse>(`/entities/${entityId}`);
    } catch (error) {
      console.error('Error deleting entity:', error);
      throw new Error('Failed to delete entity');
    }
  }

  /**
   * Update entity settings
   */
  static async updateEntitySettings(
    entityId: string,
    settings: {
      prepayment_asset_account_codes?: string[];
      excluded_pnl_account_codes?: string[];
      default_amortization_months?: number;
      entity_name?: string;
      auto_sync_enabled?: boolean;
      sync_frequency?: 'hourly' | 'daily' | 'weekly' | 'manual';
      sync_spend_money?: boolean;
      // Transaction sync settings
      transaction_sync_start_date?: string;
      // Individual sync data type toggles
      sync_invoices?: boolean;
      sync_bills?: boolean;
      sync_payments?: boolean;
      sync_bank_transactions?: boolean;
      sync_journal_entries?: boolean;
      // Additional settings
      auto_post_proposed_journals?: boolean;
      base_currency_code?: string;
      initial_sync_completed?: boolean;
      last_full_sync_date?: string;
      notification_preferences?: {
        sync_errors: boolean;
        connection_issues: boolean;
        data_anomalies: boolean;
      };
    }
  ): Promise<void> {
    try {
      await api.put(`/entities/${entityId}/settings`, settings);
    } catch (error) {
      console.error('Error updating entity settings:', error);
      throw new Error('Failed to update entity settings');
    }
  }

  /**
   * Get entity dashboard data with health metrics
   */
  static async getEntityDashboard(entityId: string): Promise<EntityDashboardData> {
    try {
      return await api.get<EntityDashboardData>(`/entities/${entityId}/dashboard`);
    } catch (error) {
      console.error('Error fetching entity dashboard:', error);
      throw new Error('Failed to fetch entity dashboard');
    }
  }

  /**
   * Get entity health metrics
   */
  static async getEntityHealthMetrics(entityId: string): Promise<EntityHealthMetrics> {
    try {
      return await api.get<EntityHealthMetrics>(`/entities/${entityId}/health`);
    } catch (error) {
      console.error('Error fetching entity health metrics:', error);
      throw new Error('Failed to fetch entity health metrics');
    }
  }

  /**
   * Check entity connection status
   */
  static async checkConnectionStatus(entityId: string): Promise<EntityConnectionStatus> {
    try {
      return await api.get<EntityConnectionStatus>(`/entities/${entityId}/connection/status`);
    } catch (error) {
      console.error('Error checking connection status:', error);
      throw new Error('Failed to check connection status');
    }
  }

  /**
   * Test entity connection
   */
  static async testConnection(entityId: string): Promise<ConnectionTestResult> {
    try {
      return await api.post<ConnectionTestResult>(`/entities/${entityId}/connection/test`);
    } catch (error) {
      console.error('Error testing connection:', error);
      throw new Error('Failed to test connection');
    }
  }

  /**
   * Get sync status for an entity
   */
  static async getSyncStatus(entityId: string): Promise<SyncStatus> {
    try {
      return await api.get<SyncStatus>(`/entities/${entityId}/sync/status`);
    } catch (error) {
      console.error('Error fetching sync status:', error);
      throw new Error('Failed to fetch sync status');
    }
  }

  /**
   * Trigger manual sync for an entity
   */
  static async triggerSync(entityId: string): Promise<void> {
    try {
      await api.post(`/entities/${entityId}/sync/trigger`);
    } catch (error) {
      console.error('Error triggering sync:', error);
      throw new Error('Failed to trigger sync');
    }
  }

  /**
   * Disconnect an entity
   */
  static async disconnectEntity(entityId: string): Promise<{ message: string }> {
    try {
      console.log(`EntitiesService: Disconnecting entity ${entityId}`);
      const result = await api.post<{ message: string }>(`/entities/${entityId}/connection/disconnect`);
      console.log('EntitiesService: Disconnect successful:', result);
      return result;
    } catch (error) {
      console.error('EntitiesService: Error disconnecting entity:', error);

      // Re-throw with more context
      if (error instanceof Error) {
        throw new Error(`Failed to disconnect entity: ${error.message}`);
      }
      throw new Error('Failed to disconnect entity: Unknown error');
    }
  }

  /**
   * Initiate OAuth connection for an entity
   */
  static async initiateOAuthConnection(entityId: string, provider: 'xero' | 'qbo'): Promise<OAuthInitiateResponse> {
    try {
      return await api.post<OAuthInitiateResponse>(`/entities/${entityId}/oauth/initiate`, { provider });
    } catch (error) {
      console.error('Error initiating OAuth connection:', error);
      throw new Error('Failed to initiate OAuth connection');
    }
  }

  /**
   * Complete OAuth connection with callback data
   */
  static async completeOAuthConnection(entityId: string, callbackData: OAuthCallbackData): Promise<CompleteConnectionResponse> {
    try {
      return await api.post<CompleteConnectionResponse>(`/entities/${entityId}/oauth/callback`, callbackData);
    } catch (error) {
      console.error('Error completing OAuth connection:', error);
      throw new Error('Failed to complete OAuth connection');
    }
  }

  /**
   * Initiate Xero connection for a client (legacy method for backward compatibility)
   */
  static async initiateXeroConnection(clientId: string): Promise<string> {
    try {
      console.log(`EntitiesService: Initiating Xero connection for client ${clientId}`);

      // Try the API client first
      try {
        const response = await api.get<{ authorization_url: string }>(`/xero/connect/initiate/${clientId}`);
        console.log('EntitiesService: Xero connection response:', response);
        console.log('EntitiesService: Response type:', typeof response);

        if (response && typeof response === 'object') {
          console.log('EntitiesService: Response keys:', Object.keys(response));

          if ('authorization_url' in response) {
            console.log('EntitiesService: Found authorization_url:', response.authorization_url);
            return response.authorization_url;
          } else {
            console.error('EntitiesService: Missing authorization_url in response. Available keys:', Object.keys(response));
            console.error('EntitiesService: Full response:', JSON.stringify(response, null, 2));
            throw new Error(`Invalid response format: missing authorization_url. Available keys: ${Object.keys(response).join(', ')}`);
          }
        } else {
          console.error('EntitiesService: Invalid response format - not an object:', response);
          throw new Error(`Invalid response format: expected object, got ${typeof response}`);
        }
      } catch (apiError) {
        console.error('EntitiesService: API client failed, trying direct axios call:', apiError);

        // Fallback to direct axios call
        const { auth } = await import('@/lib/firebase');
        const user = auth.currentUser;
        if (!user) {
          throw new Error('User not authenticated');
        }

        const token = await user.getIdToken();
        const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081';

        console.log('EntitiesService: Making direct axios call to:', `${baseURL}/xero/connect/initiate/${clientId}`);

        const axios = (await import('axios')).default;
        const directResponse = await axios.get(`${baseURL}/xero/connect/initiate/${clientId}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          timeout: 30000
        });

        console.log('EntitiesService: Direct axios response:', directResponse.data);

        if (directResponse.data && typeof directResponse.data === 'object' && 'authorization_url' in directResponse.data) {
          return directResponse.data.authorization_url;
        } else {
          throw new Error('Direct axios call also failed - invalid response format');
        }
      }
    } catch (error) {
      console.error('EntitiesService: Error initiating Xero connection:', error);

      // Log more details about the error
      if (error && typeof error === 'object') {
        console.error('EntitiesService: Error details:', {
          message: (error as any).message,
          response: (error as any).response?.data,
          status: (error as any).response?.status,
          statusText: (error as any).response?.statusText
        });
      }

      // Re-throw the original error if it has a specific message, otherwise use generic
      if (error instanceof Error && error.message.includes('Invalid response format')) {
        throw error;
      }

      throw new Error('Failed to initiate Xero connection');
    }
  }

  /**
   * Get chart of accounts for a Xero entity (from Firebase cache first)
   */
  static async getChartOfAccounts(entityId: string): Promise<Account[]> {
    try {
      console.log(`🔍 EntitiesService: Fetching chart of accounts from Firebase for entity: ${entityId}`);
      // Use the entities endpoint which reads from Firebase cache first, then falls back to Xero
      const response = await api.get<{ accounts: Account[] }>(`/entities/${entityId}/accounts`);
      console.log(`📊 EntitiesService: Got ${response.accounts?.length || 0} accounts from Firebase`);
      return response.accounts;
    } catch (error) {
      console.error('Error fetching chart of accounts:', error);
      throw new Error('Failed to fetch chart of accounts');
    }
  }

  /**
   * Revoke OAuth connection
   */
  static async revokeOAuthConnection(entityId: string): Promise<void> {
    try {
      await api.post(`/entities/${entityId}/oauth/revoke`);
    } catch (error) {
      console.error('Error revoking OAuth connection:', error);
      throw new Error('Failed to revoke OAuth connection');
    }
  }

  /**
   * Get entity enums for UI dropdowns
   */
  static async getEntityEnums(): Promise<EntityEnums> {
    try {
      return await api.get<EntityEnums>('/entities/enums');
    } catch (error) {
      console.error('Error fetching entity enums:', error);
      // Return default enums if API fails
      const defaultEnums: EntityEnums = {
        entity_types: [
          { value: 'xero', label: 'Xero', description: 'Xero accounting software' },
          { value: 'qbo', label: 'QuickBooks Online', description: 'QuickBooks Online accounting software' },
          { value: 'manual', label: 'Manual Entry', description: 'Manual data entry' }
        ],
        entity_statuses: [
          { value: 'active', label: 'Active' },
          { value: 'inactive', label: 'Inactive' },
          { value: 'error', label: 'Error' },
          { value: 'syncing', label: 'Syncing' },
          { value: 'pending', label: 'Pending' },
          { value: 'disconnected', label: 'Disconnected' }
        ],
        connection_statuses: [
          { value: 'connected', label: 'Connected' },
          { value: 'disconnected', label: 'Disconnected' },
          { value: 'error', label: 'Error' },
          { value: 'pending', label: 'Pending' },
          { value: 'expired', label: 'Expired' },
          { value: 'syncing', label: 'Syncing' }
        ],
        sync_frequencies: [
          { value: 'hourly', label: 'Hourly' },
          { value: 'daily', label: 'Daily' },
          { value: 'weekly', label: 'Weekly' },
          { value: 'manual', label: 'Manual' }
        ],
        currencies: [
          { value: 'USD', label: 'US Dollar', symbol: '$' },
          { value: 'EUR', label: 'Euro', symbol: '€' },
          { value: 'GBP', label: 'British Pound', symbol: '£' },
          { value: 'CAD', label: 'Canadian Dollar', symbol: 'C$' },
          { value: 'AUD', label: 'Australian Dollar', symbol: 'A$' },
          { value: 'NZD', label: 'New Zealand Dollar', symbol: 'NZ$' },
          { value: 'JPY', label: 'Japanese Yen', symbol: '¥' },
          { value: 'CHF', label: 'Swiss Franc', symbol: 'CHF' }
        ],
        amortization_periods: [
          { value: 1, label: '1 Month' },
          { value: 3, label: '3 Months' },
          { value: 6, label: '6 Months' },
          { value: 12, label: '12 Months' },
          { value: 24, label: '24 Months' },
          { value: 36, label: '36 Months' }
        ],
        sync_data_types: [
          { value: 'invoices', label: 'Invoices', description: 'Customer invoices and sales' },
          { value: 'bills', label: 'Bills', description: 'Vendor bills and purchases' },
          { value: 'payments', label: 'Payments', description: 'Payment transactions' },
          { value: 'bank_transactions', label: 'Bank Transactions', description: 'Bank account transactions' },
          { value: 'journal_entries', label: 'Journal Entries', description: 'Manual journal entries' },
          { value: 'spend_money', label: 'Spend Money / Expenses', description: 'Spend Money transactions (Xero) / Expenses (QuickBooks Online)' }
        ]
      };
      return defaultEnums;
    }
  }
}
