from pydantic import BaseModel, Field
from typing import List
from datetime import datetime

class DashboardSummarySection(BaseModel):
    """Model for a summary section in the dashboard."""
    count: int = Field(..., description="Number of items in this section")
    total_amount: float = Field(..., description="Total amount for items in this section")

class RecentTransaction(BaseModel):
    """Model for a recent transaction in the dashboard."""
    id: str = Field(..., description="Transaction ID")
    document_number: str = Field(..., description="Document number (e.g., invoice number)")
    date: datetime = Field(..., description="Transaction date")
    contact_name: str = Field(..., description="Contact name")
    amount: float = Field(..., description="Transaction amount")
    status: str = Field(..., description="Transaction status")
    type: str = Field(..., description="Transaction type")

class DashboardResponse(BaseModel):
    """Response model for the dashboard endpoint."""
    pending_review: DashboardSummarySection = Field(
        ...,
        description="Summary of transactions pending review"
    )
    approved: DashboardSummarySection = Field(
        ...,
        description="Summary of approved transactions"
    )
    this_month: DashboardSummarySection = Field(
        ...,
        description="Summary of transactions for the current month"
    )
    recent_transactions: List[RecentTransaction] = Field(
        default_factory=list,
        description="List of recent transactions"
    )

    model_config = {
        "json_schema_extra": {
            "example": {
                "pending_review": {
                    "count": 5,
                    "total_amount": 12500.00
                },
                "approved": {
                    "count": 10,
                    "total_amount": 25000.00
                },
                "this_month": {
                    "count": 15,
                    "total_amount": 37500.00
                },
                "recent_transactions": [
                    {
                        "id": "transaction_123",
                        "document_number": "INV-001",
                        "date": "2023-05-15T00:00:00Z",
                        "contact_name": "Acme Corp",
                        "amount": 1500.00,
                        "status": "APPROVED",
                        "type": "INVOICE"
                    }
                ]
            }
        }
    }
