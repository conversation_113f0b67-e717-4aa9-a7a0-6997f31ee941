terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 4.0"
    }
  }
  
  backend "gcs" {
    # This will be configured during initialization
    # bucket = "your-terraform-state-bucket"
    # prefix = "terraform/state"
  }
}

provider "google" {
  project = var.project_id
  region  = var.region
}

# Enable required APIs
resource "google_project_service" "services" {
  for_each = toset([
    "cloudfunctions.googleapis.com",
    "cloudbuild.googleapis.com",
    "cloudscheduler.googleapis.com",
    "run.googleapis.com",
    "secretmanager.googleapis.com",
    "pubsub.googleapis.com",
    "artifactregistry.googleapis.com"
  ])
  
  project = var.project_id
  service = each.value
  
  disable_on_destroy = false
}

# Create a service account for the cloud functions
resource "google_service_account" "function_service_account" {
  account_id   = "drcr-function-sa"
  display_name = "DRCR Cloud Functions Service Account"
  
  depends_on = [google_project_service.services]
}

# Create a service account for the REST API
resource "google_service_account" "api_service_account" {
  account_id   = "drcr-api-sa"
  display_name = "DRCR REST API Service Account"
  
  depends_on = [google_project_service.services]
}

# Grant IAM roles to the function service account
resource "google_project_iam_member" "function_sa_roles" {
  for_each = toset([
    "roles/secretmanager.secretAccessor",
    "roles/pubsub.publisher",
    "roles/pubsub.subscriber",
    "roles/logging.logWriter",
    "roles/errorreporting.writer"
  ])
  
  project = var.project_id
  role    = each.value
  member  = "serviceAccount:${google_service_account.function_service_account.email}"
}

# Grant IAM roles to the API service account
resource "google_project_iam_member" "api_sa_roles" {
  for_each = toset([
    "roles/secretmanager.secretAccessor",
    "roles/pubsub.publisher",
    "roles/logging.logWriter",
    "roles/errorreporting.writer"
  ])
  
  project = var.project_id
  role    = each.value
  member  = "serviceAccount:${google_service_account.api_service_account.email}"
}

# Create a secret for Xero OAuth tokens
resource "google_secret_manager_secret" "xero_oauth_tokens" {
  secret_id = "xero-oauth-tokens"
  
  replication {
    automatic = true
  }
  
  depends_on = [google_project_service.services]
}

# Create an initial version of the secret
resource "google_secret_manager_secret_version" "xero_oauth_tokens_initial" {
  secret      = google_secret_manager_secret.xero_oauth_tokens.id
  secret_data = jsonencode({
    "access_token": "",
    "refresh_token": "",
    "expires_at": ""
  })
}

# Create a secret for Xero API credentials
resource "google_secret_manager_secret" "xero_api_credentials" {
  secret_id = "xero-api-credentials"
  
  replication {
    automatic = true
  }
  
  depends_on = [google_project_service.services]
}

# Create an initial version of the secret
resource "google_secret_manager_secret_version" "xero_api_credentials_initial" {
  secret      = google_secret_manager_secret.xero_api_credentials.id
  secret_data = jsonencode({
    "client_id": var.xero_client_id,
    "client_secret": var.xero_client_secret,
    "redirect_uri": var.xero_redirect_uri
  })
}

# Create a secret for API credentials
resource "google_secret_manager_secret" "api_secret_key" {
  secret_id = "api-secret-key"
  
  replication {
    automatic = true
  }
  
  depends_on = [google_project_service.services]
}

# Create an initial version of the secret
resource "google_secret_manager_secret_version" "api_secret_key_initial" {
  secret      = google_secret_manager_secret.api_secret_key.id
  secret_data = var.api_secret_key
}
