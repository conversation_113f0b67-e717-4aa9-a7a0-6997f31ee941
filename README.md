# DRCR Backend

This is the backend application for the DRCR (Debit Reconciliation & Categorization Resource) system - a comprehensive financial data management platform that automates prepayment processing, Xero integration, and amortization schedule generation.

## Technologies Used

- **Python 3.9+** - Core programming language
- **FastAPI** - Modern, fast web framework for building APIs
- **Google Cloud Platform** - Cloud infrastructure and services
  - Cloud Functions - Serverless compute for background processing
  - Cloud Run - Containerized API hosting
  - Firestore - NoSQL document database
  - Pub/Sub - Messaging and event-driven architecture
- **Xero API** - Accounting platform integration
- **Firebase Authentication** - User authentication and authorization
- **Terraform** - Infrastructure as Code
- **Docker** - Containerization for deployment

## Getting Started

### Prerequisites

- Python 3.9 or higher
- pip (Python package manager)
- Google Cloud SDK (gcloud CLI)
- Docker (for containerized deployment)
- Git

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd drcr_back
```

2. Create and activate a Python virtual environment:
```bash
python -m venv .venv
# On Windows:
.venv\Scripts\activate
# On macOS/Linux:
source .venv/bin/activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Set up environment variables:
   - Copy `.env.example` to `.env` (if available)
   - Configure required environment variables (see Configuration section)

### Development

To start the FastAPI development server:

```bash
cd rest_api
python run_server.py
```

Or for optimized performance (no auto-reload):
```powershell
.\scripts\deployment\start_fast.ps1
```

The API will be available at `http://localhost:8080` with interactive documentation at `http://localhost:8080/docs`.

## Project Structure

### **Core Application**
* **`rest_api/`** - FastAPI-based REST API server
  * `main.py` - FastAPI application entry point and configuration
  * `app.py` - Alternative application setup (legacy)
  * `routes/` - API endpoint definitions organized by resource
    * `auth.py` - Authentication endpoints
    * `clients.py` - Client management endpoints
    * `entities.py` - Entity configuration endpoints
    * `xero.py` - Xero integration endpoints
    * `dashboard.py` - Dashboard data endpoints
  * `models/` - Pydantic models for request/response validation
  * `services/` - Business logic layer
  * `dependencies.py` - FastAPI dependency injection
  * `run_server.py` - Development server runner

* **`cloud_functions/`** - Google Cloud Functions for serverless processing
  * `xero_sync_consumer/` - Processes Xero synchronization via Pub/Sub
    * `main.py` - Cloud Function entry point
    * `requirements.txt` - Function-specific dependencies

* **`drcr_shared_logic/`** - Shared business logic and utilities
  * `database/` - Firestore database interaction modules
  * `services/` - Core business services (amortization, Xero integration)
  * `models/` - Data models and schemas
  * `utils/` - Common utility functions
  * `config.py` - Application configuration management

### **Infrastructure & Deployment**
* **`terraform/`** - Infrastructure as Code definitions
  * `functions.tf` - Cloud Functions configuration
  * `main.tf` - Core infrastructure setup
  * `variables.tf` - Terraform variables

* **`deployment/`** - Deployment configurations and scripts
* **`Dockerfile`** - Container configuration for Cloud Run deployment

### **Testing & Quality Assurance**
* **`tests/`** - Comprehensive test suite
  * `unit/` - Unit tests for individual components
  * `integration/` - Integration tests for API endpoints
  * `python/` - Python-specific test utilities
  * `powershell/` - PowerShell test scripts
  * `cloud_functions/` - Cloud Function testing
  * `performance/` - Performance testing scripts for API endpoints and optimization validation
  * `api/` - API integration and functional tests for various endpoints and workflows
  * `token_management/` - OAuth token management tests and utilities for Firestore token storage
  * `xero_integration/` - Xero-specific integration tests and manual testing tools

### **Scripts & Utilities**
* **`scripts/`** - Utility and deployment scripts
  * `utilities/` - Development utilities (encryption key generation, configuration fixes, token generation)
  * `deployment/` - Deployment and server management scripts (fast startup, production deployment, cleanup)

### **Documentation**
* **`docs/`** - Comprehensive documentation
  * `api_guide/` - API endpoints, authentication, and router configuration
  * `data_model/` - Firestore collections and relationships
  * `development/` - Setup instructions, testing guidelines, and codebase structure
  * `error_handling/` - Error codes and response formats
  * `PERFORMANCE_OPTIMIZATION_SUMMARY.md` - Comprehensive performance improvements and optimization guide

### **Configuration Files**
* **`requirements.txt`** - Python dependencies
* **`.env`** - Environment variables (not in version control)
* **`.gitlab-ci.yml`** - GitLab CI/CD pipeline configuration
* **`.gcloudignore`** - Files to ignore during Google Cloud deployment

## Configuration

The application requires several environment variables to be configured:

### Required Environment Variables

```bash
# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT=drcr-d660a
GCP_PROJECT_ID=drcr-d660a
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account-key.json

# Firebase Configuration
FIREBASE_PROJECT_ID=drcr-d660a

# Xero API Configuration
XERO_CLIENT_ID=your-xero-client-id
XERO_CLIENT_SECRET=your-xero-client-secret
XERO_REDIRECT_URI=your-redirect-uri

# Security
TOKEN_ENCRYPTION_KEY=your-fernet-encryption-key

# API Configuration
API_HOST=0.0.0.0
API_PORT=8080
API_DEBUG=true
```

### Authentication & Authorization

The application uses Firebase Authentication for user management and JWT tokens for API authorization. The authentication flow includes:

- **Firebase Auth** - User registration and login
- **JWT Tokens** - API request authorization
- **Role-based Access** - Client-specific data access control
- **Token Encryption** - Secure storage of OAuth tokens in Firestore

## Local Development & Testing

### Cloud Functions Testing

The `cloud_functions/xero_sync_consumer/main.py` script can be run locally for testing various scenarios:

```bash
# Run the main function directly
python cloud_functions/xero_sync_consumer/main.py

# Test with specific message data
python -c "
import asyncio
from cloud_functions.xero_sync_consumer.main import xero_sync_consumer
# Test your function here
"
```

### API Testing

Use the interactive API documentation at `http://localhost:8080/docs` or test endpoints directly:

```bash
# Health check
curl http://localhost:8080/health

# Test with authentication
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" http://localhost:8080/clients
```

### Performance Testing

Run comprehensive performance tests:

```bash
python scripts/utilities/performance_test.py --url http://localhost:8081 --token-file firebase_id_token.txt
```

## Documentation

Comprehensive documentation is available in the `docs/` directory:

* **API Guide**: `docs/api_guide/` - Information about API endpoints, authentication, and router configuration
* **Data Model**: `docs/data_model/` - Details about Firestore collections and relationships
* **Development Guides**: `docs/development/` - Setup instructions, testing guidelines, and codebase structure
* **Error Handling**: `docs/error_handling/` - Error codes and response formats
* **Performance Optimization**: `docs/PERFORMANCE_OPTIMIZATION_SUMMARY.md` - Comprehensive performance improvements and optimization guide

## Performance Optimization

The DRCR frontend has been extensively optimized for performance:

* **Bundle Size**: Reduced from ~2.5MB to ~800KB (68% reduction)
* **Load Times**: First Contentful Paint improved from ~3.2s to ~1.1s (66% improvement)
* **Code Splitting**: React.lazy() implementation for all page components
* **API Optimization**: Request caching, deduplication, and reduced timeouts
* **Build Optimization**: Manual chunk splitting and asset organization
* **Development Experience**: Optimized HMR and file watching

**Key Features**:
- Intelligent API caching with 5-minute TTL
- Request deduplication within 5-second windows
- Lazy loading with Suspense for better UX
- Optimized Vite configuration for faster builds

For detailed performance metrics and optimization strategies, see `docs/PERFORMANCE_OPTIMIZATION_GUIDE.md`.

## Related Projects

- **Backend API**: `/d:/Projects/drcr_back/` - FastAPI backend with Xero integration
- **Shared Documentation**: Both projects share Firebase configuration and deployment processes
