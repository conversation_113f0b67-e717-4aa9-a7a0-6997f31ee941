import React from "react";
import { cn } from "@/lib/utils";
import { <PERSON>ader2, CheckCircle, AlertCircle, Clock, Wifi, WifiOff, RefreshCw } from "lucide-react";
import { Progress } from "./progress";
import { Alert, AlertDescription } from "./alert";

export type XeroOperationType = 'connect' | 'disconnect' | 'reconnect' | 'sync';
export type XeroOperationStatus = 'idle' | 'loading' | 'success' | 'error' | 'timeout';

interface XeroOperationStatusProps {
  operation: XeroOperationType;
  status: XeroOperationStatus;
  message?: string;
  progress?: number;
  className?: string;
  size?: "sm" | "md" | "lg";
  showProgress?: boolean;
  timeoutSeconds?: number;
  elapsedSeconds?: number;
  showElapsedTime?: boolean;
  variant?: "default" | "compact" | "detailed";
  onRetry?: () => void;
  onCancel?: () => void;
}

const operationMessages = {
  connect: {
    loading: "Connecting to Xero...",
    success: "Successfully connected to Xero",
    error: "Failed to connect to Xero",
    timeout: "Connection is taking longer than expected"
  },
  disconnect: {
    loading: "Disconnecting from Xero...",
    success: "Successfully disconnected from Xero",
    error: "Failed to disconnect from Xero",
    timeout: "Disconnection is taking longer than expected"
  },
  reconnect: {
    loading: "Reconnecting to Xero...",
    success: "Successfully reconnected to Xero",
    error: "Failed to reconnect to Xero",
    timeout: "Reconnection is taking longer than expected"
  },
  sync: {
    loading: "Syncing data with Xero...",
    success: "Data synchronized successfully",
    error: "Failed to sync data",
    timeout: "Sync is taking longer than expected"
  }
};

const operationDescriptions = {
  connect: {
    loading: "Please wait while we establish a secure connection with your Xero account. This may take a few moments.",
    success: "Your Xero account is now connected and ready for data synchronization.",
    error: "We couldn't establish a connection with your Xero account. Please check your credentials and try again.",
    timeout: "The connection process is taking longer than usual. This might be due to network issues or high server load."
  },
  disconnect: {
    loading: "We're safely disconnecting your Xero account. Your data will remain secure.",
    success: "Your Xero account has been disconnected. No further data synchronization will occur.",
    error: "We encountered an issue while disconnecting your account. Please try again or contact support.",
    timeout: "The disconnection process is taking longer than expected. Your account may still be disconnected."
  },
  reconnect: {
    loading: "Re-establishing connection with your Xero account. Please wait while we refresh your authentication.",
    success: "Your Xero connection has been successfully restored and is ready for use.",
    error: "We couldn't reconnect to your Xero account. You may need to re-authorize the connection.",
    timeout: "The reconnection process is taking longer than usual. Please check your network connection."
  },
  sync: {
    loading: "Synchronizing your latest data from Xero. This may take a few minutes depending on the amount of data.",
    success: "All data has been successfully synchronized from your Xero account.",
    error: "We encountered an issue while syncing your data. Some information may not be up to date.",
    timeout: "The sync process is taking longer than expected. Large amounts of data may require more time."
  }
};

const sizeClasses = {
  sm: {
    icon: "h-4 w-4",
    text: "text-sm",
    container: "gap-2",
    description: "text-xs"
  },
  md: {
    icon: "h-5 w-5",
    text: "text-base",
    container: "gap-3",
    description: "text-sm"
  },
  lg: {
    icon: "h-6 w-6",
    text: "text-lg",
    container: "gap-4",
    description: "text-base"
  }
};

export function XeroOperationStatus({
  operation,
  status,
  message,
  progress,
  className,
  size = "md",
  showProgress = false,
  timeoutSeconds,
  elapsedSeconds,
  showElapsedTime = false,
  variant = "default",
  onRetry,
  onCancel
}: XeroOperationStatusProps) {
  const sizeConfig = sizeClasses[size];
  const defaultMessage = message || operationMessages[operation][status] || "";
  const description = operationDescriptions[operation][status] || "";

  const getIcon = () => {
    switch (status) {
      case 'loading':
        return <Loader2 className={cn("animate-spin text-primary", sizeConfig.icon)} />;
      case 'success':
        return <CheckCircle className={cn("text-green-600", sizeConfig.icon)} />;
      case 'error':
        return <AlertCircle className={cn("text-destructive", sizeConfig.icon)} />;
      case 'timeout':
        return <Clock className={cn("text-yellow-600", sizeConfig.icon)} />;
      default:
        return null;
    }
  };

  const getProgressValue = () => {
    if (progress !== undefined) return progress;
    if (status === 'loading' && timeoutSeconds && elapsedSeconds) {
      return Math.min((elapsedSeconds / timeoutSeconds) * 100, 100);
    }
    if (status === 'loading') return undefined; // Indeterminate progress
    if (status === 'success') return 100;
    return 0;
  };

  const getTimeoutMessage = () => {
    if (timeoutSeconds && elapsedSeconds && status === 'loading') {
      const remaining = Math.max(0, timeoutSeconds - elapsedSeconds);
      if (remaining <= 10) {
        return `Operation may timeout in ${remaining}s`;
      }
    }
    return null;
  };

  const getTextColor = () => {
    switch (status) {
      case 'loading':
        return "text-foreground";
      case 'success':
        return "text-green-700";
      case 'error':
        return "text-destructive";
      case 'timeout':
        return "text-yellow-700";
      default:
        return "text-muted-foreground";
    }
  };

  const formatElapsedTime = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  if (status === 'idle') {
    return null;
  }

  // Compact variant for inline display
  if (variant === "compact") {
    return (
      <div className={cn("flex items-center", sizeConfig.container, className)}>
        {getIcon()}
        <span className={cn(sizeConfig.text, getTextColor())}>
          {defaultMessage}
        </span>
        {showElapsedTime && elapsedSeconds !== undefined && status === 'loading' && (
          <span className="text-xs text-muted-foreground">
            ({formatElapsedTime(elapsedSeconds)})
          </span>
        )}
      </div>
    );
  }

  // Detailed variant with full information
  if (variant === "detailed") {
    return (
      <Alert className={cn(
        "border-l-4 shadow-lg transition-all duration-300 ease-in-out",
        "bg-gradient-to-r from-white to-gray-50/50 backdrop-blur-sm",
        {
          "border-l-blue-500 bg-gradient-to-r from-blue-50 to-blue-100/30 shadow-blue-100": status === 'loading',
          "border-l-green-500 bg-gradient-to-r from-green-50 to-green-100/30 shadow-green-100": status === 'success',
          "border-l-red-500 bg-gradient-to-r from-red-50 to-red-100/30 shadow-red-100": status === 'error',
          "border-l-amber-500 bg-gradient-to-r from-amber-50 to-amber-100/30 shadow-amber-100": status === 'timeout'
        }, 
        className
      )}>
        <div className="flex items-start gap-4">
          <div className={cn(
            "flex-shrink-0 p-2 rounded-full transition-all duration-300",
            {
              "bg-blue-100 text-blue-600": status === 'loading',
              "bg-green-100 text-green-600": status === 'success',
              "bg-red-100 text-red-600": status === 'error',
              "bg-amber-100 text-amber-600": status === 'timeout'
            }
          )}>
            {getIcon()}
          </div>
          
          <div className="flex-1 space-y-3">
            <div className="flex items-center justify-between">
              <h4 className={cn(
                "font-semibold text-lg tracking-tight",
                {
                  "text-blue-800": status === 'loading',
                  "text-green-800": status === 'success',
                  "text-red-800": status === 'error',
                  "text-amber-800": status === 'timeout'
                }
              )}>
                {defaultMessage}
              </h4>
              {showElapsedTime && elapsedSeconds !== undefined && status === 'loading' && (
                <div className="flex items-center gap-2 px-3 py-1 bg-white/70 rounded-full border border-blue-200">
                  <Clock className="h-3 w-3 text-blue-600" />
                  <span className="text-sm font-medium text-blue-700">
                    {formatElapsedTime(elapsedSeconds)}
                  </span>
                </div>
              )}
            </div>
            
            <AlertDescription className={cn(
              "text-base leading-relaxed",
              {
                "text-blue-700": status === 'loading',
                "text-green-700": status === 'success',
                "text-red-700": status === 'error',
                "text-amber-700": status === 'timeout'
              }
            )}>
              {description}
            </AlertDescription>

            {showProgress && (
              <div className="space-y-3">
                {(status === 'loading' || status === 'success') && (
                  <div className="space-y-2">
                    <Progress 
                      value={getProgressValue()} 
                      className={cn(
                        "h-3 bg-white/50 border border-gray-200 shadow-inner",
                        {
                          "[&>div]:bg-gradient-to-r [&>div]:from-blue-500 [&>div]:to-blue-600": status === 'loading',
                          "[&>div]:bg-gradient-to-r [&>div]:from-green-500 [&>div]:to-green-600": status === 'success'
                        }
                      )}
                    />
                    {status === 'loading' && (
                      <div className="flex items-center justify-center">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                          <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                          <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
                {status === 'loading' && timeoutSeconds && elapsedSeconds !== undefined && (
                  <div className="flex justify-between items-center p-3 bg-white/60 rounded-lg border border-blue-200">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                      <span className="text-sm font-medium text-blue-700">
                        Elapsed: {formatElapsedTime(elapsedSeconds)}
                      </span>
                    </div>
                    <span className="text-sm text-blue-600">
                      Timeout: {timeoutSeconds}s
                    </span>
                  </div>
                )}
                {getTimeoutMessage() && (
                  <div className="flex items-center gap-2 p-3 bg-amber-100 border border-amber-300 rounded-lg">
                    <AlertCircle className="h-4 w-4 text-amber-600" />
                    <span className="text-sm font-medium text-amber-800">
                      {getTimeoutMessage()}
                    </span>
                  </div>
                )}
              </div>
            )}

            {(status === 'error' || status === 'timeout') && (onRetry || onCancel) && (
              <div className="flex gap-3 pt-2">
                {onRetry && (
                  <button
                    onClick={onRetry}
                    className={cn(
                      "flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-200",
                      "bg-white border-2 hover:shadow-md transform hover:scale-105",
                      {
                        "border-red-300 text-red-700 hover:bg-red-50 hover:border-red-400": status === 'error',
                        "border-amber-300 text-amber-700 hover:bg-amber-50 hover:border-amber-400": status === 'timeout'
                      }
                    )}
                  >
                    <RefreshCw className="h-4 w-4" />
                    Try Again
                  </button>
                )}
                {onCancel && (
                  <button
                    onClick={onCancel}
                    className="flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 bg-gray-100 border-2 border-gray-300 text-gray-700 hover:bg-gray-200 hover:border-gray-400 hover:shadow-md transform hover:scale-105"
                  >
                    Cancel
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      </Alert>
    );
  }

  // Default variant
  return (
    <div className={cn(
      "flex flex-col p-4 rounded-lg border shadow-sm transition-all duration-300",
      "bg-gradient-to-r from-white to-gray-50/50",
      {
        "border-blue-200 bg-gradient-to-r from-blue-50/50 to-blue-100/30": status === 'loading',
        "border-green-200 bg-gradient-to-r from-green-50/50 to-green-100/30": status === 'success',
        "border-red-200 bg-gradient-to-r from-red-50/50 to-red-100/30": status === 'error',
        "border-amber-200 bg-gradient-to-r from-amber-50/50 to-amber-100/30": status === 'timeout'
      },
      sizeConfig.container, 
      className
    )}>
      <div className={cn("flex items-center justify-between", sizeConfig.container)}>
        <div className="flex items-center gap-3">
          <div className={cn(
            "p-1.5 rounded-full",
            {
              "bg-blue-100 text-blue-600": status === 'loading',
              "bg-green-100 text-green-600": status === 'success',
              "bg-red-100 text-red-600": status === 'error',
              "bg-amber-100 text-amber-600": status === 'timeout'
            }
          )}>
            {getIcon()}
          </div>
          <span className={cn(
            sizeConfig.text, 
            "font-medium",
            {
              "text-blue-800": status === 'loading',
              "text-green-800": status === 'success',
              "text-red-800": status === 'error',
              "text-amber-800": status === 'timeout'
            }
          )}>
            {defaultMessage}
          </span>
        </div>
        {showElapsedTime && elapsedSeconds !== undefined && status === 'loading' && (
          <div className="flex items-center gap-1 px-2 py-1 bg-white/70 rounded-full border border-blue-200">
            <Clock className="h-3 w-3 text-blue-600" />
            <span className="text-xs font-medium text-blue-700">
              {formatElapsedTime(elapsedSeconds)}
            </span>
          </div>
        )}
      </div>

      {showProgress && (
        <div className="w-full space-y-2 mt-3">
          {(status === 'loading' || status === 'success') && (
            <Progress 
              value={getProgressValue()} 
              className={cn(
                "h-2 bg-white/50 border border-gray-200",
                {
                  "[&>div]:bg-gradient-to-r [&>div]:from-blue-500 [&>div]:to-blue-600": status === 'loading',
                  "[&>div]:bg-gradient-to-r [&>div]:from-green-500 [&>div]:to-green-600": status === 'success'
                }
              )}
            />
          )}
          {status === 'loading' && timeoutSeconds && elapsedSeconds !== undefined && (
            <div className="flex justify-between text-xs">
              <span className="text-blue-700 font-medium">Elapsed: {formatElapsedTime(elapsedSeconds)}</span>
              <span className="text-blue-600">Timeout: {timeoutSeconds}s</span>
            </div>
          )}
          {getTimeoutMessage() && (
            <div className="flex items-center gap-1 text-xs text-amber-700 font-medium">
              <AlertCircle className="h-3 w-3" />
              {getTimeoutMessage()}
            </div>
          )}
        </div>
      )}
    </div>
  );
}

// Enhanced hook for managing Xero operation state with better UX
export function useXeroOperationState(timeoutSeconds: number = 30) {
  const [status, setStatus] = React.useState<XeroOperationStatus>('idle');
  const [elapsedSeconds, setElapsedSeconds] = React.useState(0);
  const [timeoutId, setTimeoutId] = React.useState<NodeJS.Timeout | null>(null);
  const [intervalId, setIntervalId] = React.useState<NodeJS.Timeout | null>(null);
  const [operation, setOperation] = React.useState<XeroOperationType>('connect');

  const startOperation = React.useCallback((operationType: XeroOperationType = 'connect') => {
    setOperation(operationType);
    setStatus('loading');
    setElapsedSeconds(0);

    // Start elapsed time counter
    const interval = setInterval(() => {
      setElapsedSeconds(prev => prev + 1);
    }, 1000);
    setIntervalId(interval);

    // Set timeout
    const timeout = setTimeout(() => {
      setStatus('timeout');
      clearInterval(interval);
    }, timeoutSeconds * 1000);
    setTimeoutId(timeout);
  }, [timeoutSeconds]);

  const completeOperation = React.useCallback((success: boolean) => {
    setStatus(success ? 'success' : 'error');
    if (timeoutId) clearTimeout(timeoutId);
    if (intervalId) clearInterval(intervalId);
    setTimeoutId(null);
    setIntervalId(null);
  }, [timeoutId, intervalId]);

  const resetOperation = React.useCallback(() => {
    setStatus('idle');
    setElapsedSeconds(0);
    if (timeoutId) clearTimeout(timeoutId);
    if (intervalId) clearInterval(intervalId);
    setTimeoutId(null);
    setIntervalId(null);
  }, [timeoutId, intervalId]);

  const retryOperation = React.useCallback(() => {
    resetOperation();
    // Allow caller to restart the operation
    return { startOperation };
  }, [resetOperation, startOperation]);

  React.useEffect(() => {
    return () => {
      if (timeoutId) clearTimeout(timeoutId);
      if (intervalId) clearInterval(intervalId);
    };
  }, [timeoutId, intervalId]);

  return {
    status,
    elapsedSeconds,
    operation,
    startOperation,
    completeOperation,
    resetOperation,
    retryOperation,
    isLoading: status === 'loading',
    isSuccess: status === 'success',
    isError: status === 'error',
    isTimeout: status === 'timeout'
  };
}

// Utility component for displaying connection status with Xero-specific styling
export function XeroConnectionBadge({ 
  status, 
  size = "sm" 
}: { 
  status: 'connected' | 'disconnected' | 'error' | 'pending' | 'expired' | 'syncing';
  size?: "sm" | "md" | "lg";
}) {
  const sizeConfig = sizeClasses[size];
  
  const statusConfig = {
    connected: { 
      icon: <Wifi className={sizeConfig.icon} />, 
      color: "text-green-600 bg-green-50 border-green-200",
      label: "Connected"
    },
    disconnected: { 
      icon: <WifiOff className={sizeConfig.icon} />, 
      color: "text-gray-600 bg-gray-50 border-gray-200",
      label: "Disconnected"
    },
    error: { 
      icon: <AlertCircle className={sizeConfig.icon} />, 
      color: "text-red-600 bg-red-50 border-red-200",
      label: "Error"
    },
    pending: { 
      icon: <Clock className={sizeConfig.icon} />, 
      color: "text-yellow-600 bg-yellow-50 border-yellow-200",
      label: "Pending"
    },
    expired: { 
      icon: <AlertCircle className={sizeConfig.icon} />, 
      color: "text-orange-600 bg-orange-50 border-orange-200",
      label: "Expired"
    },
    syncing: { 
      icon: <RefreshCw className={cn(sizeConfig.icon, "animate-spin")} />, 
      color: "text-blue-600 bg-blue-50 border-blue-200",
      label: "Syncing"
    }
  };

  const config = statusConfig[status];

  return (
    <div className={cn(
      "inline-flex items-center gap-1 px-2 py-1 rounded-md border text-xs font-medium",
      config.color
    )}>
      {config.icon}
      <span>{config.label}</span>
    </div>
  );
}
