from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import date, datetime
from enum import Enum

class ScheduleStatus(str, Enum):
    PENDING_REVIEW = "pending_review"
    PENDING_CONFIRMATION = "pending_confirmation"
    CONFIRMED = "confirmed" # Journal proposed or ready for posting
    POSTED = "posted"       # Journal successfully posted to accounting system
    CANCELLED = "cancelled"
    ERROR = "error"

class Schedule(BaseModel):
    id: str = Field(..., description="Unique identifier for the amortization schedule entry (Firestore Document ID)")
    transaction_id: str = Field(..., description="ID of the parent transaction this schedule belongs to")
    line_item_id: Optional[str] = Field(None, description="ID of the specific line item this schedule refers to, if applicable")
    
    status: ScheduleStatus = Field(..., description="Current status of the schedule entry")
    entry_date: date = Field(..., description="Date the schedule entry is due to be recognized/posted")
    amount: float = Field(..., description="Amount to be amortized for this entry")
    currency: str = Field(..., description="Currency of the amount (e.g., USD)")
    
    description: Optional[str] = Field(None, description="Description for this schedule entry")
    account_code: Optional[str] = Field(None, description="Expense account code for this amortization entry")
    
    # Journal related fields (populated if a journal is created/posted)
    journal_id_external: Optional[str] = Field(None, description="ID of the journal entry in the external accounting system (e.g., Xero JournalID)")
    journal_link_external: Optional[str] = Field(None, description="Direct link to the journal entry in the external accounting system")

    # Timestamps
    created_at: datetime = Field(default_factory=datetime.now, description="Timestamp of when the schedule entry was created")
    updated_at: datetime = Field(default_factory=datetime.now, description="Timestamp of when the schedule entry was last updated")

    class Config:
        from_attributes = True
        use_enum_values = True # Ensure enum values are used in serialization 