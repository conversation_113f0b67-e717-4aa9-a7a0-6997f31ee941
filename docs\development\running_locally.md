# Running the DRCR Backend Locally

This guide provides instructions for running the DRCR FastAPI application and its associated services (like Firebase emulators) on your local machine for development and testing.

**Prerequisites:** Ensure you have completed all steps in the [Prerequisites & Setup](./setup.md) guide, including:
*   Cloning the repository.
*   Setting up and activating your Python virtual environment (`.venv`).
*   Installing all dependencies from `requirements.txt`.
*   Configuring your `.env` file with necessary environment variables (especially `GOOGLE_APPLICATION_CREDENTIALS` and `FIREBASE_PROJECT_ID`).

## 1. Running the FastAPI Application

The DRCR REST API is built with FastAPI and typically run using Uvicorn, an ASGI server.

*   **Navigate to the `rest_api` directory**:
    Most likely, your main application instance (`app` from `app.py`) is located here.
    ```bash
    cd rest_api
    ```
    If your `app.py` (or the file containing `app = FastAPI()`) is in the root or another location, adjust the path accordingly.

*   **Start the Uvicorn server**:
    ```bash
    uvicorn app:app --reload --host 0.0.0.0 --port 8000
    ```
    *   `app:app`: This tells Uvicorn to look for an object named `app` inside a file named `app.py`. Adjust if your file or app object is named differently.
    *   `--reload`: Enables auto-reload, so the server will restart automatically when you make code changes. This is very useful for development.
    *   `--host 0.0.0.0`: Makes the server accessible from your local network (not just `localhost`).
    *   `--port 8000`: Specifies the port to run on. You can change this if port 8000 is in use.

*   **Accessing the API**:
    Once the server is running, you can access:
    *   The API itself at `http://localhost:8000` or `http://<your-local-ip>:8000`.
    *   The interactive Swagger UI documentation at `http://localhost:8000/docs`.
    *   The ReDoc documentation at `http://localhost:8000/redoc`.

## 2. Running Cloud Functions Locally (with Firebase Emulator Suite)

For developing and testing Cloud Functions, including those triggered by Firestore events or HTTP requests, the Firebase Emulator Suite is highly recommended. It allows you to run emulated versions of Firebase services on your local machine.

*   **Install/Update Firebase Emulator Suite**:
    If you haven't already, or to ensure you have the latest emulators:
    ```bash
    firebase setup:emulators:firestore
    firebase setup:emulators:functions
    # firebase setup:emulators:auth # If you also want to emulate Firebase Auth
    # firebase setup:emulators:pubsub # If you use Pub/Sub triggered functions
    ```
    Follow the prompts. This will also help create a `firebase.json` configuration if one doesn't exist or update the existing one.

*   **Configure `firebase.json`**:
    Ensure your `firebase.json` file correctly configures the emulators, especially the source directory for your functions and any necessary runtime settings. Example snippets:
    ```json
    {
      "functions": {
        "source": "cloud_functions" // Or the correct path to your functions' root
      },
      "emulators": {
        "auth": {
          "port": 9099
        },
        "functions": {
          "port": 5001
        },
        "firestore": {
          "port": 8080,
          "host": "localhost" // Default is localhost
        },
        // ... other emulators ...
        "ui": {
          "enabled": true, // Enables the Emulator Suite UI
          "port": 4000    // Default port for UI
        }
      }
    }
    ```

*   **Start the Firebase Emulator Suite**:
    From the root of your project directory (where `firebase.json` is located):
    ```bash
    firebase emulators:start
    ```
    This will start the emulators for Firestore, Functions, and any other services you've configured.
    *   The Emulator UI will be accessible (usually at `http://localhost:4000`) providing a dashboard to view data in the emulated Firestore, logs from emulated functions, etc.

*   **Pointing your local FastAPI app to the emulators**:
    When running your FastAPI application locally alongside the emulators, you need to ensure it interacts with the emulated services instead of your live Firebase project. This is typically handled by environment variables that the Firebase Admin SDK and client libraries automatically detect when the emulators are running.
    *   **Firestore**: If `FIRESTORE_EMULATOR_HOST` environment variable is set (e.g., `localhost:8080` or `127.0.0.1:8080`), the Admin SDK will connect to the local Firestore emulator. The `firebase emulators:start` command usually sets this up for its sub-processes. If running the FastAPI app in a separate terminal, you might need to set this manually or use a script:
        ```bash
        export FIRESTORE_EMULATOR_HOST="localhost:8080" # For Linux/macOS
        # For Windows (PowerShell): $env:FIRESTORE_EMULATOR_HOST="localhost:8080"
        uvicorn app:app --reload ...
        ```
    *   **Auth/Other Services**: Similar environment variables (`FIREBASE_AUTH_EMULATOR_HOST`, etc.) apply for other emulated services.

## 3. Interacting with Emulated Firestore

*   **From your FastAPI application**: With `FIRESTORE_EMULATOR_HOST` set, your application's Firestore client (using Firebase Admin SDK) will automatically connect to the emulated Firestore.
*   **Using the Emulator UI**: Access the Emulator UI (typically `http://localhost:4000`) to view, add, edit, and delete data in your emulated Firestore instance. This is very useful for setting up test data or observing the results of function triggers.
*   **Data Persistence**: By default, data in the emulators is not persisted between sessions. To save and load data:
    *   `firebase emulators:start --import=./path/to/export-data-dir`
    *   `firebase emulators:export ./path/to/export-data-dir` (can be run while emulators are active, or it will prompt on shutdown)

## Important Considerations

*   **Virtual Environment**: Always ensure your Python virtual environment (`.venv`) is activated in any terminal session where you're running DRCR backend code.
*   **`.env` File**: The `.env` file should be correctly configured with all necessary variables as outlined in the `setup.md` guide.
*   **Separate Terminals**: You will likely need multiple terminal windows:
    *   One for the Uvicorn server (FastAPI app).
    *   One for the Firebase Emulator Suite.
    *   Potentially others for `git` commands, `pip install`, etc.

---

By following these steps, you can effectively run and test the DRCR backend components locally, enabling a faster and more isolated development cycle. 