# LLM Integration Test Invoice Creation Guide

## 🎯 Test Invoice Creation Strategy

### Test Scenarios to Create

#### 1. **GL-Only Detection** (Account Code 620)
Create bills using account code `620` without attachments:
```
Bill Details:
- Supplier: Test Software Company
- Account Code: 620 (Prepaid Expenses)
- Description: "Annual Software License - 12 months"
- Amount: £1,200
- Date: Current date
- Reference: "GL-TEST-001"
```

#### 2. **LLM-Only Detection** (With Attachments)
Create bills with different account codes but clear prepayment language:
```
Bill Details:
- Supplier: Insurance Provider Ltd
- Account Code: 770 (General Expenses)
- Description: "Professional Services"
- Amount: £2,400
- Date: Current date
- Reference: "LLM-TEST-001"
- Attachment: PDF with text like "Annual Insurance Premium - Coverage Period: Jan 1, 2025 to Dec 31, 2025"
```

#### 3. **Combined Detection** (Account 620 + Attachments)
Create bills with both GL coding and attachments:
```
Bill Details:
- Supplier: Office Rent Ltd
- Account Code: 620 (Prepaid Expenses)
- Description: "Quarterly Rent Payment"
- Amount: £3,600
- Date: Current date
- Reference: "COMBINED-TEST-001"
- Attachment: PDF with "Rent Period: Q1 2025 (Jan 1 - Mar 31, 2025)"
```

#### 4. **Service Period Extraction Test**
Create bills with complex service periods for LLM extraction:
```
Bill Details:
- Supplier: Consulting Services Inc
- Account Code: 310 (Professional Services)
- Description: "Consulting Retainer"
- Amount: £6,000
- Date: Current date
- Reference: "SERVICE-TEST-001"
- Attachment: PDF with "Service Period: February 15, 2025 through August 14, 2025"
```

## 📄 Attachment Content Examples

### Example 1: Software License
```
INVOICE #INV-2025-001
Software License Agreement

License Period: January 1, 2025 to December 31, 2025
Annual License Fee: £1,200.00
Payment Terms: Annual prepayment required

Service Details:
- Software access for 12 months
- Support included for full term
- License valid from 01/01/2025 to 31/12/2025
```

### Example 2: Insurance Premium
```
INSURANCE PREMIUM NOTICE
Policy Number: POL-2025-789

Coverage Period: 01 January 2025 - 31 December 2025
Premium Amount: £2,400.00
Payment Due: Immediate

This premium covers the full annual period.
Coverage begins: January 1st, 2025
Coverage ends: December 31st, 2025
```

### Example 3: Rent Payment
```
RENT INVOICE - Q1 2025
Property: Office Suite 123

Rental Period: 1st January 2025 - 31st March 2025
Quarterly Rent: £3,600.00
Due Date: 1st January 2025

This payment covers three months:
- January 2025
- February 2025  
- March 2025
```

## 🔧 Testing Process

### Step 1: Create Test Invoices in Xero
1. Log into your Xero test organization
2. Create bills using the scenarios above
3. Upload PDF attachments with clear service period language
4. Ensure bills are approved/authorized

### Step 2: Monitor with Test Script
```bash
python tests/monitor_llm_test_flow.py
```

### Step 3: Trigger Sync
The monitoring script can trigger syncs, or manually trigger:
```bash
# The script will show entities and let you choose which to sync
```

### Step 4: Analyze Results
Look for:
- ✅ `gl_coding_detected: true` for account 620 bills
- ✅ `llm_detected: true` for bills with prepayment attachments  
- ✅ `llm_service_start_date` and `llm_service_end_date` extracted
- ✅ `llm_confidence` scores above 0.7
- ✅ `detection_method` showing "combined" for dual detection

## 📊 Expected Results

### GL Detection (Account 620)
```json
{
  "prepayment_analysis": {
    "gl_coding_detected": true,
    "gl_detected_accounts": ["620"],
    "detection_method": "gl_coding"
  }
}
```

### LLM Detection (With Attachments)
```json
{
  "prepayment_analysis": {
    "llm_detected": true,
    "llm_confidence": 0.85,
    "llm_service_start_date": "2025-01-01",
    "llm_service_end_date": "2025-12-31",
    "detection_method": "llm_analysis"
  }
}
```

### Combined Detection
```json
{
  "prepayment_analysis": {
    "gl_coding_detected": true,
    "gl_detected_accounts": ["620"],
    "llm_detected": true,
    "llm_confidence": 0.92,
    "llm_service_start_date": "2025-01-01", 
    "llm_service_end_date": "2025-03-31",
    "detection_method": "combined"
  }
}
```

## 🚨 Troubleshooting

### No LLM Processing
- Check `enable_llm_prepayment_detection: true` in entity settings
- Verify attachments are properly uploaded to Xero
- Check Cloud Function logs for LLM API errors

### No GL Detection  
- Verify `prepayment_asset_account_codes: ["620"]` in entity settings
- Ensure bills use exact account code "620"
- Check line item account codes in sync results

### No Service Period Extraction
- Ensure attachment text clearly mentions dates
- Check LLM confidence scores (low confidence may skip extraction)
- Verify date formats are recognizable (DD/MM/YYYY, Month DD, YYYY, etc.)

## 📝 Test Checklist

- [ ] Created bill with account 620 (GL detection)
- [ ] Created bill with attachment containing service dates (LLM detection)  
- [ ] Created bill with both account 620 AND attachment (combined)
- [ ] Uploaded clear PDF attachments with service period text
- [ ] Verified entity has `enable_llm_prepayment_detection: true`
- [ ] Verified entity has `prepayment_asset_account_codes: ["620"]`
- [ ] Triggered sync using monitoring script
- [ ] Checked results show expected detection methods
- [ ] Verified service periods extracted correctly
- [ ] Confirmed confidence scores are reasonable (>0.7)

Ready to test the full LLM integration flow! 🚀 