from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional

class BaseAccountingClient(ABC):
    """
    Abstract Base Class for accounting platform API clients.
    Defines a common interface for interacting with different accounting systems.
    """

    def __init__(self, platform_org_id: str, tenant_id: str, config: Dict[str, Any]):
        """
        Initializes the client.

        Args:
            platform_org_id: The unique identifier for the organization on the platform (e.g., Xero Tenant ID, QBO Realm ID).
            tenant_id: The internal tenant ID within your system.
            config: A dictionary containing platform-specific configurations like
                    client_id, client_secret, api_base_url, token_url, scopes, etc.
        """
        self.platform_org_id = platform_org_id
        self.tenant_id = tenant_id
        self.config = config
        # Consider initializing a logger here, e.g., self.logger = logging.getLogger(self.__class__.__name__)

    @abstractmethod
    async def get_records(
        self,
        record_type: str, # e.g., "Invoices", "Bills", "Contacts"
        params: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Fetches a list of records of a specific type.
        Token management should be handled internally by the implementing client.
        """
        pass

    @abstractmethod
    async def get_record_detail(
        self,
        record_type: str,
        record_id: str,
        params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Fetches detailed information for a single record.
        Token management should be handled internally by the implementing client.
        """
        pass

    @abstractmethod
    async def download_attachment_content(
        self,
        attachment_url: str # The direct URL to download the attachment
        # platform_org_id might be needed if tokens are org-specific and not handled globally by client instance
    ) -> bytes:
        """
        Downloads the content of an attachment given its URL.
        Token management should be handled internally by the implementing client.
        """
        pass

    @abstractmethod
    async def refresh_access_token(self) -> str:
        """
        Forces a refresh of the access token and returns the new token.
        Stores the refreshed token internally for subsequent use.
        This is more for platform clients that manage tokens with refresh mechanisms.
        """
        pass

    @abstractmethod
    async def get_current_access_token(self) -> str:
        """
        Ensures a valid access token is available (refreshing if necessary via its own logic)
        and returns it. This is the token that should be used for API calls if the client
        doesn't embed token usage transparently in other methods.
        """
        pass 