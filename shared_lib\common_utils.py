import logging
import json
import os
from datetime import datetime, date
from typing import Any, Dict, List, Optional, Union
from google.cloud import secretmanager

class DateTimeEncoder(json.JSONEncoder):
    """JSON encoder that handles datetime objects."""
    
    def default(self, obj):
        if isinstance(obj, (datetime, date)):
            return obj.isoformat()
        return super().default(obj)

def setup_logging(name: str = None, level: int = logging.INFO) -> logging.Logger:
    """
    Set up logging configuration.
    
    Args:
        name: Logger name
        level: Logging level
        
    Returns:
        Logger object
    """
    logger = logging.getLogger(name)
    
    # If the logger already has handlers, don't add another one
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    logger.setLevel(level)
    
    return logger

def get_secret(secret_name: str, project_id: str = None) -> str:
    """
    Get a secret from Secret Manager.
    
    Args:
        secret_name: Name of the secret
        project_id: Google Cloud project ID (defaults to environment variable)
        
    Returns:
        Secret value as a string
    """
    if not project_id:
        project_id = os.environ.get("GCP_PROJECT_ID")
        if not project_id:
            raise ValueError("No project ID provided and GCP_PROJECT_ID environment variable not set")
    
    client = secretmanager.SecretManagerServiceClient()
    name = f"projects/{project_id}/secrets/{secret_name}/versions/latest"
    
    try:
        response = client.access_secret_version(request={"name": name})
        return response.payload.data.decode("UTF-8")
    except Exception as e:
        logging.error(f"Error accessing secret {secret_name}: {e}")
        raise

def format_currency(amount: float, currency: str = "USD") -> str:
    """
    Format a currency amount.
    
    Args:
        amount: The amount to format
        currency: The currency code
        
    Returns:
        Formatted currency string
    """
    if currency == "USD":
        return f"${amount:,.2f}"
    elif currency == "EUR":
        return f"€{amount:,.2f}"
    elif currency == "GBP":
        return f"£{amount:,.2f}"
    else:
        return f"{amount:,.2f} {currency}"

def parse_date(date_str: str, formats: List[str] = None) -> Optional[date]:
    """
    Parse a date string into a date object.
    
    Args:
        date_str: Date string to parse
        formats: List of format strings to try
        
    Returns:
        Date object or None if parsing fails
    """
    if not date_str:
        return None
    
    if not formats:
        formats = ["%Y-%m-%d", "%d/%m/%Y", "%m/%d/%Y", "%Y/%m/%d"]
    
    for fmt in formats:
        try:
            return datetime.strptime(date_str, fmt).date()
        except ValueError:
            continue
    
    return None
