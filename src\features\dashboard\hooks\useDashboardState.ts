import React, { useState, useEffect, useCallback } from 'react';
import { EntitiesService } from '@/services/entities.service';
import { api } from '@/lib/api';
import { toast } from 'sonner';
import { useXeroOperationState } from '@/components/ui/xero-operation-status';
import type {
  DashboardState,
  DashboardActions,
  DashboardFilters,
  ClientSummary
} from '../types';

const fetchFirmClientSummary = async (firmId: string, filters: any): Promise<{ clients: ClientSummary[], pagination: any }> => {
  try {
    console.log('DEBUG: fetchFirmClientSummary called with firmId:', firmId, 'filters:', filters);

    // First test if we can reach the backend at all
    console.log('DEBUG: Testing basic connectivity...');
    try {
      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081';
      const healthResponse = await fetch(`${apiBaseUrl}/health`);
      console.log('DEBUG: Health check response:', healthResponse.status);
    } catch (healthError) {
      console.error('DEBUG: Health check failed:', healthError);
      throw new Error('Cannot connect to backend server');
    }

    const apiFilters = {
      page: filters.page || 1,
      limit: filters.pageSize || 5,
      client_filter: filters.clientFilter,
      status_filter: filters.statusFilter,
    };

    console.log('DEBUG: Making API call with filters:', apiFilters);
    const response = await api.getClientsSummary(apiFilters);
    console.log('DEBUG: Dashboard API response received:', JSON.stringify(response, null, 2));
    console.log('DEBUG: First client name field:', response.clients[0]?.name);
    console.log('DEBUG: First client object:', response.clients[0]);

    return {
      clients: response.clients,
      pagination: response.pagination,
    };
  } catch (error) {
    console.error('Error fetching firm client summary:', error);
    throw new Error('Failed to fetch firm dashboard data');
  }
};

export function useDashboardState(firmId: string): DashboardState & DashboardActions & {
  filters: DashboardFilters;
  setFilters: (filters: Partial<DashboardFilters>) => void;
  refreshData: () => Promise<void>;
  setConnectModalOpen: (open: boolean) => void;
  setSettingsModalOpen: (open: boolean) => void;
  setSelectedClientId: (id: string | null) => void;
  setSelectedEntityId: (id: string | null) => void;
  createClientModalOpen: boolean;
  setCreateClientModalOpen: (open: boolean) => void;
  clientSettingsModalOpen: boolean;
  setClientSettingsModalOpen: (open: boolean) => void;
} {
  // State
  const [clientSummaries, setClientSummaries] = useState<ClientSummary[]>([]);
  const [pagination, setPagination] = useState<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedClients, setExpandedClients] = useState<Record<string, boolean>>({});

  // Filters
  const [filters, setFiltersState] = useState<DashboardFilters>({
    clientFilter: '',
    statusFilter: 'all',
    page: 1,
    pageSize: 5
  });

  // Modal states
  const [connectModalOpen, setConnectModalOpen] = useState(false);
  const [settingsModalOpen, setSettingsModalOpen] = useState(false);
  const [createClientModalOpen, setCreateClientModalOpen] = useState(false);
  const [clientSettingsModalOpen, setClientSettingsModalOpen] = useState(false);
  const [selectedClientId, setSelectedClientId] = useState<string | null>(null);
  const [selectedEntityId, setSelectedEntityId] = useState<string | null>(null);

  // Operation states for enhanced user feedback
  const disconnectState = useXeroOperationState(30);
  const connectState = useXeroOperationState(45);
  const [operatingEntityId, setOperatingEntityId] = useState<string | null>(null);

  // Auto-dismiss success notifications after 3 seconds
  useEffect(() => {
    if (disconnectState.isSuccess) {
      const timer = setTimeout(() => {
        disconnectState.resetOperation();
        setOperatingEntityId(null);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [disconnectState.isSuccess]);

  useEffect(() => {
    if (connectState.isSuccess) {
      const timer = setTimeout(() => {
        connectState.resetOperation();
        setOperatingEntityId(null);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [connectState.isSuccess]);

  // Fetch data function - wrap in useCallback to prevent infinite loops
  const refreshData = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const data = await fetchFirmClientSummary(firmId, filters);
      setClientSummaries(data.clients);
      setPagination(data.pagination);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  }, [firmId, filters.page, filters.pageSize, filters.clientFilter, filters.statusFilter]);

  // Effect to refresh data when filters change - use individual filter properties
  useEffect(() => {
    refreshData();
  }, [refreshData]);

  // Filter update function - wrap in useCallback
  const setFilters = useCallback((newFilters: Partial<DashboardFilters>) => {
    setFiltersState(prev => ({ ...prev, ...newFilters }));
  }, []);

  // Action handlers - wrap in useCallback to prevent unnecessary re-renders
  const handleClientClick = useCallback((clientId: string) => {
    alert(`Navigate to client dashboard: ${clientId}`);
    // In real app, this would likely trigger navigation/route change
  }, []);

  const handleAddNewClient = useCallback(() => {
    setCreateClientModalOpen(true);
  }, []);

  const handleClientSettings = useCallback((clientId: string) => {
    setSelectedClientId(clientId);
    setClientSettingsModalOpen(true);
  }, []);

  const handleDisconnectEntity = useCallback(async (clientId: string, entityId: string) => {
    try {
      if (confirm('Are you sure you want to disconnect this entity? This will stop data synchronization.')) {
        // Start the disconnect operation
        setOperatingEntityId(entityId);
        disconnectState.startOperation('disconnect');

        await EntitiesService.disconnectEntity(entityId);

        // Complete the operation successfully
        disconnectState.completeOperation(true);

        // Show success toast
        toast.success('Entity disconnected successfully', {
          description: 'Data synchronization has been stopped for this entity.'
        });

        // Refresh data to show updated status
        await refreshData();
      }
    } catch (error) {
      console.error('Error disconnecting entity:', error);
      disconnectState.completeOperation(false);

      // Show error toast
      toast.error('Failed to disconnect entity', {
        description: 'Please try again or contact support if the problem persists.'
      });
    } finally {
      setOperatingEntityId(null);
    }
  }, [refreshData, disconnectState]);

  const handleFixConnection = useCallback(async (clientId: string, entityId: string) => {
    try {
      // Start the connect operation
      setOperatingEntityId(entityId);
      connectState.startOperation('reconnect');

      // Get entity details to determine the type
      const entity = await EntitiesService.getEntity(entityId);

      if (entity.type === 'xero') {
        // For Xero, initiate a new connection
        const authUrl = await EntitiesService.initiateXeroConnection(clientId);
        connectState.completeOperation(true);

        // Show success toast
        toast.success('Redirecting to Xero for authentication', {
          description: 'Please complete the authorization process in the new window.'
        });

        window.location.href = authUrl;
      } else if (entity.type === 'qbo') {
        connectState.completeOperation(false);

        // Show info toast for QBO
        toast.info('QuickBooks Online reconnection coming soon!', {
          description: 'This feature will be available in a future update.'
        });
      } else {
        connectState.completeOperation(false);

        // Show info toast for manual entities
        toast.info('Manual entities do not require connection fixes', {
          description: 'This entity is configured for manual data entry.'
        });
      }
    } catch (error) {
      console.error('Error fixing connection:', error);
      connectState.completeOperation(false);

      // Show error toast
      toast.error('Failed to fix connection', {
        description: 'Please try again or contact support if the problem persists.'
      });
    } finally {
      setOperatingEntityId(null);
    }
  }, [connectState]);

  const handleConnectNewEntity = useCallback((clientId: string) => {
    setSelectedClientId(clientId);
    setConnectModalOpen(true);
  }, []);

  const handleEntitySettings = useCallback((clientId: string, entityId: string) => {
    setSelectedClientId(clientId);
    setSelectedEntityId(entityId);
    setSettingsModalOpen(true);
  }, []);

  const handleInitiateConnection = useCallback(async (clientId: string, provider: 'xero' | 'qbo') => {
    try {
      if (provider === 'xero') {
        const authUrl = await EntitiesService.initiateXeroConnection(clientId);

        // Show success toast
        toast.success('Redirecting to Xero for connection', {
          description: 'Please complete the authorization process to connect your Xero account.'
        });

        window.location.href = authUrl;
      } else {
        // Show info toast for QBO
        toast.info('QuickBooks Online integration coming soon!', {
          description: 'This feature will be available in a future update.'
        });
      }
    } catch (error) {
      console.error('Error initiating connection:', error);

      // Show error toast
      toast.error('Failed to initiate connection', {
        description: 'Please try again or contact support if the problem persists.'
      });
    }
  }, []);

  const toggleClientExpansion = useCallback((clientId: string) => {
    setExpandedClients(prev => ({
      ...prev,
      [clientId]: !prev[clientId]
    }));
  }, []);

  return {
    // State
    clientSummaries,
    pagination,
    isLoading,
    error,
    expandedClients,
    connectModalOpen,
    settingsModalOpen,
    createClientModalOpen,
    clientSettingsModalOpen,
    selectedClientId,
    selectedEntityId,

    // Operation states
    disconnectState,
    connectState,
    operatingEntityId,

    // Actions
    handleClientClick,
    handleAddNewClient,
    handleClientSettings,
    handleDisconnectEntity,
    handleFixConnection,
    handleConnectNewEntity,
    handleEntitySettings,
    handleInitiateConnection,
    toggleClientExpansion,

    // Filters and utilities
    filters,
    setFilters,
    refreshData,

    // Modal state setters
    setConnectModalOpen,
    setSettingsModalOpen,
    setCreateClientModalOpen,
    setSelectedClientId,
    setSelectedEntityId,
    setClientSettingsModalOpen
  };
}