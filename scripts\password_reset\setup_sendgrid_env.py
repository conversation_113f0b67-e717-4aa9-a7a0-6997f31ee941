#!/usr/bin/env python3
"""
Helper script to set up SendGrid environment variables and test email sending.

This script will help you configure and test your SendGrid integration.
"""

import os
import sys
import asyncio
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def setup_environment():
    """Set up SendGrid environment variables"""
    
    logger.info("SendGrid Environment Setup")
    logger.info("=" * 40)
    
    # Check if API key is already set
    current_api_key = os.getenv('SENDGRID_API_KEY')
    if current_api_key and current_api_key != "your-actual-sendgrid-api-key-here":
        logger.info(f"✅ SENDGRID_API_KEY already set: {current_api_key[:10]}...")
    else:
        logger.error("❌ SENDGRID_API_KEY not properly set")
        logger.info("\nTo set your SendGrid API key, run one of these commands:")
        logger.info("\nPowerShell:")
        logger.info('$env:SENDGRID_API_KEY = "SG.your-actual-api-key-here"')
        logger.info("\nCommand Prompt:")
        logger.info('set SENDGRID_API_KEY=SG.your-actual-api-key-here')
        logger.info("\nBash/Linux:")
        logger.info('export SENDGRID_API_KEY="SG.your-actual-api-key-here"')
        logger.info("\nReplace 'SG.your-actual-api-key-here' with your real SendGrid API key.")
        logger.info("You can get your API key from: https://app.sendgrid.com/settings/api_keys")
        return False
    
    # Set optional environment variables with defaults
    env_vars = {
        'SENDGRID_FROM_EMAIL': '<EMAIL>',
        'SENDGRID_FROM_NAME': 'DRCR Labs',
        'FRONTEND_BASE_URL': 'https://app.drcrlabs.com',
        'PASSWORD_RESET_URL_PATH': '/reset-password'
    }
    
    for key, default_value in env_vars.items():
        current_value = os.getenv(key)
        if not current_value:
            os.environ[key] = default_value
            logger.info(f"📝 Set {key} = {default_value}")
        else:
            logger.info(f"✅ {key} = {current_value}")
    
    # Template ID is optional
    template_id = os.getenv('SENDGRID_TEMPLATE_ID_PASSWORD_RESET')
    if template_id:
        logger.info(f"📋 SENDGRID_TEMPLATE_ID_PASSWORD_RESET = {template_id}")
    else:
        logger.info("⚠️  SENDGRID_TEMPLATE_ID_PASSWORD_RESET not set (will use custom HTML)")
    
    return True

async def test_email_sending():
    """Test sending an <NAME_EMAIL>"""
    
    try:
        # Add the rest_api directory to the path
        sys.path.append(os.path.join(os.path.dirname(__file__), 'rest_api'))
        
        from rest_api.services.email_service import EmailService
        
        # Create email service instance
        email_service = EmailService()
        
        # Test email details
        test_email = "<EMAIL>"
        test_user_name = "Test User"
        test_reset_link = "https://app.drcrlabs.com/reset-password?token=test_token_123456789"
        
        logger.info(f"\n📧 Sending test password reset email to {test_email}...")
        
        # Send the email
        success = await email_service.send_password_reset_email(
            to_email=test_email,
            user_name=test_user_name,
            reset_link=test_reset_link,
            expiry_time="24 hours"
        )
        
        if success:
            logger.info(f"✅ Test email sent successfully to {test_email}")
            logger.info("📬 Please check the <NAME_EMAIL>")
            logger.info("📱 Also check the spam/junk folder if you don't see it")
            return True
        else:
            logger.error(f"❌ Failed to send test email to {test_email}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error sending test email: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main function"""
    
    logger.info("DRCR SendGrid Setup and Test")
    logger.info("=" * 50)
    
    # Set up environment
    if not setup_environment():
        logger.error("\n❌ Environment setup failed. Please set your SendGrid API key first.")
        return False
    
    logger.info("\n🧪 Testing email sending...")
    success = await test_email_sending()
    
    if success:
        logger.info("\n🎉 Email test completed successfully!")
        logger.info("\nNext steps:")
        logger.info("1. ✅ SendGrid integration is working")
        logger.info("2. 📧 Check <EMAIL> for the test email")
        logger.info("3. 🔧 Optionally create a SendGrid template for better styling")
        logger.info("4. 🖥️  Implement frontend password reset components")
    else:
        logger.error("\n❌ Email test failed. Please check your SendGrid configuration.")
        logger.info("\nTroubleshooting:")
        logger.info("1. Verify your SendGrid API key is correct")
        logger.info("2. Check that your SendGrid account is active")
        logger.info("3. Ensure your from email is verified in SendGrid")
        logger.info("4. Check SendGrid activity logs for more details")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1) 