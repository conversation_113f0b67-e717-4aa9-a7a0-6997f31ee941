import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Search, PlusCircle } from 'lucide-react';
import type { DashboardFilters } from '../types';

interface DashboardFiltersProps {
  filters: DashboardFilters;
  onFiltersChange: (filters: Partial<DashboardFilters>) => void;
  onAddNewClient: () => void;
  isAdmin?: boolean;
}

export function DashboardFiltersComponent({ 
  filters, 
  onFiltersChange, 
  onAddNewClient, 
  isAdmin = true 
}: DashboardFiltersProps) {
  return (
    <div className="flex flex-col sm:flex-row gap-4 items-center p-4 border rounded-lg bg-white shadow-sm flex-shrink-0">
      <div className="relative w-full sm:w-auto flex-grow sm:flex-grow-0">
        <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
        <Input
          type="search"
          placeholder="Search clients / groups..."
          value={filters.clientFilter}
          onChange={(e) => onFiltersChange({ clientFilter: e.target.value })}
          className="pl-10 sm:w-[350px] h-10"
        />
      </div>
      <div className="flex items-center space-x-2 sm:ml-auto w-full sm:w-auto justify-between sm:justify-end">
        {isAdmin && (
          <Button onClick={onAddNewClient} size="default">
            <PlusCircle className="mr-2 h-4 w-4" /> Add New Client
          </Button>
        )}
        <div className="flex items-center space-x-2">
          <label htmlFor="status-filter" className="text-sm font-medium whitespace-nowrap sr-only">
            Status:
          </label>
          <Select 
            value={filters.statusFilter} 
            onValueChange={(value) => onFiltersChange({ statusFilter: value })}
          >
            <SelectTrigger className="w-full sm:w-[200px] h-10">
              <SelectValue placeholder="Filter by status..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="action_needed">Action Needed</SelectItem>
              <SelectItem value="error">Error</SelectItem>
              <SelectItem value="ok">OK</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
} 