#!/bin/sh
# <PERSON>ript for preparing backend deployment environment

echo "Current directory: $(pwd)"
echo "Listing root directory:"
ls -la

# Ensure GCP authentication is set up
if [ -z "$GCP_SERVICE_KEY" ]; then
  echo "Error: GCP_SERVICE_KEY is not set"
  exit 1
fi

# Decode and save the GCP service account key
echo "$GCP_SERVICE_KEY" | base64 -d > gcp-key.json
if [ ! -s gcp-key.json ]; then
  echo "Error: Failed to decode GCP_SERVICE_KEY"
  exit 1
fi

# Authenticate with GCP
gcloud auth activate-service-account --key-file gcp-key.json
gcloud config set project drcr-d660a
gcloud auth configure-docker gcr.io,us-docker.pkg.dev

# Create backend directory structure
mkdir -p backend
cp -r app/* backend/ || true
cp -r rest_api/* backend/ || true
cp requirements.txt backend/ || true
cp Dockerfile backend/ || true

echo "✅ Backend deployment environment prepared successfully!"
