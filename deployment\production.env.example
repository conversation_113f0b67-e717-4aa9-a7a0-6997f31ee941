# Production Environment Configuration
# Copy this to production.env and update values for your deployment

# Enable Secret Manager loading (set to "true" for production)
LOAD_SECRETS_FROM_SECRET_MANAGER=true

# Google Cloud Configuration
GCP_PROJECT_ID=your-production-project-id
GCP_REGION=your-production-region
GCS_BUCKET_NAME=your-production-bucket

# Firebase Configuration
FIREBASE_CREDENTIALS_PATH=/path/to/production/service-account.json

# API Configuration
API_HOST=0.0.0.0
API_PORT=8080
API_DEBUG=false

# PubSub Configuration
PUBSUB_TOPIC_XERO_SYNC=xero-sync-topic
PUBSUB_SUBSCRIPTION_XERO_SYNC=xero-sync-subscription

# The following secrets will be loaded from Secret Manager when LOAD_SECRETS_FROM_SECRET_MANAGER=true
# They should NOT be set in this file for production - they will be loaded automatically

# XERO_CLIENT_ID=loaded-from-secret-manager
# XERO_CLIENT_SECRET=loaded-from-secret-manager
# XERO_REDIRECT_URI=loaded-from-secret-manager
# OPENAI_API_KEY=loaded-from-secret-manager
# MISTRAL_API_KEY=loaded-from-secret-manager
# TOKEN_ENCRYPTION_KEY=loaded-from-secret-manager
# SECRET_KEY=loaded-from-secret-manager

# Local development overrides (these will take precedence over Secret Manager if set)
# Uncomment for local development:
# XERO_CLIENT_ID=your-dev-client-id
# XERO_CLIENT_SECRET=your-dev-client-secret
# XERO_REDIRECT_URI=http://localhost:8080/callback
# OPENAI_API_KEY=your-dev-openai-key
# MISTRAL_API_KEY=your-dev-mistral-key
# TOKEN_ENCRYPTION_KEY=your-dev-encryption-key
# SECRET_KEY=your-dev-secret-key 