# 🚀 Full-Stack Quick Reference - DRCR Application

## **One-Time Setup** (15 minutes)

### 1. **Organize Your Repository**
```bash
# Create the expected structure
mkdir your-gitlab-repo
cd your-gitlab-repo

# Move your projects
mv /path/to/drcr_front frontend/
mv /path/to/drcr_back backend/

# Copy the pipeline files
cp frontend/.gitlab-ci.yml .
cp frontend/FULLSTACK_CICD_SETUP.md .
```

### 2. **Get Firebase Token**
```bash
cd frontend
npm install -g firebase-tools
firebase login:ci
# Copy the token
```

### 3. **Get Google Cloud Service Key**
```bash
# Create service account
gcloud iam service-accounts create gitlab-ci \
    --project=drcr-d660a

# Grant permissions
gcloud projects add-iam-policy-binding drcr-d660a \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/run.admin"

# Create key and encode
gcloud iam service-accounts keys create gcp-key.json \
    --iam-account=<EMAIL>
base64 gcp-key.json > gcp-key-base64.txt
```

### 4. **Add GitLab Variables**
Go to: **Project Settings** → **CI/CD** → **Variables**

| Variable | Value | Protected | Masked |
|----------|-------|-----------|---------|
| `FIREBASE_TOKEN` | Your Firebase token | ✅ | ✅ |
| `GCP_SERVICE_KEY` | Base64 encoded key | ✅ | ✅ |

### 5. **Push to GitLab**
```bash
git add .
git commit -m "Add full-stack CI/CD pipeline"
git push origin main
```

## **Daily Workflow**

### 🔧 **Feature Development**
```bash
# 1. Create feature branch
git checkout -b feature/new-dashboard-api

# 2. Work on both frontend and backend
# Frontend: Edit frontend/src/features/dashboard/...
# Backend: Edit backend/rest_api/routes/...

# 3. Push and create MR
git add .
git commit -m "Add new dashboard API with optimized frontend"
git push origin feature/new-dashboard-api

# 4. In GitLab: Create merge request
# 5. Manually trigger preview deployment (frontend only)
```

### 🧪 **Staging Deployment**
```bash
# 1. Merge to develop
git checkout develop
git merge feature/new-dashboard-api
git push origin develop

# 2. In GitLab pipeline:
# - Manually trigger "deploy_frontend_staging"
# - Manually trigger "deploy_backend_staging"
```

### 🚀 **Production Deployment**
```bash
# 1. Merge to main
git checkout main
git merge develop
git push origin main

# 2. In GitLab pipeline:
# - Manually trigger "deploy_frontend_production"
# - Manually trigger "deploy_backend_production"
# - Manually trigger "deploy_fullstack_production" (coordinates both)
```

## **Pipeline Overview**

| Stage | Frontend Jobs | Backend Jobs |
|-------|---------------|--------------|
| **Install** | `install_frontend` | `install_backend` |
| **Test** | `test_frontend` (lint) | `test_backend` (pytest) |
| **Build** | `build_frontend` (Vite) | `build_backend` (Docker) |
| **Deploy** | Firebase Hosting | Google Cloud Run |

## **Deployment Environments**

| Environment | Frontend URL | Backend URL | Trigger |
|-------------|--------------|-------------|---------|
| **Preview** | `preview-[MR-ID].web.app` | ❌ | Manual (MR) |
| **Staging** | `drcr-d660a--staging.web.app` | `drcr-backend-staging.run.app` | Manual (`develop`) |
| **Production** | `drcr-d660a.web.app` | `drcr-backend-prod.run.app` | Manual (`main`) |

## **Quick Commands**

### **Local Testing**
```bash
# Frontend
cd frontend
npm run build
npm run lint

# Backend  
cd backend
python -m pytest tests/
docker build -t test-backend .
```

### **GitLab Pipeline**
```bash
# View pipelines: GitLab → CI/CD → Pipelines
# Trigger deployment: Click pipeline → Deploy stage → Play button
# Download artifacts: Click job → Download
```

## **Expected Build Output**

### **Frontend Bundle Analysis:**
```
📊 Frontend bundle analysis:
  react-vendor-[hash].js: ~326 KB
  firebase-[hash].js: ~164 KB
  index-[hash].js: ~124 KB
  data-[hash].js: ~35 KB
```

### **Backend Docker Image:**
```
🐳 Building backend Docker image...
✅ Backend Docker image built
📦 Image: registry.gitlab.com/your-project/backend:latest
```

## **Troubleshooting**

| Issue | Quick Fix |
|-------|-----------|
| Frontend build fails | `cd frontend && npm run build` |
| Backend tests fail | `cd backend && python -m pytest` |
| Firebase deploy fails | Regenerate `FIREBASE_TOKEN` |
| Cloud Run deploy fails | Check `GCP_SERVICE_KEY` |
| Docker build fails | Check `backend/requirements.txt` |

## **Performance Monitoring**

After deployment, verify:

### **Frontend (Firebase Hosting):**
- ✅ Bundle sizes optimized
- ✅ Loading speed <2s
- ✅ All routes working
- ✅ No console errors

### **Backend (Cloud Run):**
- ✅ Health check passing
- ✅ API responses <300ms
- ✅ Auto-scaling working
- ✅ Logs clean

### **Integration:**
- ✅ Frontend → Backend API calls working
- ✅ Authentication flow working
- ✅ CORS configured properly

## **Advanced Usage**

### **Environment-Specific Configuration:**
```bash
# Frontend environment variables
# frontend/.env.production
VITE_API_URL=https://drcr-backend-prod.run.app

# frontend/.env.staging  
VITE_API_URL=https://drcr-backend-staging.run.app
```

### **Database Migrations:**
Add to `.gitlab-ci.yml`:
```yaml
migrate_production:
  stage: deploy
  script:
    - cd backend
    - python manage.py migrate
  only:
    - main
  when: manual
```

### **Rollback Strategy:**
```bash
# Frontend: Use Firebase Hosting rollback
firebase hosting:clone source-site-id:source-channel-id target-site-id:live

# Backend: Deploy previous Docker image
gcloud run deploy drcr-backend-prod --image=previous-image-tag
```

## **Success Metrics**

Your full-stack pipeline provides:

- ✅ **Automated builds** for both frontend & backend
- ✅ **Quality gates** (linting, tests, coverage)
- ✅ **Multi-environment deployments**
- ✅ **Performance optimization** (bundle analysis, Docker layers)
- ✅ **Coordinated deployments** (full-stack orchestration)
- ✅ **Security** (masked secrets, minimal permissions)
- ✅ **Monitoring** (health checks, logs, metrics)

---

**🎯 Result:** Your optimized DRCR application automatically deployed with:
- **Frontend**: Optimized React bundle with code splitting
- **Backend**: Containerized FastAPI with auto-scaling
- **Integration**: Seamless full-stack deployment coordination

**Next:** Create your first merge request and watch the magic happen! 🚀 