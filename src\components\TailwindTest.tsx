import React from 'react';

export function TailwindTest() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-slate-100">
      <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
        <h1 className="text-2xl font-bold text-slate-800 mb-4">Tailwind CSS Test</h1>
        <p className="text-slate-600 mb-6">
          This component is using Tailwind CSS classes. If you can see proper styling, Tailwind is working!
        </p>
        <div className="space-y-4">
          <div className="p-4 bg-blue-100 text-blue-700 rounded-md">
            This is a blue info box
          </div>
          <div className="p-4 bg-green-100 text-green-700 rounded-md">
            This is a green success box
          </div>
          <div className="p-4 bg-yellow-100 text-yellow-700 rounded-md">
            This is a yellow warning box
          </div>
          <div className="p-4 bg-red-100 text-red-700 rounded-md">
            This is a red error box
          </div>
        </div>
        <button className="mt-6 w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors">
          Click Me
        </button>
      </div>
    </div>
  );
}
