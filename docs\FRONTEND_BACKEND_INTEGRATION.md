# Frontend-Backend Authentication Integration

This document describes the integration between the React/TypeScript frontend and the FastAPI backend for authentication.

## Overview

The DRCR application uses Firebase Authentication for user management with a FastAPI backend that validates Firebase JWT tokens. The frontend handles user authentication through Firebase, and the backend validates these tokens for API access.

## Architecture

### Frontend (React/TypeScript)
- **Firebase Authentication**: Handles user sign-in/sign-up
- **Zustand Store**: Manages authentication state
- **Axios Client**: Automatically includes Firebase ID tokens in API requests
- **Protected Routes**: Redirects unauthenticated users to login

### Backend (FastAPI)
- **Firebase Admin SDK**: Validates Firebase ID tokens
- **JWT Bearer Authentication**: Expects `Authorization: Bearer <firebase-id-token>` header
- **User Profile Management**: Fetches user roles and permissions from Firestore

## Setup Instructions

### 1. Firebase Configuration

1. Create a Firebase project at [Firebase Console](https://console.firebase.google.com/)
2. Enable Authentication with Email/Password provider
3. Get your Firebase configuration from Project Settings > General > Your apps
4. Update the `.env` file with your Firebase configuration:

```env
# Firebase Configuration
VITE_FIREBASE_API_KEY=your-api-key
VITE_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=*********
VITE_FIREBASE_APP_ID=1:*********:web:abcdef123456
```

### 2. Backend Configuration

Ensure your backend is configured with the same Firebase project:
- Set `GCP_PROJECT_ID` environment variable to match your Firebase project ID
- Configure Firebase Admin SDK with appropriate service account credentials

### 3. API Base URL

Update the API base URL in `.env` if your backend runs on a different port:

```env
VITE_API_BASE_URL=http://localhost:8080
```

## Authentication Flow

### Login Process

1. User enters email/password on login page
2. Frontend calls Firebase `signInWithEmailAndPassword`
3. Firebase returns user credentials with ID token
4. Frontend stores user state and redirects to dashboard
5. Subsequent API calls include the ID token in Authorization header
6. Backend validates token and returns user profile data

### API Request Flow

1. Frontend makes API request using the `api` client
2. Request interceptor automatically adds `Authorization: Bearer <token>` header
3. Backend validates the Firebase ID token
4. Backend fetches user profile from Firestore (FIRM_USERS or CLIENT_USERS)
5. Backend returns requested data with user context

### Error Handling

- **Invalid Credentials**: User-friendly error messages for common Firebase errors
- **Token Expiration**: Automatic redirect to login page on 401 responses
- **Network Errors**: Graceful handling of connection issues

## Key Components

### Frontend

- **`src/pages/ShadcnLoginPage.tsx`**: Main login page with Shadcn/UI components
- **`src/store/auth.store.ts`**: Zustand store for authentication state
- **`src/services/auth.service.ts`**: Firebase authentication service wrapper
- **`src/lib/api.ts`**: Axios client with automatic token injection
- **`src/components/auth/ProtectedRoute.tsx`**: Route protection component

### Backend

- **`rest_api/core/firebase_auth.py`**: Firebase token validation and user profile fetching
- **`rest_api/routes/auth.py`**: Authentication-related API endpoints
- **`rest_api/models/auth.py`**: User and authentication data models

## Testing the Integration

### Prerequisites

1. Firebase project configured with Email/Password authentication
2. Backend running on `http://localhost:8080`
3. Frontend environment variables configured

### Test Steps

1. Start the backend server
2. Start the frontend development server
3. Navigate to `/login`
4. Create a test user in Firebase Console or use existing credentials
5. Attempt to log in with valid credentials
6. Verify successful redirect to dashboard
7. Check that API calls include proper Authorization headers
8. Test logout functionality

### Test Credentials

For development, you can create test users in the Firebase Console:
- Go to Authentication > Users
- Add a new user with email/password
- Use these credentials to test the login flow

## Security Considerations

- Firebase ID tokens are automatically refreshed by the Firebase SDK
- Tokens are included in API requests via secure HTTP headers
- Backend validates token signature, expiration, and project ID
- User sessions are managed by Firebase Authentication
- Logout clears local authentication state

## Troubleshooting

### Common Issues

1. **"Firebase configuration not found"**
   - Verify `.env` file has correct Firebase configuration
   - Ensure environment variables start with `VITE_`

2. **"401 Unauthorized" on API calls**
   - Check that Firebase user is authenticated
   - Verify backend Firebase configuration matches frontend
   - Ensure API base URL is correct

3. **"Network request failed"**
   - Verify backend server is running
   - Check API base URL in `.env`
   - Ensure CORS is configured on backend

4. **Login page shows Firebase errors**
   - Verify Firebase project has Email/Password authentication enabled
   - Check Firebase configuration values
   - Ensure user exists in Firebase Authentication

### Debug Tips

- Check browser console for authentication errors
- Verify network requests include Authorization header
- Check backend logs for token validation errors
- Use Firebase Console to manage test users

## Next Steps

- Implement password reset functionality
- Add user profile management
- Implement role-based access control
- Add social authentication providers
- Implement refresh token handling
