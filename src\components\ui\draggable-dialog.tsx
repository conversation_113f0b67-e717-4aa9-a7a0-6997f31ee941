import * as React from "react"
import * as DialogPrimitive from "@radix-ui/react-dialog"
import { X, Maximize2, Minimize2 } from "lucide-react"
import { cn } from "@/lib/utils"

const DraggableDialog = DialogPrimitive.Root
const DraggableDialogTrigger = DialogPrimitive.Trigger
const DraggableDialogPortal = DialogPrimitive.Portal
const DraggableDialogClose = DialogPrimitive.Close

const DraggableDialogOverlay = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Overlay
    ref={ref}
    className={cn(
      "fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
      className
    )}
    {...props}
  />
))
DraggableDialogOverlay.displayName = DialogPrimitive.Overlay.displayName

interface DraggableDialogContentProps
  extends React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content> {
  defaultWidth?: number
  defaultHeight?: number
  minWidth?: number
  minHeight?: number
  maxWidth?: number
  maxHeight?: number
}

const DraggableDialogContent = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  DraggableDialogContentProps
>(({ 
  className, 
  children, 
  defaultWidth = 1200, 
  defaultHeight = 800,
  minWidth = 300,
  minHeight = 200,
  maxWidth = window.innerWidth - 40,
  maxHeight = window.innerHeight - 40,
  ...props 
}, ref) => {
  const [position, setPosition] = React.useState({ x: 0, y: 0 })
  const [size, setSize] = React.useState({ width: defaultWidth, height: defaultHeight })
  const [isMaximized, setIsMaximized] = React.useState(true)
  const [isDragging, setIsDragging] = React.useState(false)
  const [isResizing, setIsResizing] = React.useState(false)

  // Use refs for real-time tracking during drag/resize operations
  const currentPosition = React.useRef({ x: 0, y: 0 })
  const currentSize = React.useRef({ width: defaultWidth, height: defaultHeight })
  const dragStart = React.useRef({ x: 0, y: 0 })
  const resizeStart = React.useRef({ x: 0, y: 0, width: 0, height: 0 })
  const resizeHandle = React.useRef<string>("")
  const animationFrame = React.useRef<number | null>(null)

  const contentRef = React.useRef<HTMLDivElement>(null)

  // Sync refs with state
  React.useEffect(() => {
    currentPosition.current = position
  }, [position])

  React.useEffect(() => {
    currentSize.current = size
  }, [size])

  // Center dialog on mount or set to maximized
  React.useEffect(() => {
    if (contentRef.current) {
      if (isMaximized) {
        // Start maximized - full screen
        const newPosition = { x: 0, y: 0 }
        const newSize = { width: window.innerWidth, height: window.innerHeight }
        setPosition(newPosition)
        setSize(newSize)
        currentPosition.current = newPosition
        currentSize.current = newSize
      } else {
        // Center the dialog
        const rect = contentRef.current.getBoundingClientRect()
        const newPosition = {
          x: (window.innerWidth - rect.width) / 2,
          y: (window.innerHeight - rect.height) / 2
        }
        setPosition(newPosition)
        currentPosition.current = newPosition
      }
    }
  }, [isMaximized])

  const updateTransform = React.useCallback(() => {
    if (contentRef.current && !isMaximized) {
      const { x, y } = currentPosition.current
      const { width, height } = currentSize.current
      contentRef.current.style.transform = `translate(${x}px, ${y}px)`
      contentRef.current.style.width = `${width}px`
      contentRef.current.style.height = `${height}px`
    }
  }, [isMaximized])

  const handleMouseDown = React.useCallback((e: React.MouseEvent, action: 'drag' | 'resize', handle?: string) => {
    e.preventDefault()
    e.stopPropagation()

    if (action === 'drag') {
      setIsDragging(true)
      dragStart.current = {
        x: e.clientX - currentPosition.current.x,
        y: e.clientY - currentPosition.current.y
      }
    } else if (action === 'resize' && handle) {
      setIsResizing(true)
      resizeHandle.current = handle
      resizeStart.current = {
        x: e.clientX,
        y: e.clientY,
        width: currentSize.current.width,
        height: currentSize.current.height
      }
    }

    // Add cursor styles for better UX
    if (action === 'drag') {
      document.body.style.cursor = 'grabbing'
    } else if (action === 'resize') {
      document.body.style.userSelect = 'none'
    }
  }, [])

  const handleMouseMove = React.useCallback((e: MouseEvent) => {
    if (animationFrame.current) {
      cancelAnimationFrame(animationFrame.current)
    }

    animationFrame.current = requestAnimationFrame(() => {
      if (isDragging) {
        const newX = Math.max(0, Math.min(window.innerWidth - currentSize.current.width, e.clientX - dragStart.current.x))
        const newY = Math.max(0, Math.min(window.innerHeight - currentSize.current.height, e.clientY - dragStart.current.y))
        
        currentPosition.current = { x: newX, y: newY }
        updateTransform()
      } else if (isResizing) {
        const deltaX = e.clientX - resizeStart.current.x
        const deltaY = e.clientY - resizeStart.current.y
        
        let newWidth = resizeStart.current.width
        let newHeight = resizeStart.current.height
        let newX = currentPosition.current.x
        let newY = currentPosition.current.y

        if (resizeHandle.current.includes('right')) {
          newWidth = Math.max(minWidth, Math.min(maxWidth, resizeStart.current.width + deltaX))
        }
        if (resizeHandle.current.includes('left')) {
          newWidth = Math.max(minWidth, Math.min(maxWidth, resizeStart.current.width - deltaX))
          newX = currentPosition.current.x + (resizeStart.current.width - newWidth)
        }
        if (resizeHandle.current.includes('bottom')) {
          newHeight = Math.max(minHeight, Math.min(maxHeight, resizeStart.current.height + deltaY))
        }
        if (resizeHandle.current.includes('top')) {
          newHeight = Math.max(minHeight, Math.min(maxHeight, resizeStart.current.height - deltaY))
          newY = currentPosition.current.y + (resizeStart.current.height - newHeight)
        }

        currentSize.current = { width: newWidth, height: newHeight }
        currentPosition.current = { x: newX, y: newY }
        updateTransform()
      }
    })
  }, [isDragging, isResizing, minWidth, minHeight, maxWidth, maxHeight, updateTransform])

  const handleMouseUp = React.useCallback(() => {
    if (animationFrame.current) {
      cancelAnimationFrame(animationFrame.current)
    }

    // Reset cursor styles
    document.body.style.cursor = ''
    document.body.style.userSelect = ''

    // Update state with final values
    if (isDragging || isResizing) {
      setPosition(currentPosition.current)
      setSize(currentSize.current)
    }

    setIsDragging(false)
    setIsResizing(false)
    resizeHandle.current = ""
  }, [isDragging, isResizing])

  React.useEffect(() => {
    if (isDragging || isResizing) {
      document.addEventListener('mousemove', handleMouseMove, { passive: true })
      document.addEventListener('mouseup', handleMouseUp)
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
        if (animationFrame.current) {
          cancelAnimationFrame(animationFrame.current)
        }
      }
    }
  }, [isDragging, isResizing, handleMouseMove, handleMouseUp])

  const toggleMaximize = () => {
    setIsMaximized(!isMaximized)
    if (!isMaximized) {
      const newPosition = { x: 0, y: 0 }
      const newSize = { width: window.innerWidth, height: window.innerHeight }
      setPosition(newPosition)
      setSize(newSize)
      currentPosition.current = newPosition
      currentSize.current = newSize
    } else {
      const newPosition = { x: (window.innerWidth - defaultWidth) / 2, y: (window.innerHeight - defaultHeight) / 2 }
      const newSize = { width: defaultWidth, height: defaultHeight }
      setPosition(newPosition)
      setSize(newSize)
      currentPosition.current = newPosition
      currentSize.current = newSize
    }
  }

  const dialogStyle = React.useMemo(() => {
    if (isMaximized) {
      return { width: '100vw', height: '100vh', transform: 'translate(0, 0)' }
    }
    return { 
      width: `${size.width}px`, 
      height: `${size.height}px`, 
      transform: `translate(${position.x}px, ${position.y}px)`,
      transition: isDragging || isResizing ? 'none' : 'transform 0.2s ease-out'
    }
  }, [isMaximized, size, position, isDragging, isResizing])

  return (
    <DraggableDialogPortal>
      <DraggableDialogOverlay />
      <DialogPrimitive.Content
        ref={contentRef}
        className={cn(
          "fixed z-50 bg-background shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 border rounded-lg overflow-hidden",
          "top-0 left-0",
          className
        )}
        style={dialogStyle}
        {...props}
      >
        {/* Drag Handle */}
        <div
          className={cn(
            "flex items-center justify-between p-4 border-b bg-muted/50 select-none",
            !isMaximized && "cursor-move"
          )}
          onMouseDown={(e) => !isMaximized && handleMouseDown(e, 'drag')}
        >
          <div className="flex-1" />
          <div className="flex items-center gap-2">
            <button
              onClick={toggleMaximize}
              className="p-1 hover:bg-muted rounded transition-colors"
              type="button"
            >
              {isMaximized ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
            </button>
            <DialogPrimitive.Close className="p-1 hover:bg-muted rounded transition-colors">
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </DialogPrimitive.Close>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {children}
        </div>

        {/* Resize Handles */}
        {!isMaximized && (
          <>
            {/* Corner handles */}
            <div
              className="absolute top-0 left-0 w-3 h-3 cursor-nw-resize hover:bg-primary/20 transition-colors"
              onMouseDown={(e) => handleMouseDown(e, 'resize', 'top-left')}
            />
            <div
              className="absolute top-0 right-0 w-3 h-3 cursor-ne-resize hover:bg-primary/20 transition-colors"
              onMouseDown={(e) => handleMouseDown(e, 'resize', 'top-right')}
            />
            <div
              className="absolute bottom-0 left-0 w-3 h-3 cursor-sw-resize hover:bg-primary/20 transition-colors"
              onMouseDown={(e) => handleMouseDown(e, 'resize', 'bottom-left')}
            />
            <div
              className="absolute bottom-0 right-0 w-3 h-3 cursor-se-resize hover:bg-primary/20 transition-colors"
              onMouseDown={(e) => handleMouseDown(e, 'resize', 'bottom-right')}
            />
            
            {/* Edge handles */}
            <div
              className="absolute top-0 left-3 right-3 h-1 cursor-n-resize hover:bg-primary/20 transition-colors"
              onMouseDown={(e) => handleMouseDown(e, 'resize', 'top')}
            />
            <div
              className="absolute bottom-0 left-3 right-3 h-1 cursor-s-resize hover:bg-primary/20 transition-colors"
              onMouseDown={(e) => handleMouseDown(e, 'resize', 'bottom')}
            />
            <div
              className="absolute left-0 top-3 bottom-3 w-1 cursor-w-resize hover:bg-primary/20 transition-colors"
              onMouseDown={(e) => handleMouseDown(e, 'resize', 'left')}
            />
            <div
              className="absolute right-0 top-3 bottom-3 w-1 cursor-e-resize hover:bg-primary/20 transition-colors"
              onMouseDown={(e) => handleMouseDown(e, 'resize', 'right')}
            />
          </>
        )}
      </DialogPrimitive.Content>
    </DraggableDialogPortal>
  )
})
DraggableDialogContent.displayName = "DraggableDialogContent"

const DraggableDialogHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "flex flex-col space-y-1.5 text-center sm:text-left",
      className
    )}
    {...props}
  />
)
DraggableDialogHeader.displayName = "DraggableDialogHeader"

const DraggableDialogFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 mt-6",
      className
    )}
    {...props}
  />
)
DraggableDialogFooter.displayName = "DraggableDialogFooter"

const DraggableDialogTitle = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Title
    ref={ref}
    className={cn(
      "text-lg font-semibold leading-none tracking-tight",
      className
    )}
    {...props}
  />
))
DraggableDialogTitle.displayName = DialogPrimitive.Title.displayName

const DraggableDialogDescription = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Description>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Description
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
))
DraggableDialogDescription.displayName = DialogPrimitive.Description.displayName

export {
  DraggableDialog,
  DraggableDialogPortal,
  DraggableDialogOverlay,
  DraggableDialogClose,
  DraggableDialogTrigger,
  DraggableDialogContent,
  DraggableDialogHeader,
  DraggableDialogFooter,
  DraggableDialogTitle,
  DraggableDialogDescription,
} 