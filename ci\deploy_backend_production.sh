#!/bin/sh
# Script for deploying backend to production in CI/CD pipeline

echo "Current directory: $(pwd)"
echo "Listing current directory:"
ls -la

# Create backend directory if it doesn't exist
mkdir -p backend

# Copy necessary files to backend directory
cp -r app/* backend/ || true
cp -r rest_api/* backend/ || true
cp requirements.txt backend/ || true
cp Dockerfile backend/ || true

cd backend
echo "🚀 Deploying backend to Google Cloud Run (Production)..."
docker push $CI_REGISTRY_IMAGE/backend:$CI_COMMIT_SHA
gcloud run deploy drcr-backend-prod --image $CI_REGISTRY_IMAGE/backend:$CI_COMMIT_SHA --platform managed --region us-central1 --allow-unauthenticated --set-env-vars="ENVIRONMENT=production"
echo "✅ Backend production deployment successful!"
BACKEND_URL=$(gcloud run services describe drcr-backend-prod --region=us-central1 --format='value(status.url)')
echo "🔗 Backend live at: $BACKEND_URL"
