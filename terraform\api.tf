# Create a Cloud Run service for the REST API
resource "google_cloud_run_service" "rest_api" {
  name     = "drcr-api"
  location = var.region
  
  template {
    spec {
      containers {
        image = var.api_image
        
        resources {
          limits = {
            cpu    = "1000m"
            memory = "512Mi"
          }
        }
        
        env {
          name  = "GCP_PROJECT_ID"
          value = var.project_id
        }
        
        env {
          name  = "PUBSUB_TOPIC_XERO_SYNC"
          value = var.xero_sync_topic_name
        }
        
        # Reference secrets
        env {
          name = "API_SECRET_KEY"
          value_from {
            secret_key_ref {
              name = google_secret_manager_secret.api_secret_key.secret_id
              key  = "latest"
            }
          }
        }
      }
      
      service_account_name = google_service_account.api_service_account.email
    }
  }
  
  traffic {
    percent         = 100
    latest_revision = true
  }
  
  depends_on = [
    google_project_service.services,
    google_secret_manager_secret.api_secret_key
  ]
}

# Make the Cloud Run service publicly accessible
resource "google_cloud_run_service_iam_member" "rest_api_public" {
  service  = google_cloud_run_service.rest_api.name
  location = google_cloud_run_service.rest_api.location
  role     = "roles/run.invoker"
  member   = "allUsers"
}

# Create a custom domain mapping if a domain is provided
resource "google_cloud_run_domain_mapping" "rest_api_domain" {
  count    = var.api_domain != "" ? 1 : 0
  name     = var.api_domain
  location = var.region
  
  metadata {
    namespace = var.project_id
  }
  
  spec {
    route_name = google_cloud_run_service.rest_api.name
  }
  
  depends_on = [google_cloud_run_service.rest_api]
}
