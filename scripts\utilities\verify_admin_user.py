#!/usr/bin/env python3
"""
Script <NAME_EMAIL> admin user restoration
"""

import os
from google.cloud import firestore
from firebase_admin import auth as firebase_auth
from firebase_admin import initialize_app, credentials
from dotenv import load_dotenv

def verify_admin_user():
    """Verify <EMAIL> admin user restoration"""
    
    # Load environment variables
    load_dotenv()
    
    # Initialize Firebase Admin SDK
    cred_path = os.getenv('FIREBASE_CREDENTIALS_PATH')
    if not cred_path or not os.path.exists(cred_path):
        print("❌ Firebase credentials file not found")
        return
    
    # Initialize Firebase app if not already initialized
    try:
        cred = credentials.Certificate(cred_path)
        initialize_app(cred)
    except ValueError:
        # App already initialized
        pass
    
    # Initialize Firestore client
    db = firestore.Client()
    
    # User details
    email = "<EMAIL>"
    firm_id = "83a0939b-dcbb-4725-b1db-5e0acc5e8607"
    
    print(f"🔍 Verifying admin user restoration for {email}...")
    
    try:
        # Get Firebase user
        firebase_user = firebase_auth.get_user_by_email(email)
        user_id = firebase_user.uid
        
        print(f"✅ Firebase user exists: {user_id}")
        
        # Check FIRM_USERS collection
        print(f"\n🔍 Checking FIRM_USERS collection...")
        firm_users = db.collection("FIRM_USERS")\
            .where("user_id", "==", user_id)\
            .where("firm_id", "==", firm_id)\
            .stream()
        
        user_found = False
        for user_doc in firm_users:
            user_found = True
            user_data = user_doc.to_dict()
            
            print(f"✅ Found FIRM_USERS document: {user_doc.id}")
            print(f"   Email: {user_data.get('email')}")
            print(f"   Display Name: {user_data.get('display_name')}")
            print(f"   Role: {user_data.get('role')}")
            print(f"   Status: {user_data.get('status')}")
            print(f"   Firm ID: {user_data.get('firm_id')}")
            print(f"   Created: {user_data.get('created_at')}")
            print(f"   Updated: {user_data.get('updated_at')}")
            
            # Verify admin role
            if user_data.get('role') == 'firm_admin':
                print(f"✅ User has firm_admin role")
            else:
                print(f"❌ User role is not firm_admin: {user_data.get('role')}")
            
            # Verify active status
            if user_data.get('status') == 'active':
                print(f"✅ User status is active")
            else:
                print(f"❌ User status is not active: {user_data.get('status')}")
        
        if not user_found:
            print(f"❌ No FIRM_USERS document found for user {user_id} in firm {firm_id}")
            return
        
        # Check firm exists
        print(f"\n🔍 Verifying firm exists...")
        firm_ref = db.collection("FIRMS").document(firm_id)
        firm_doc = firm_ref.get()
        
        if firm_doc.exists:
            firm_data = firm_doc.to_dict()
            print(f"✅ Firm exists: {firm_data.get('name')}")
            print(f"   Status: {firm_data.get('status')}")
        else:
            print(f"❌ Firm {firm_id} not found!")
            return
        
        print(f"\n🎉 Verification complete! User {email} is properly configured as firm_admin.")
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_admin_user() 