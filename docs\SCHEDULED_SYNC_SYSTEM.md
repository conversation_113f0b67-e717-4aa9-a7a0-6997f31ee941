# Scheduled Sync System Documentation

## Overview

The DRCR Scheduled Sync System provides automated, regular synchronization of entity data based on configurable frequencies. This system ensures that accounting data stays up-to-date without manual intervention, using Google Cloud Scheduler, Pub/Sub, and Cloud Functions.

## Architecture

```
Cloud Scheduler Jobs (Hourly/Daily/Weekly)
    ↓ (Pub/Sub Messages)
Scheduled Sync Processor (Cloud Function)
    ↓ (Queries Firestore)
Entity Settings (sync_frequency, last_sync_timestamps)
    ↓ (Triggers individual syncs)
Xero Sync Consumer (Cloud Function)
    ↓ (Updates data)
Firestore Collections (TRANSACTIONS, etc.)
```

## Components

### 1. Cloud Scheduler Jobs

Three scheduler jobs trigger syncs at different intervals:

- **Hourly Sync**: `0 * * * *` (every hour at minute 0)
- **Daily Sync**: `0 2 * * *` (every day at 2:00 AM UTC)
- **Weekly Sync**: `0 3 * * 1` (every Monday at 3:00 AM UTC)

Each job publishes a message to the `scheduled-sync-topic` with:
```json
{
  "sync_frequency": "daily",
  "trigger_type": "scheduled",
  "timestamp": "2025-01-15T02:00:00Z"
}
```

### 2. Scheduled Sync Processor

Cloud Function that:
1. Receives scheduled sync messages
2. Queries entities with matching `sync_frequency` and `auto_sync_enabled=true`
3. Checks if entities need syncing based on last sync timestamps
4. Triggers individual entity syncs via the `xero-sync-topic`

**Key Logic:**
- **Never synced**: Always sync
- **Hourly**: Sync if last sync was ≥1 hour ago
- **Daily**: Sync if last sync was ≥24 hours ago
- **Weekly**: Sync if last sync was ≥7 days ago
- **Manual**: Never auto-sync

### 3. Entity Settings Configuration

Each entity has settings in the `ENTITY_SETTINGS` collection:

```json
{
  "entity_id": "8ead108d-f6a2-41e4-b2a8-962024248a66",
  "sync_frequency": "daily",
  "auto_sync_enabled": true,
  "sync_bills": true,
  "sync_invoices": false,
  "sync_spend_money": true,
  "transaction_sync_start_date": "2025-02-01",
  "_system_lastSyncTimestampUtc_Bills": "2025-06-03T13:30:07.783169+00:00",
  "_system_lastSyncTimestampUtc_Accounts": "2025-06-03T13:29:35.499335+00:00"
}
```

### 4. Sync Timestamp Tracking

The system tracks last sync timestamps for each document type:
- `_system_lastSyncTimestampUtc_Accounts`
- `_system_lastSyncTimestampUtc_Bills`
- `_system_lastSyncTimestampUtc_Contacts`
- `_system_lastSyncTimestampUtc_Invoices`
- `_system_lastSyncTimestampUtc_JournalEntries`
- `_system_lastSyncTimestampUtc_SpendMoney`

## Deployment

### Prerequisites

1. **GCS Backend**: Terraform state stored in Google Cloud Storage
2. **Cloud Scheduler API**: Enabled in the project
3. **Service Accounts**: Proper IAM permissions for Cloud Functions

### Deploy Infrastructure

1. **Create Function Zip Files**:
   ```bash
   cd cloud_functions/scheduled_sync_processor
   zip -r ../scheduled_sync_processor.zip .
   ```

2. **Apply Terraform**:
   ```bash
   cd terraform
   terraform init
   terraform plan
   terraform apply
   ```

3. **Use Deployment Script**:
   ```bash
   python scripts/deploy_scheduled_sync.py
   ```

### Verify Deployment

```bash
# Check Cloud Scheduler jobs
gcloud scheduler jobs list

# Check Pub/Sub topics
gcloud pubsub topics list

# Check Cloud Functions
gcloud functions list
```

## Configuration

### Entity Sync Frequency

Set the sync frequency for each entity:

```python
# Via REST API
PUT /entities/{entity_id}/settings
{
  "sync_frequency": "daily",
  "auto_sync_enabled": true
}
```

### Supported Frequencies

- `"hourly"`: Sync every hour
- `"daily"`: Sync once per day
- `"weekly"`: Sync once per week
- `"manual"`: No automatic syncing

### Document Type Selection

Configure which document types to sync:

```json
{
  "sync_invoices": false,      // Customer invoices (ACCREC)
  "sync_bills": true,          // Supplier bills (ACCPAY) - for prepayments
  "sync_spend_money": true,    // Spend money transactions
  "sync_journal_entries": true, // Manual journal entries
  "sync_payments": false,      // Payment transactions
  "sync_bank_transactions": false // Bank transactions
}
```

## Monitoring

### Cloud Function Logs

Monitor scheduled sync execution:

```bash
# View scheduled sync processor logs
gcloud functions logs read scheduled-sync-processor --limit=50

# View xero sync consumer logs
gcloud functions logs read xero-sync-consumer --limit=50
```

### Firestore Monitoring

Check sync status in Firestore:

```python
# Query entities by sync frequency
db.collection("ENTITY_SETTINGS").where("sync_frequency", "==", "daily").get()

# Check last sync timestamps
entity_settings = db.collection("ENTITY_SETTINGS").document(entity_id).get()
last_sync = entity_settings.get("_system_lastSyncTimestampUtc_Bills")
```

### Pub/Sub Monitoring

Monitor message flow:

```bash
# Check topic metrics
gcloud pubsub topics describe scheduled-sync-topic

# Check subscription metrics
gcloud pubsub subscriptions describe scheduled-sync-subscription
```

## Testing

### Manual Testing

1. **Test Sync Logic**:
   ```bash
   python test_scheduled_sync.py
   ```

2. **Trigger Manual Sync**:
   ```python
   # Publish test message
   from google.cloud import pubsub_v1
   
   publisher = pubsub_v1.PublisherClient()
   topic_path = publisher.topic_path("drcr-d660a", "scheduled-sync-topic")
   
   message = {
       "sync_frequency": "daily",
       "trigger_type": "manual_test"
   }
   
   publisher.publish(topic_path, json.dumps(message).encode())
   ```

3. **Check Entity Status**:
   ```bash
   python -c "
   import asyncio
   from test_scheduled_sync import check_entity_sync_status
   asyncio.run(check_entity_sync_status())
   "
   ```

### Automated Testing

The system includes comprehensive tests for:
- Entity querying by sync frequency
- Sync timing logic validation
- Message publishing and processing
- Firestore data verification

## Troubleshooting

### Common Issues

1. **No Entities Being Synced**
   - Check `auto_sync_enabled` is `true`
   - Verify `sync_frequency` is set correctly
   - Ensure entities have valid OAuth tokens

2. **Scheduler Jobs Not Running**
   - Check Cloud Scheduler job status
   - Verify IAM permissions for service accounts
   - Check Pub/Sub topic exists

3. **Function Timeouts**
   - Monitor function execution time
   - Increase timeout if processing many entities
   - Check for Firestore query performance

4. **Missing Sync Timestamps**
   - Verify xero-sync-consumer is updating timestamps
   - Check for errors in sync processing
   - Ensure proper field names in Firestore

### Debug Commands

```bash
# Check scheduler job status
gcloud scheduler jobs describe hourly-entity-sync

# View function logs with timestamps
gcloud functions logs read scheduled-sync-processor --format="table(timestamp,message)"

# Check Pub/Sub subscription backlog
gcloud pubsub subscriptions describe scheduled-sync-subscription --format="value(numUndeliveredMessages)"
```

## Performance Considerations

### Scaling

- **Function Concurrency**: Max 5 instances for scheduled sync processor
- **Memory Allocation**: 512Mi for processing multiple entities
- **Timeout**: 5 minutes to handle large entity sets

### Optimization

1. **Batch Processing**: Process entities in batches to avoid timeouts
2. **Selective Syncing**: Only sync document types that are enabled
3. **Timestamp Caching**: Use most recent timestamp across all document types
4. **Error Handling**: Graceful degradation for individual entity failures

### Cost Management

- **Scheduler Frequency**: Balance sync freshness with cost
- **Function Execution**: Minimize cold starts with proper scheduling
- **Pub/Sub Messages**: Efficient message batching

## Security

### IAM Permissions

Required roles for the function service account:
- `roles/pubsub.publisher` - Publish to xero-sync-topic
- `roles/pubsub.subscriber` - Receive scheduled sync messages
- `roles/datastore.user` - Read/write Firestore data
- `roles/logging.logWriter` - Write function logs

### Data Protection

- **Encryption**: All data encrypted in transit and at rest
- **Access Control**: Function-level service account isolation
- **Audit Logging**: Comprehensive logging of sync activities

## Future Enhancements

### Planned Features

1. **Dynamic Scheduling**: Adjust frequency based on entity activity
2. **Sync Prioritization**: Priority queues for critical entities
3. **Health Monitoring**: Automated alerting for sync failures
4. **Batch Optimization**: Intelligent batching based on entity size
5. **Regional Scheduling**: Time zone-aware scheduling

### Integration Points

- **Monitoring Dashboard**: Real-time sync status visualization
- **Alert System**: Slack/email notifications for sync issues
- **Analytics**: Sync performance and cost analysis
- **API Extensions**: Programmatic sync control and monitoring 