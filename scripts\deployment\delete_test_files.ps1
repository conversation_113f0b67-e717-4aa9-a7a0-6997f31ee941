# delete_test_files.ps1
# <PERSON>ript to delete test files from the root directory

# Delete PowerShell test scripts
Get-ChildItem -Path "." -Filter "test_*.ps1" | ForEach-Object {
    Remove-Item -Path $_.FullName -Force
    Write-Host "Deleted $($_.Name)"
}

# Delete Python test scripts
Get-ChildItem -Path "." -Filter "test_*.py" | ForEach-Object {
    Remove-Item -Path $_.FullName -Force
    Write-Host "Deleted $($_.Name)"
}

# Delete other test files
$otherFiles = @(
    "direct_test.py",
    "local_test.py",
    "hello.py"
)

foreach ($file in $otherFiles) {
    if (Test-Path $file) {
        Remove-Item -Path $file -Force
        Write-Host "Deleted $file"
    }
}

Write-Host "`nCleanup complete. Root directory should now be clean."
