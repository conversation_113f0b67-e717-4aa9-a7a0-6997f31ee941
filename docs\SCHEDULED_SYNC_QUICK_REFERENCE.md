# Scheduled Sync System - Quick Reference

## 🚀 Quick Start

### Deploy Infrastructure
```bash
python scripts/deploy_scheduled_sync.py
```

### Test System
```bash
python tests/scheduled_sync/test_scheduled_sync.py
```

### Monitor Logs
```bash
gcloud functions logs read scheduled-sync-processor --limit=20
```

## 📋 Key Components

| Component | Purpose | Location |
|-----------|---------|----------|
| **Cloud Scheduler Jobs** | Trigger syncs at intervals | `terraform/scheduler.tf` |
| **Scheduled Sync Processor** | Process scheduled triggers | `cloud_functions/scheduled_sync_processor/` |
| **Xero Sync Consumer** | Execute actual syncs | `cloud_functions/xero_sync_consumer/` |
| **Entity Settings** | Store sync configuration | Firestore `ENTITY_SETTINGS` |

## ⏰ Sync Frequencies

| Frequency | Schedule | Cron Expression |
|-----------|----------|-----------------|
| **Hourly** | Every hour at minute 0 | `0 * * * *` |
| **Daily** | Every day at 2:00 AM UTC | `0 2 * * *` |
| **Weekly** | Every Monday at 3:00 AM UTC | `0 3 * * 1` |
| **Manual** | No automatic syncing | N/A |

## 🔧 Configuration

### Set Entity Sync Frequency
```python
# Via REST API
PUT /entities/{entity_id}/settings
{
  "sync_frequency": "daily",
  "auto_sync_enabled": true,
  "sync_bills": true,
  "sync_invoices": false
}
```

### Environment Variables
```bash
GCP_PROJECT_ID=drcr-d660a
PUBSUB_TOPIC_XERO_SYNC=xero-sync-topic
```

## 📊 Monitoring Commands

### Check Scheduler Jobs
```bash
gcloud scheduler jobs list --format='table(name,schedule,state)'
```

### View Function Logs
```bash
# Scheduled sync processor
gcloud functions logs read scheduled-sync-processor --limit=50

# Xero sync consumer
gcloud functions logs read xero-sync-consumer --limit=50
```

### Check Pub/Sub Topics
```bash
gcloud pubsub topics list --format='table(name)'
gcloud pubsub subscriptions list --format='table(name,topic)'
```

### Monitor Firestore
```python
# Query entities by sync frequency
db.collection("ENTITY_SETTINGS").where("sync_frequency", "==", "daily").get()

# Check last sync timestamps
settings = db.collection("ENTITY_SETTINGS").document(entity_id).get()
last_sync = settings.get("_system_lastSyncTimestampUtc_Bills")
```

## 🧪 Testing

### Manual Sync Trigger
```python
from google.cloud import pubsub_v1
import json

publisher = pubsub_v1.PublisherClient()
topic_path = publisher.topic_path("drcr-d660a", "scheduled-sync-topic")

message = {
    "sync_frequency": "daily",
    "trigger_type": "manual_test"
}

publisher.publish(topic_path, json.dumps(message).encode())
```

### Test Sync Logic
```bash
# Run comprehensive tests
python tests/scheduled_sync/test_scheduled_sync.py

# Test specific frequency
python cloud_functions/scheduled_sync_processor/main.py daily
```

## 🔍 Troubleshooting

### Common Issues

| Issue | Check | Solution |
|-------|-------|----------|
| **No entities syncing** | `auto_sync_enabled=true` | Update entity settings |
| **Scheduler not running** | Job status | Check IAM permissions |
| **Function timeouts** | Execution time | Increase timeout/memory |
| **Missing timestamps** | Sync completion | Check xero-sync-consumer logs |

### Debug Commands
```bash
# Check scheduler job status
gcloud scheduler jobs describe daily-entity-sync

# View function errors
gcloud functions logs read scheduled-sync-processor --filter="severity>=ERROR"

# Check Pub/Sub backlog
gcloud pubsub subscriptions describe scheduled-sync-subscription \
  --format="value(numUndeliveredMessages)"
```

## 📁 File Locations

### Core Files
- **Main Function**: `cloud_functions/scheduled_sync_processor/main.py`
- **Terraform Config**: `terraform/scheduler.tf`
- **Deployment Script**: `scripts/deploy_scheduled_sync.py`
- **Test Suite**: `tests/scheduled_sync/test_scheduled_sync.py`
- **Documentation**: `docs/SCHEDULED_SYNC_SYSTEM.md`

### Utility Scripts
- **Firestore Index Creation**: `scripts/utilities/create_firestore_indexes.py`
- **Prepayment Data Checker**: `scripts/utilities/check_prepayment_data.py`
- **Xero Invoice Testing**: `scripts/utilities/pull_xero_invoices_for_testing.py`
- **Sync Testing**: `scripts/utilities/sync_invoices_for_prepayment_testing.py`

### Key Functions
- `process_scheduled_sync()` - Main processing logic
- `get_entities_due_for_sync()` - Query entities by frequency
- `should_sync_entity()` - Check if sync is needed
- `trigger_entity_sync()` - Publish sync message

## 🎯 Entity Settings Fields

### Required Fields
```json
{
  "entity_id": "uuid",
  "sync_frequency": "daily|hourly|weekly|manual",
  "auto_sync_enabled": true,
  "transaction_sync_start_date": "2025-02-01"
}
```

### Document Type Controls
```json
{
  "sync_bills": true,           // Supplier bills (ACCPAY)
  "sync_invoices": false,       // Customer invoices (ACCREC)
  "sync_spend_money": true,     // Spend money transactions
  "sync_journal_entries": true, // Manual journals
  "sync_payments": false,       // Payment transactions
  "sync_bank_transactions": false // Bank transactions
}
```

### System Timestamps
```json
{
  "_system_lastSyncTimestampUtc_Accounts": "2025-06-03T13:29:35.499335+00:00",
  "_system_lastSyncTimestampUtc_Bills": "2025-06-03T13:30:07.783169+00:00",
  "_system_lastSyncTimestampUtc_Contacts": "2025-06-03T13:29:31.144653+00:00"
}
```

## 🚨 Emergency Commands

### Pause All Scheduled Syncs
```bash
gcloud scheduler jobs pause hourly-entity-sync
gcloud scheduler jobs pause daily-entity-sync
gcloud scheduler jobs pause weekly-entity-sync
```

### Resume Scheduled Syncs
```bash
gcloud scheduler jobs resume hourly-entity-sync
gcloud scheduler jobs resume daily-entity-sync
gcloud scheduler jobs resume weekly-entity-sync
```

### Force Manual Sync
```bash
# Trigger manual sync for specific entity
curl -X POST "https://your-api-url/entities/{entity_id}/sync/trigger" \
  -H "Authorization: Bearer $TOKEN"
```

## 📈 Performance Metrics

### Function Limits
- **Max Instances**: 5 (scheduled-sync-processor)
- **Memory**: 512Mi
- **Timeout**: 5 minutes
- **Concurrency**: 1 per instance

### Expected Performance
- **Entity Query**: ~100ms per 100 entities
- **Sync Decision**: ~10ms per entity
- **Message Publishing**: ~50ms per message
- **Total Processing**: ~1-2 seconds for typical workload 