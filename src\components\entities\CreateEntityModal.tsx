import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

import { 
  Building2, 
  ArrowRight, 
  ArrowLeft, 
  Check, 
  AlertCircle,
  Settings,
  Link as LinkIcon,
  Key,
  Globe,
  Loader2
} from 'lucide-react';

import { EntitiesService } from '@/services/entities.service';
import { EntityType } from '@/types/entity.types';
import type { 
  EntityWizardStep1, 
  EntityWizardStep2, 
  EntityWizardStep3 
} from '@/types/entity.types';

// Form schemas for each step
const step1Schema = z.object({
  entity_name: z.string().min(1, 'Entity name is required').max(100, 'Entity name too long'),
  type: z.nativeEnum(EntityType),
  description: z.string().optional(),
});

const step2Schema = z.object({
  connection_method: z.enum(['oauth', 'manual'] as const),
  oauth_provider: z.enum(['xero', 'qbo'] as const).optional(),
  manual_config: z.object({
    api_key: z.string().optional(),
    secret: z.string().optional(),
    endpoint: z.string().url().optional(),
  }).optional(),
});

const step3Schema = z.object({
  auto_sync_enabled: z.boolean().default(true),
  sync_frequency: z.enum(['hourly', 'daily', 'weekly', 'manual'] as const).default('daily'),
  prepayment_asset_account_codes: z.array(z.string()).default([]),
  excluded_pnl_account_codes: z.array(z.string()).default([]),
  default_amortization_months: z.number().min(1).max(60).default(12),
  notification_preferences: z.object({
    sync_errors: z.boolean().default(true),
    connection_issues: z.boolean().default(true),
    data_anomalies: z.boolean().default(false),
  }).default({
    sync_errors: true,
    connection_issues: true,
    data_anomalies: false,
  }),
});

type Step1FormData = z.infer<typeof step1Schema>;
type Step2FormData = z.infer<typeof step2Schema>;
type Step3FormData = z.infer<typeof step3Schema>;

interface CreateEntityModalProps {
  isOpen: boolean;
  onClose: () => void;
  clientId: string;
  clientName?: string;
  onSuccess: (entityId: string) => void;
}

export function CreateEntityModal({ 
  isOpen, 
  onClose, 
  clientId, 
  clientName, 
  onSuccess 
}: CreateEntityModalProps) {
  // State
  const [currentStep, setCurrentStep] = useState(1);
  const [isCreating, setIsCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Form data storage
  const [step1Data, setStep1Data] = useState<EntityWizardStep1 | null>(null);
  const [step2Data, setStep2Data] = useState<EntityWizardStep2 | null>(null);
  const [step3Data, setStep3Data] = useState<EntityWizardStep3 | null>(null);

  // Forms for each step
  const step1Form = useForm<Step1FormData>({
    resolver: zodResolver(step1Schema),
    defaultValues: {
      entity_name: '',
      type: EntityType.XERO,
      description: '',
    },
  });

  const step2Form = useForm<Step2FormData>({
    resolver: zodResolver(step2Schema),
    defaultValues: {
      connection_method: 'oauth',
      oauth_provider: 'xero',
    },
  });

  const step3Form = useForm<Step3FormData>({
    resolver: zodResolver(step3Schema),
    defaultValues: {
      auto_sync_enabled: true,
      sync_frequency: 'daily',
      prepayment_asset_account_codes: [],
      excluded_pnl_account_codes: [],
      default_amortization_months: 12,
      notification_preferences: {
        sync_errors: true,
        connection_issues: true,
        data_anomalies: false,
      },
    },
  });

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setCurrentStep(1);
      setError(null);
      setStep1Data(null);
      setStep2Data(null);
      setStep3Data(null);
      step1Form.reset();
      step2Form.reset();
      step3Form.reset();
    }
  }, [isOpen, step1Form, step2Form, step3Form]);

  // Update step 2 form when step 1 type changes
  useEffect(() => {
    const subscription = step1Form.watch((value) => {
      if (value.type && ['xero', 'qbo'].includes(value.type)) {
        step2Form.setValue('oauth_provider', value.type as 'xero' | 'qbo');
      }
    });
    return () => subscription.unsubscribe();
  }, [step1Form, step2Form]);

  // Event handlers
  const handleStep1Submit = (data: Step1FormData) => {
    const step1Data: EntityWizardStep1 = {
      entity_name: data.entity_name,
      type: data.type,
      description: data.description,
    };
    setStep1Data(step1Data);
    setCurrentStep(2);
  };

  const handleStep2Submit = (data: Step2FormData) => {
    const step2Data: EntityWizardStep2 = {
      connection_method: data.connection_method,
      oauth_provider: data.oauth_provider,
      manual_config: data.manual_config,
    };
    setStep2Data(step2Data);
    setCurrentStep(3);
  };

  const handleStep3Submit = async (data: Step3FormData) => {
    if (!step1Data || !step2Data) return;

    setIsCreating(true);
    setError(null);

    try {
      const step3Data: EntityWizardStep3 = {
        settings: {
          entity_id: '', // Will be set by the service
          entity_name: step1Data.entity_name,
          prepayment_asset_account_codes: data.prepayment_asset_account_codes,
          excluded_pnl_account_codes: data.excluded_pnl_account_codes,
          default_amortization_months: data.default_amortization_months,
          auto_sync_enabled: data.auto_sync_enabled,
          sync_frequency: data.sync_frequency,
          notification_preferences: {
            sync_errors: data.notification_preferences.sync_errors,
            connection_issues: data.notification_preferences.connection_issues,
            data_anomalies: data.notification_preferences.data_anomalies,
          },
        },
        auto_sync_enabled: data.auto_sync_enabled,
        sync_frequency: data.sync_frequency,
      };
      
      const entityId = await EntitiesService.createEntityFromWizard(
        step1Data,
        step2Data,
        step3Data,
        clientId
      );
      
      onSuccess(entityId);
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create entity');
    } finally {
      setIsCreating(false);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleClose = () => {
    if (!isCreating) {
      onClose();
    }
  };

  // Step indicator
  const renderStepIndicator = () => (
    <div className="flex items-center justify-center mb-6">
      {[1, 2, 3].map((step) => (
        <React.Fragment key={step}>
          <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
            step === currentStep 
              ? 'border-blue-600 bg-blue-600 text-white' 
              : step < currentStep 
                ? 'border-green-600 bg-green-600 text-white'
                : 'border-gray-300 bg-white text-gray-400'
          }`}>
            {step < currentStep ? (
              <Check className="h-4 w-4" />
            ) : (
              <span className="text-sm font-medium">{step}</span>
            )}
          </div>
          {step < 3 && (
            <div className={`w-12 h-0.5 mx-2 ${
              step < currentStep ? 'bg-green-600' : 'bg-gray-300'
            }`} />
          )}
        </React.Fragment>
      ))}
    </div>
  );

  // Step 1: Basic Information
  const renderStep1 = () => (
    <Form {...step1Form}>
      <form onSubmit={step1Form.handleSubmit(handleStep1Submit)} className="space-y-6">
        <div className="text-center mb-6">
          <h3 className="text-lg font-semibold">Basic Information</h3>
          <p className="text-sm text-gray-500">Set up your entity details</p>
        </div>

        <FormField
          control={step1Form.control}
          name="entity_name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Entity Name</FormLabel>
              <FormControl>
                <Input placeholder="e.g., ABC Company - Xero" {...field} />
              </FormControl>
              <FormDescription>
                A descriptive name for this accounting entity
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={step1Form.control}
          name="type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Entity Type</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select entity type" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="xero">
                    <div className="flex items-center">
                      <Building2 className="h-4 w-4 mr-2 text-blue-600" />
                      Xero
                    </div>
                  </SelectItem>
                  <SelectItem value="qbo">
                    <div className="flex items-center">
                      <Building2 className="h-4 w-4 mr-2 text-green-600" />
                      QuickBooks Online
                    </div>
                  </SelectItem>
                  <SelectItem value="manual">
                    <div className="flex items-center">
                      <Settings className="h-4 w-4 mr-2 text-gray-600" />
                      Manual Entry
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={step1Form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description (Optional)</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="Additional notes about this entity..."
                  className="resize-none"
                  rows={3}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end">
          <Button type="submit">
            Next
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </form>
    </Form>
  );

  // Step 2: Connection Setup
  const renderStep2 = () => (
    <Form {...step2Form}>
      <form onSubmit={step2Form.handleSubmit(handleStep2Submit)} className="space-y-6">
        <div className="text-center mb-6">
          <h3 className="text-lg font-semibold">Connection Setup</h3>
          <p className="text-sm text-gray-500">Configure how to connect to your accounting software</p>
        </div>

        {step1Data?.type !== 'manual' && (
          <FormField
            control={step2Form.control}
            name="connection_method"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Connection Method</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select connection method" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="oauth">
                      <div className="flex items-center">
                        <LinkIcon className="h-4 w-4 mr-2 text-green-600" />
                        OAuth (Recommended)
                      </div>
                    </SelectItem>
                    <SelectItem value="manual">
                      <div className="flex items-center">
                        <Key className="h-4 w-4 mr-2 text-orange-600" />
                        Manual Configuration
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>
                  OAuth provides secure, automatic connection setup
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        {step2Form.watch('connection_method') === 'oauth' && step1Data?.type !== 'manual' && (
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">OAuth Connection</CardTitle>
              <CardDescription>
                You'll be redirected to {step1Data?.type?.toUpperCase()} to authorize the connection
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <Badge variant="outline">
                  <Globe className="h-3 w-3 mr-1" />
                  Secure
                </Badge>
                <Badge variant="outline">
                  <Check className="h-3 w-3 mr-1" />
                  Automatic
                </Badge>
              </div>
            </CardContent>
          </Card>
        )}

        {step2Form.watch('connection_method') === 'manual' && (
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Manual Configuration</CardTitle>
              <CardDescription>
                Enter your API credentials manually
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={step2Form.control}
                name="manual_config.api_key"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>API Key</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder="Enter API key" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={step2Form.control}
                name="manual_config.secret"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>API Secret</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder="Enter API secret" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={step2Form.control}
                name="manual_config.endpoint"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>API Endpoint (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="https://api.example.com" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>
        )}

        {step1Data?.type === 'manual' && (
          <Alert>
            <Settings className="h-4 w-4" />
            <AlertDescription>
              Manual entities don't require connection setup. You can proceed to configuration.
            </AlertDescription>
          </Alert>
        )}

        <div className="flex justify-between">
          <Button type="button" variant="outline" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <Button type="submit">
            Next
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </form>
    </Form>
  );

  // Step 3: Configuration
  const renderStep3 = () => (
    <Form {...step3Form}>
      <form onSubmit={step3Form.handleSubmit(handleStep3Submit)} className="space-y-6">
        <div className="text-center mb-6">
          <h3 className="text-lg font-semibold">Configuration</h3>
          <p className="text-sm text-gray-500">Set up sync preferences and settings</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={step3Form.control}
            name="auto_sync_enabled"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>Enable Auto Sync</FormLabel>
                  <FormDescription>
                    Automatically sync data at regular intervals
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />

          <FormField
            control={step3Form.control}
            name="sync_frequency"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Sync Frequency</FormLabel>
                <Select 
                  onValueChange={field.onChange} 
                  defaultValue={field.value}
                  disabled={!step3Form.watch('auto_sync_enabled')}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select frequency" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="hourly">Hourly</SelectItem>
                    <SelectItem value="daily">Daily</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="manual">Manual Only</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={step3Form.control}
          name="default_amortization_months"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Default Amortization Period (Months)</FormLabel>
              <FormControl>
                <Input 
                  type="number" 
                  min="1" 
                  max="60" 
                  {...field}
                  onChange={(e) => field.onChange(parseInt(e.target.value) || 12)}
                />
              </FormControl>
              <FormDescription>
                Default period for amortizing prepayments (1-60 months)
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Notification Preferences</CardTitle>
            <CardDescription>
              Choose which events you'd like to be notified about
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={step3Form.control}
              name="notification_preferences.sync_errors"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Sync Errors</FormLabel>
                    <FormDescription>
                      Get notified when sync operations fail
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />

            <FormField
              control={step3Form.control}
              name="notification_preferences.connection_issues"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Connection Issues</FormLabel>
                    <FormDescription>
                      Get notified about connection problems
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />

            <FormField
              control={step3Form.control}
              name="notification_preferences.data_anomalies"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Data Anomalies</FormLabel>
                    <FormDescription>
                      Get notified about unusual data patterns
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        <div className="flex justify-between">
          <Button type="button" variant="outline" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <Button type="submit" disabled={isCreating}>
            {isCreating ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Creating...
              </>
            ) : (
              <>
                <Check className="h-4 w-4 mr-2" />
                Create Entity
              </>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Entity</DialogTitle>
          <DialogDescription>
            Add a new accounting entity for {clientName || `Client ${clientId}`}
          </DialogDescription>
        </DialogHeader>

        {renderStepIndicator()}

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="py-4">
          {currentStep === 1 && renderStep1()}
          {currentStep === 2 && renderStep2()}
          {currentStep === 3 && renderStep3()}
        </div>
      </DialogContent>
    </Dialog>
  );
}
 