#!/usr/bin/env python3

import asyncio
import os
from google.cloud import firestore
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def fix_xero_redirect_uri():
    """Fix the Xero redirect URI in client configurations"""
    
    # Initialize Firestore client
    db = firestore.AsyncClient()
    
    try:
        # Get all clients
        clients_ref = db.collection("CLIENTS")
        clients = await clients_ref.get()
        
        print(f"Found {len(clients)} clients")
        
        for client_doc in clients:
            client_data = client_doc.to_dict()
            client_id = client_data.get("client_id")
            
            # Check if this client has Xero configuration
            if "xero_config" in client_data:
                xero_config = client_data["xero_config"]
                current_redirect_uri = xero_config.get("redirect_uri", "")
                
                print(f"Client {client_id}: Current redirect URI: {current_redirect_uri}")
                
                # Update if it's using port 8080
                if "localhost:8080" in current_redirect_uri:
                    new_redirect_uri = current_redirect_uri.replace("localhost:8080", "localhost:8081")
                    
                    # Update the client document
                    await client_doc.reference.update({
                        "xero_config.redirect_uri": new_redirect_uri,
                        "XERO_REDIRECT_URI": new_redirect_uri
                    })
                    
                    print(f"✅ Updated client {client_id}: {current_redirect_uri} → {new_redirect_uri}")
                else:
                    print(f"✓ Client {client_id}: Redirect URI is already correct")
            else:
                print(f"- Client {client_id}: No Xero configuration")
        
        print("\n✅ Xero redirect URI fix completed!")
        
    except Exception as e:
        print(f"❌ Error fixing Xero redirect URI: {e}")
    finally:
        # Close the client
        db.close()

if __name__ == "__main__":
    asyncio.run(fix_xero_redirect_uri())
