#!/bin/sh
# Script for setting up Firebase configuration

echo "Running setup_firebase_config.sh"
echo "Current directory: $(pwd)"
echo "Listing current directory:"
ls -la

mkdir -p frontend
cp -r drcr_front/* frontend/ || true
echo "Listing drcr_front directory:"
ls -la drcr_front/ || echo "drcr_front directory not found"
echo "Listing frontend directory:"
ls -la frontend/ || echo "frontend directory is empty"

if [ ! -f "frontend/firebase.json" ]; then
  echo "firebase.json not found, creating default configuration"
  echo '{
    "hosting": {
      "public": "dist",
      "ignore": [
        "firebase.json",
        "**/.*",
        "**/node_modules/**"
      ],
      "rewrites": [
        {
          "source": "**",
          "destination": "/index.html"
        }
      ]
    }
  }' > frontend/firebase.json
fi

if [ ! -f "frontend/.firebaserc" ]; then
  echo ".firebaserc not found, creating default configuration"
  echo '{
    "projects": {
      "default": "drcr-d660a"
    }
  }' > frontend/.firebaserc
fi

echo "Firebase configuration setup completed"
