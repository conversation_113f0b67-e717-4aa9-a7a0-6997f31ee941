from fastapi import APIRouter, Depends, HTTPException, status, Body, Path, Query
from fastapi.responses import RedirectResponse
from typing import List, Dict, Any, Optional
import uuid
import logging
from google.cloud.firestore import SERVER_TIMESTAMP

from ..core.firebase_auth import get_current_user, get_firm_admin, get_firm_user_with_client_access, AuthUser
from ..models.auth import ClientModel
from ..models.error_codes import AppErrorCode
from ..dependencies import get_db
from ..services.client_service import ClientService
from ..schemas.client_schemas import (
    ClientCreate, ClientUpdate, ClientResponse, ClientListResponse,
    ClientWizardStep1, ClientWizardStep2, ClientW<PERSON>rdStep3,
    ClientStatus, ClientType, ClientSize
)

logger = logging.getLogger(__name__)
router = APIRouter(tags=["Clients"])

def get_client_service(db = Depends(get_db)) -> ClientService:
    """Dependency to get client service instance"""
    return ClientService(db)

@router.get("/test-first")
async def test_clients_first():
    """Test endpoint at the very beginning of clients router"""
    return {"message": "First test endpoint in clients router", "status": "ok"}

@router.post("/", response_model=Dict[str, Any])
async def create_client(
    client_data: ClientCreate,
    current_user: AuthUser = Depends(get_firm_admin),
    client_service: ClientService = Depends(get_client_service)
):
    """Create a new client for the firm (firm_admin only)"""
    try:
        client_id, client_name = await client_service.create_client(client_data, current_user)
        return {
            "message": "Client created successfully",
            "client_id": client_id,
            "name": client_name
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create client"
        )

@router.post("/wizard", response_model=Dict[str, Any])
async def create_client_from_wizard(
    wizard_data: Dict[str, Any] = Body(...),
    current_user: AuthUser = Depends(get_firm_admin),
    client_service: ClientService = Depends(get_client_service)
):
    """Create a new client using the multi-step wizard"""
    try:
        # Extract step data from the nested structure
        step1_data = ClientWizardStep1(**wizard_data.get("step1_data", {}))
        step2_data = ClientWizardStep2(**wizard_data.get("step2_data", {}))
        step3_data = ClientWizardStep3(**wizard_data.get("step3_data", {}))

        client_id, client_name = await client_service.create_client_from_wizard(
            step1_data, step2_data, step3_data, current_user
        )
        return {
            "message": "Client created successfully from wizard",
            "client_id": client_id,
            "name": client_name
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create client from wizard"
        )

@router.get("/", response_model=ClientListResponse)
async def list_clients(
    page: int = Query(1, description="Page number", ge=1),
    limit: int = Query(20, description="Items per page", ge=1, le=100),
    name_filter: Optional[str] = Query(None, description="Filter clients by name"),
    status_filter: Optional[str] = Query(None, description="Filter by status"),
    client_type_filter: Optional[str] = Query(None, description="Filter by client type"),
    current_user: AuthUser = Depends(get_current_user),
    client_service: ClientService = Depends(get_client_service)
):
    """List clients with enhanced filtering and pagination"""
    try:
        clients, pagination = await client_service.get_clients_for_firm(
            current_user, page, limit, name_filter, status_filter, client_type_filter
        )
        return ClientListResponse(clients=clients, pagination=pagination)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve clients"
        )

@router.get("/summary")
async def get_clients_summary(
    page: int = Query(1, description="Page number", ge=1),
    limit: int = Query(20, description="Items per page", ge=1, le=100),
    client_filter: Optional[str] = Query(None, description="Filter clients by name"),
    status_filter: Optional[str] = Query(None, description="Filter by overall status"),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Get summary of all clients for firm dashboard with entities and aggregated data (legacy endpoint)"""
    if not current_user.firm_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User is not associated with a firm"
        )

    # Firm admins can see all clients, staff can only see assigned clients
    all_clients = []

    if current_user.role == "firm_admin":
        # Get all clients for the firm
        query = db.collection("CLIENTS").where("firm_id", "==", current_user.firm_id)
        client_docs = await query.get()
        client_data_list = [doc.to_dict() for doc in client_docs]
    else:
        # Get only assigned clients
        client_data_list = []
        assigned_client_ids = current_user.assigned_client_ids or []
        for client_id in assigned_client_ids:
            client_ref = db.collection("CLIENTS").document(client_id)
            client_doc = await client_ref.get()
            if client_doc.exists:
                client_data = client_doc.to_dict()
                if client_data["firm_id"] == current_user.firm_id:
                    client_data_list.append(client_data)

    # Process each client to get entities and calculate summary data
    for client_data in client_data_list:
        client_id = client_data.get("client_id")

        # Get entities for this client - handle errors gracefully
        entities = []
        error_count = 0
        try:
            entities_query = db.collection("ENTITIES").where("client_id", "==", client_id)
            entities_docs = await entities_query.get()

            for entity_doc in entities_docs:
                entity_data = entity_doc.to_dict()
                entity_id = entity_doc.id
                connection_status = entity_data.get("connection_details", {}).get("status", "unknown")
                entity_status = entity_data.get("status", "unknown")

                # Count errors
                if connection_status in ["error", "disconnected"] or entity_status == "error":
                    error_count += 1

                # Get sync status for this entity
                sync_status = await _get_entity_sync_status_for_dashboard(db, entity_id, client_id)

                # Get actual last sync timestamp from entity settings
                last_sync_timestamp = await _get_last_sync_timestamp(db, entity_id)

                entities.append({
                    "entity_id": entity_id,
                    "entity_name": entity_data.get("entity_name"),
                    "type": entity_data.get("type", "unknown"),
                    "connection_status": connection_status,
                    "entity_status": entity_status,
                    "last_sync": last_sync_timestamp,
                    "error_message": entity_data.get("connection_details", {}).get("error_message"),
                    "sync_status": {
                        "is_syncing": entity_status == "syncing",
                        "current_step": sync_status.get("current_step", "idle"),
                        "progress_percentage": sync_status.get("progress_percentage", 0),
                        "estimated_remaining": sync_status.get("estimated_remaining"),
                        "user_message": sync_status.get("user_message", "Ready"),
                        "last_sync_completed": sync_status.get("last_sync_completed"),
                        "sync_duration_warning": "Xero operations typically take 5-15 minutes" if entity_status == "syncing" else None
                    }
                })
        except Exception as e:
            # Log the error but continue processing
            print(f"Error fetching entities for client {client_id}: {e}")
            # entities remains empty list, error_count remains 0

        # Calculate overall status
        overall_status = "ok"
        if error_count > 0:
            overall_status = "error"
        # TODO: Add logic for pending items count and action_needed status
        # This would require querying transactions/schedules for pending items

        client_summary = {
            "client_id": client_id,
            "name": client_data.get("name"),  # Fix: Use 'name' instead of 'client_name'
            "status": client_data.get("status"),
            "entities": entities,
            "pending_items_count": 0,  # TODO: Calculate from transactions/schedules
            "error_count": error_count,
            "last_activity": client_data.get("updated_at"),
            "overall_status": overall_status
        }

        all_clients.append(client_summary)

    # Apply filters
    filtered_clients = all_clients

    if client_filter:
        filtered_clients = [
            c for c in filtered_clients
            if client_filter.lower() in c["name"].lower()  # Fix: Use 'name' instead of 'client_name'
        ]

    if status_filter and status_filter != "all":
        filtered_clients = [
            c for c in filtered_clients
            if c["overall_status"] == status_filter
        ]

    # Apply pagination
    total_items = len(filtered_clients)
    total_pages = (total_items + limit - 1) // limit
    start_idx = (page - 1) * limit
    end_idx = start_idx + limit
    paginated_clients = filtered_clients[start_idx:end_idx]

    return {
        "clients": paginated_clients,
        "pagination": {
            "current_page": page,
            "page_size": limit,
            "total_items": total_items,
            "total_pages": total_pages
        }
    }

@router.get("/{client_id}", response_model=ClientResponse)
async def get_client(
    client_id: str = Path(...),
    current_user: AuthUser = Depends(get_current_user),
    client_service: ClientService = Depends(get_client_service)
):
    """Get detailed client information by ID"""
    try:
        client = await client_service.get_client_by_id(client_id, current_user)
        return client
    except ValueError as e:
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=str(e)
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=str(e)
            )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve client"
        )

@router.put("/{client_id}")
async def update_client(
    client_data: ClientUpdate,
    client_id: str = Path(...),
    current_user: AuthUser = Depends(get_firm_admin),
    client_service: ClientService = Depends(get_client_service)
):
    """Update client information (firm_admin only)"""
    try:
        await client_service.update_client(client_id, client_data, current_user)
        return {"message": "Client updated successfully"}
    except ValueError as e:
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=str(e)
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=str(e)
            )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update client"
        )

@router.delete("/{client_id}")
async def delete_client(
    client_id: str = Path(...),
    current_user: AuthUser = Depends(get_firm_admin),
    client_service: ClientService = Depends(get_client_service)
):
    """Soft delete a client (firm_admin only)"""
    try:
        await client_service.delete_client(client_id, current_user)
        return {"message": "Client deleted successfully"}
    except ValueError as e:
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=str(e)
            )
        elif "cannot delete" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=str(e)
            )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete client"
        )

# Utility endpoints
@router.get("/enums/client-types")
async def get_client_types():
    """Get available client types"""
    return {
        "client_types": [
            {"value": client_type.value, "label": client_type.value.replace("_", " ").title()}
            for client_type in ClientType
        ]
    }

@router.get("/enums/client-sizes")
async def get_client_sizes():
    """Get available client sizes"""
    return {
        "client_sizes": [
            {"value": size.value, "label": size.value.title()}
            for size in ClientSize
        ]
    }

@router.get("/enums/client-statuses")
async def get_client_statuses():
    """Get available client statuses"""
    return {
        "client_statuses": [
            {"value": status_value.value, "label": status_value.value.title()}
            for status_value in ClientStatus
        ]
    }

# Test endpoints (keeping for backward compatibility)
@router.get("/test")
async def test_clients_basic():
    """Basic test endpoint for clients router"""
    return {"message": "Clients router test endpoint", "status": "ok"}

@router.get("/test-auth")
async def test_clients_auth(
    current_user: AuthUser = Depends(get_current_user)
):
    """Test endpoint with authentication"""
    return {
        "message": "Clients router auth test endpoint",
        "user": current_user.email,
        "firm_id": current_user.firm_id,
        "status": "ok"
    }

@router.get("/test-db")
async def test_clients_db(
    db = Depends(get_db)
):
    """Test endpoint with database connection"""
    try:
        # Test database connection
        test_ref = db.collection("TEST").document("test")
        await test_ref.set({"test": True, "timestamp": SERVER_TIMESTAMP})
        return {"message": "Database connection test successful", "status": "ok"}
    except Exception as e:
        return {"message": f"Database connection test failed: {str(e)}", "status": "error"}

# Legacy Xero configure route - redirect to entities page
@router.get("/{client_id}/xero/configure")
async def legacy_xero_configure_redirect(
    client_id: str = Path(...),
    entities: Optional[str] = Query(None, description="Entity IDs (legacy parameter)"),
    reconnected: Optional[bool] = Query(None, description="Reconnection flag (legacy parameter)"),
    current_user: AuthUser = Depends(get_current_user)
):
    """
    Legacy route for Xero configuration - redirects to entities page.
    This route exists to handle old bookmarks/cached redirects that still point to the configure endpoint.
    """
    # Check client access
    await get_firm_user_with_client_access(client_id, current_user)
    
    # Redirect to the entities page
    return RedirectResponse(url=f"/clients/{client_id}/entities", status_code=307)

@router.get("/{client_id}/entities")
async def get_client_entities(
    client_id: str = Path(...),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """
    Get entities for a specific client.
    This route is used by the frontend after Xero OAuth redirects.
    """
    try:
        # Check client access
        await get_firm_user_with_client_access(client_id, current_user)
        
        # Get entities for this client
        entities_query = (
            db.collection("ENTITIES")
            .where("client_id", "==", client_id)
            .where("status", "==", "active")
        )
        entities_docs = entities_query.stream()
        
        entities = []
        async for entity_doc in entities_docs:
            entity_data = entity_doc.to_dict()
            entity_id = entity_doc.id
            
            raw_entity_for_frontend = {
                "entity_id": entity_id,
                "entity_name": entity_data.get("entity_name", "Unnamed Entity"),
                "type": entity_data.get("type"),
                "status": entity_data.get("status"), 
                "connection_details": entity_data.get("connection_details", {}),
                "pending_items_count": entity_data.get("pending_items_count"),
                "error_count": entity_data.get("error_count"),
                "health_score": entity_data.get("health_score")
            }
            
            if not raw_entity_for_frontend.get("type"):
                logger.warning(f"Entity {entity_id} is missing 'type' in Firestore. Defaulting to 'unknown' for API response.")
                raw_entity_for_frontend["type"] = "unknown"
            
            entities.append(raw_entity_for_frontend)
        
        return {
            "client_id": client_id,
            "entities": entities,
            "total": len(entities)
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to get entities for client {client_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve client entities"
        )

async def _get_entity_sync_status_for_dashboard(db, entity_id: str, client_id: str) -> dict:
    """
    Get sync status information for an entity to display on the dashboard.
    Returns a simplified sync status object for dashboard display.
    """
    try:
        # Get recent audit logs for sync status
        audit_query = (
            db.collection("AUDIT_LOG")
            .where("entity_id", "==", entity_id)
            .where("event_category", "==", "SYNC")
            .order_by("timestamp", direction="DESCENDING")
            .limit(5)
        )
        audit_docs = await audit_query.get()

        recent_events = []
        current_sync_job = None
        last_completed_sync = None

        for doc in audit_docs:
            audit_data = doc.to_dict()
            event_type = audit_data.get("event_type", "")

            recent_events.append({
                "event_type": event_type,
                "status": audit_data.get("status"),
                "timestamp": audit_data.get("timestamp")
            })

            # Track current sync job
            if event_type == "XERO_SYNC_JOB_STARTED" and not current_sync_job:
                current_sync_job = audit_data.get("details", {}).get("syncJobId")

            # Track last completed sync
            if event_type == "XERO_SYNC_JOB_COMPLETED" and not last_completed_sync:
                last_completed_sync = audit_data.get("timestamp")

        # Determine current step and progress
        current_step = "idle"
        progress_percentage = 0
        user_message = "Ready"
        estimated_remaining = None

        if recent_events:
            latest_event = recent_events[0]
            event_type = latest_event.get("event_type", "")

            if event_type == "XERO_SYNC_JOB_STARTED":
                current_step = "connecting"
                progress_percentage = 10
                user_message = "Connecting to Xero..."
                estimated_remaining = "8-12 minutes"
            elif event_type == "CHART_OF_ACCOUNTS_SYNC_STARTED":
                current_step = "accounts"
                progress_percentage = 25
                user_message = "Syncing Chart of Accounts..."
                estimated_remaining = "6-10 minutes"
            elif event_type == "CONTACTS_SYNC_STARTED":
                current_step = "contacts"
                progress_percentage = 50
                user_message = "Syncing Contacts..."
                estimated_remaining = "4-6 minutes"
            elif event_type == "TRANSACTIONS_SYNC_STARTED":
                current_step = "transactions"
                progress_percentage = 75
                user_message = "Syncing Transactions..."
                estimated_remaining = "2-4 minutes"
            elif event_type == "XERO_SYNC_JOB_COMPLETED":
                current_step = "completed"
                progress_percentage = 100
                user_message = "Sync completed successfully"
                estimated_remaining = None
            elif "ERROR" in event_type or "FAILED" in event_type:
                current_step = "error"
                progress_percentage = 0
                user_message = "Sync failed - please check connection"
                estimated_remaining = None

        return {
            "current_step": current_step,
            "progress_percentage": progress_percentage,
            "user_message": user_message,
            "estimated_remaining": estimated_remaining,
            "last_sync_completed": last_completed_sync,
            "current_sync_job": current_sync_job
        }

    except Exception as e:
        # Return default status if there's an error
        print(f"Error getting sync status for entity {entity_id}: {e}")
        return {
            "current_step": "idle",
            "progress_percentage": 0,
            "user_message": "Ready",
            "estimated_remaining": None,
            "last_sync_completed": None,
            "current_sync_job": None
        }


async def _get_last_sync_timestamp(db, entity_id: str) -> str:
    """
    Get the most recent sync timestamp from entity settings.
    Returns the timestamp as an ISO string or None if never synced.
    """
    try:
        # Get entity settings to check for sync timestamps
        settings_ref = db.collection("ENTITY_SETTINGS").document(entity_id)
        settings_doc = await settings_ref.get()

        if not settings_doc.exists:
            return None

        settings_data = settings_doc.to_dict()

        # Look for the most recent sync timestamp
        sync_timestamps = []

        # Check for various sync timestamp fields
        timestamp_fields = [
            "_system_lastSyncTimestampUtc_Accounts",
            "_system_lastSyncTimestampUtc_Contacts",
            "_system_lastSyncTimestampUtc_Transactions",
            "_system_lastSyncTimestampUtc_Bills",
            "_system_lastSyncTimestampUtc_Invoices"
        ]

        for field in timestamp_fields:
            if field in settings_data and settings_data[field]:
                sync_timestamps.append(settings_data[field])

        if sync_timestamps:
            # Return the most recent timestamp
            return max(sync_timestamps)

        return None

    except Exception as e:
        print(f"Error getting last sync timestamp for entity {entity_id}: {e}")
        return None

