# DRCR Application Documentation

Welcome to the official documentation for the DRCR (Data Reconciliation & Categorization Resource) application. This documentation provides a comprehensive guide to understanding the system's architecture, data model, API specifications, and development practices.

## Overview

The DRCR application is designed to streamline financial data management, offering robust tools for data ingestion from accounting platforms (like Xero), automated data processing (such as amortization schedule generation), and a RESTful API for programmatic access and integration.

This documentation is intended for:
*   **Developers**: Working on maintaining or extending the DRCR backend and associated services.
*   **API Consumers**: Integrating other applications or services with the DRCR API.
*   **System Architects**: Seeking to understand the overall design and data flow of the application.

## Table of Contents

1.  **[Data Model](./data_model/README.md)**
    *   [Introduction to the Data Model](./data_model/README.md)
    *   [Firestore Collections](./data_model/collections.md)
    *   [Data Relationships & Flow](./data_model/relationships.md)
    *   [Diagrams](./data_model/diagrams/) *(Placeholder for visual aids)*

2.  **[API Guide](./api_guide/README.md)**
    *   [API Overview & Base URL](./api_guide/README.md)
    *   [Authentication & Authorization](./api_guide/authentication.md)
    *   [Standard Request Patterns (Pagination, Versioning, Rate Limiting)](./api_guide/request_patterns.md)
    *   [Endpoint Reference (via OpenAPI/Swagger)](./api_guide/endpoints.md) *(Primarily points to auto-generated docs)*

3.  **[Error Handling](./error_handling/README.md)**
    *   [Error Handling Overview](./error_handling/README.md)
    *   [Application-Specific Error Codes](./error_handling/error_codes.md)
    *   [Error Response Examples & Client Handling](./error_handling/examples.md)

4.  **[Development Guide](./development/README.md)**
    *   [Development Environment Setup](./development/setup.md)
    *   [Running Locally](./development/running_locally.md)
    *   [Codebase Structure](./development/codebase_structure.md)
    *   [Testing Guidelines](./development/testing.md)
    *   [Style Guide](./development/style_guide.md)

5.  **[Scheduled Sync System](./SCHEDULED_SYNC_SYSTEM.md)**
    *   Automated entity synchronization infrastructure
    *   Cloud Scheduler, Pub/Sub, and Cloud Functions integration
    *   Configuration, monitoring, and troubleshooting
    *   [Quick Reference Guide](./SCHEDULED_SYNC_QUICK_REFERENCE.md)

6.  **[Performance Optimization](./PERFORMANCE_OPTIMIZATION_SUMMARY.md)**
    *   Performance improvements and optimization strategies
    *   Benchmarking results and monitoring

7.  **[CI/CD & Deployment](./FULLSTACK_CICD_SETUP.md)**
    *   GitLab CI/CD pipeline configuration
    *   Full-stack deployment procedures
    *   [Quick Deploy Reference](./QUICK_DEPLOY_REFERENCE.md)

8.  **[Additional Documentation](./)**
    *   [Backend Refactoring Summary](./BACKEND_REFACTORING_SUMMARY.md)
    *   [Firestore Token Migration](./FIRESTORE_TOKEN_MIGRATION.md)
    *   [Task Planning](./TASK_PLANNER_UPDATED.md)

## Contribution

Details on how to contribute to this documentation will be added here. For now, please raise issues or pull requests against the main repository.