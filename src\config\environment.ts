// Smart environment configuration
// Automatically detects environment and sets appropriate values

interface EnvironmentConfig {
  api: {
    timeout: number;
    baseUrl: string;
    retryAttempts: number;
    cacheTtl: number;
  };
  auth: {
    timeout: number;
    cacheTimeout: number;
  };
  debug: {
    enabled: boolean;
    verbose: boolean;
  };
  performance: {
    requestDeduplicationWindow: number;
    cacheTimeout: number;
  };
}

// Environment detection
const isProduction = import.meta.env.PROD;
const isDevelopment = import.meta.env.DEV;
const isLocalhost = typeof window !== 'undefined' && 
  (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1');

// Smart configuration based on environment
export const config: EnvironmentConfig = {
  api: {
    // Longer timeout for localhost development, shorter for production
    timeout: isLocalhost ? 15000 : isProduction ? 8000 : 12000,
    baseUrl: 'http://localhost:8081', // Hardcoded to fix the fucking issue
    retryAttempts: isProduction ? 2 : 1,
    // Cache timeout: longer for production (more stable), shorter for dev
    cacheTtl: isProduction ? 10 * 60 * 1000 : 5 * 60 * 1000
  },
  auth: {
    // Auth timeout matches API timeout
    timeout: isLocalhost ? 15000 : isProduction ? 8000 : 12000,
    // Cache auth profile for 5 minutes in all environments
    cacheTimeout: 5 * 60 * 1000
  },
  debug: {
    // Enable debug logs in development only
    enabled: isDevelopment,
    // Verbose logging only in localhost development
    verbose: isDevelopment && isLocalhost
  },
  performance: {
    // Shorter deduplication window for production
    requestDeduplicationWindow: isProduction ? 300 : 500,
    // Shorter cache timeout for production
    cacheTimeout: isProduction ? 3 * 60 * 1000 : 5 * 60 * 1000
  }
};

// Helper functions for common checks
export const env = {
  isProduction,
  isDevelopment,
  isLocalhost,
  
  // Smart logging that respects debug settings
  log: (...args: unknown[]) => {
    if (config.debug.enabled) {
      console.log(...args);
    }
  },
  
  logVerbose: (...args: unknown[]) => {
    if (config.debug.verbose) {
      console.log(...args);
    }
  },
  
  error: (...args: unknown[]) => {
    // Always log errors, but with different levels
    if (config.debug.enabled) {
      console.error(...args);
    } else {
      // In production, log errors but less verbosely
      console.error(args[0]);
    }
  }
};

export default config; 