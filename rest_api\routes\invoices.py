from fastapi import APIRouter, Depends, HTTPException, Path, Query
from typing import List, Optional

from ..core.firebase_auth import get_current_user, get_firm_user_with_client_access, AuthUser
from ..dependencies import get_db

router = APIRouter(tags=["Invoices"])

@router.get("/")
async def list_invoices(
    client_id: str = Query(..., description="Client ID"),
    entity_id: Optional[str] = Query(None, description="Entity ID (optional)"),
    status: Optional[str] = Query(None, description="Filter by status"),
    limit: int = Query(50, description="Number of records to return"),
    offset: int = Query(0, description="Number of records to skip"),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """List invoices for a client/entity with pagination"""
    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)

    # Base query - always filter by client_id
    query = db.collection("TRANSACTIONS").where("clientId", "==", client_id)

    # Additional filters
    if entity_id:
        query = query.where("entityId", "==", entity_id)

    # TODO: Implement more filters and pagination

    # Example placeholder response
    return {"invoices": [], "total": 0, "limit": limit, "offset": offset}

@router.get("/{transaction_id}")
async def get_invoice(
    transaction_id: str = Path(...),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Get invoice details"""
    # Get transaction record
    transaction_ref = db.collection("TRANSACTIONS").document(transaction_id)
    transaction_doc = await transaction_ref.get()

    if not transaction_doc.exists:
        raise HTTPException(status_code=404, detail="Invoice not found")

    transaction_data = transaction_doc.to_dict()
    client_id = transaction_data.get("clientId")

    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)

    # Get associated schedules
    schedules = []
    schedule_ids = transaction_data.get("_system_amortizationScheduleIDs", [])

    for schedule_id in schedule_ids:
        schedule_ref = db.collection("AMORTIZATION_SCHEDULES").document(schedule_id)
        schedule_doc = await schedule_ref.get()
        if schedule_doc.exists:
            schedules.append(schedule_doc.to_dict())

    # Get associated attachments
    attachments = []
    attachment_ids = transaction_data.get("_system_attachment_ids", [])

    for attachment_id in attachment_ids:
        attachment_ref = db.collection("ATTACHMENTS").document(attachment_id)
        attachment_doc = await attachment_ref.get()
        if attachment_doc.exists:
            attachments.append(attachment_doc.to_dict())

    # Return transaction data with associated records
    return {
        **transaction_data,
        "schedules": schedules,
        "attachments": attachments
    }