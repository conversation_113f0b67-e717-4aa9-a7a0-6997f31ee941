# API Authentication Guide

All DRCR API endpoints that access or modify protected resources require authentication. This guide details the authentication mechanism used by the API.

## Authentication Method: Firebase JWT

The DRCR API uses **JSON Web Tokens (JWTs)** issued by **Firebase Authentication**. Clients must obtain a valid Firebase ID token for a user and include it in their API requests.

## Obtaining a Firebase ID Token

Typically, client applications (e.g., web frontends, mobile apps) will use a Firebase SDK to handle user sign-in (e.g., email/password, Google Sign-In, etc.). After a user successfully signs in, the Firebase SDK provides an ID token.

**Example (Conceptual - JavaScript using Firebase SDK):**

```javascript
import firebase from 'firebase/app';
import 'firebase/auth';

// Initialize Firebase (config details omitted)
firebase.initializeApp({ /* ... your firebase config ... */ });

async function signInAndGetToken(email, password) {
  try {
    const userCredential = await firebase.auth().signInWithEmailAndPassword(email, password);
    const user = userCredential.user;
    if (user) {
      const idToken = await user.getIdToken();
      // idToken is the JWT you will send to the DRCR API
      console.log('Firebase ID Token:', idToken);
      return idToken;
    }
  } catch (error) {
    console.error('Authentication failed:', error);
    throw error;
  }
}
```

Refer to the official [Firebase Authentication documentation](https://firebase.google.com/docs/auth) for detailed instructions on integrating Firebase Authentication into your specific client platform.

## Including the Token in API Requests

Once you have a valid Firebase ID token, you must include it in the `Authorization` header of your API requests using the **Bearer** scheme.

**Format:**

`Authorization: Bearer <YOUR_FIREBASE_ID_TOKEN>`

**Example (using `curl`):**

```bash
curl -X GET "https://<your-api-domain>/api/v1/clients" \
  -H "Authorization: Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6Im..." \
  -H "Content-Type: application/json"
```

## Token Validation

The DRCR API backend automatically validates the provided Firebase ID token on protected endpoints. This validation includes:

*   Verifying the token's signature against Firebase public keys.
*   Checking the token's expiration time (`exp` claim).
*   Ensuring the token was issued for your Firebase project (`aud` and `iss` claims).

If the token is invalid, expired, or missing, the API will respond with a `401 Unauthorized` status code. Refer to the [Error Handling Documentation](../error_handling/README.md) and [Application Error Codes](../error_handling/error_codes.md) for specific error details (e.g., `TOKEN_INVALID`, `TOKEN_EXPIRED`).

## Token Expiration and Refresh

Firebase ID tokens are short-lived (typically 1 hour). Client applications are responsible for:

1.  **Monitoring token expiration**: The Firebase SDK often provides mechanisms to get notified when a token is about to expire or has expired.
2.  **Refreshing tokens**: Use the Firebase SDK's `getIdToken(true)` method (or equivalent) to force a refresh of the ID token. This usually happens seamlessly if the user's session is still valid with Firebase (i.e., the Firebase refresh token is still valid).

It's crucial to implement a robust token refresh strategy in your client application to ensure uninterrupted API access for users.

## User Context

Upon successful token validation, the DRCR API backend extracts the user's Firebase UID from the token. This UID is then used to:

*   Identify the user within the DRCR application (e.g., by looking up their profile in the `FIRM_USERS` collection).
*   Determine the user's roles and permissions for authorization checks.

If a valid Firebase user (based on UID) does not have a corresponding profile or necessary roles within the DRCR application, the API will typically respond with a `403 Forbidden` error (e.g., `USER_PROFILE_NOT_FOUND`, `ROLE_NOT_ASSIGNED`).

---

By following these guidelines, you can securely authenticate your API requests and interact with the DRCR platform. 