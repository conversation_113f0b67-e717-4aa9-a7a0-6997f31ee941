// API Response Types
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success: boolean;
}

// Client and Entity Types
export interface ClientSummary {
  client_id: string;
  name: string; // Fix: API returns 'name', not 'client_name'
  status: string;
  entities?: EntitySummary[]; // Make optional since it's not always included
  pending_items_count?: number;
  error_count?: number;
  last_activity?: string;
  overall_status?: 'ok' | 'action_needed' | 'error';
}

export interface EntitySummary {
  entity_id: string;
  entity_name: string;
  type: 'xero' | 'qbo' | 'manual';
  connection_status: 'active' | 'error' | 'disconnected' | 'syncing' | 'pending';
  entity_status: 'active' | 'error' | 'disconnected' | 'syncing' | 'pending';
  last_sync?: string;
  error_message?: string;
  sync_status?: {
    is_syncing: boolean;
    current_step: string;
    progress_percentage: number;
    estimated_remaining?: string;
    user_message: string;
    last_sync_completed?: string;
    sync_duration_warning?: string;
  };
}

export interface EntitySettings {
  entity_id: string;
  entity_name: string;
  prepayment_asset_account_codes: string[];
  excluded_pnl_account_codes?: string[];
  default_amortization_months?: number;
}

export interface Account {
  account_id?: string;
  code: string;
  name: string;
  type: 'ASSET' | 'EXPENSE' | 'LIABILITY' | 'EQUITY' | 'REVENUE' | 'CURRENT';
  class?: 'ASSET' | 'EXPENSE' | 'LIABILITY' | 'EQUITY' | 'REVENUE';
  status?: string;
  description?: string;
  raw_xero_data?: {
    Class?: string;
    ReportingCode?: string;
    [key: string]: any;
  };
}

export interface DashboardData {
  pending_review: {
    count: number;
    total_amount: number;
  };
  approved: {
    count: number;
    total_amount: number;
  };
  this_month: {
    count: number;
    total_amount: number;
  };
  recent_transactions: any[];
}

// API Filter Types
export interface FirmDashboardFilters {
  page?: number;
  limit?: number;
  clientFilter?: string;
  statusFilter?: string;
}

export interface FirmDashboardResponse {
  clients: ClientSummary[];
  pagination: {
    current_page: number;
    page_size: number;
    total_items: number;
    total_pages: number;
  };
}
