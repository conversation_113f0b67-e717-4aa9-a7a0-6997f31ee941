// Client Management Types

export enum ClientStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
  SUSPENDED = "suspended",
  PENDING = "pending"
}

export enum ClientType {
  CORPORATION = "corporation",
  LLC = "llc",
  PARTNERSHIP = "partnership",
  SOLE_PROPRIETORSHIP = "sole_proprietorship",
  NON_PROFIT = "non_profit",
  OTHER = "other"
}

export enum ClientSize {
  SMALL = "small",
  MEDIUM = "medium",
  LARGE = "large"
}

export interface ContactInfo {
  name?: string;
  email?: string;
  phone?: string;
  title?: string;
}

export interface ClientAddress {
  street?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
}

export interface ClientSettings {
  fiscal_year_end?: string; // MM-DD format
  default_currency?: string; // ISO currency code
  timezone?: string; // IANA timezone
  date_format?: string; // Preferred date format
  number_format?: string; // Number formatting locale
}

export interface ClientCreate {
  name: string;
  client_type?: ClientType;
  client_size?: ClientSize;
  industry?: string;
  website?: string;
  description?: string;
  primary_contact?: ContactInfo;
  billing_contact?: ContactInfo;
  business_address?: ClientAddress;
  billing_address?: ClientAddress;
  tax_id?: string;
  settings?: ClientSettings;
  custom_fields?: Record<string, any>;
}

export interface ClientUpdate {
  name?: string;
  client_type?: ClientType;
  client_size?: ClientSize;
  industry?: string;
  website?: string;
  description?: string;
  status?: ClientStatus;
  primary_contact?: ContactInfo;
  billing_contact?: ContactInfo;
  business_address?: ClientAddress;
  billing_address?: ClientAddress;
  tax_id?: string;
  settings?: ClientSettings;
  custom_fields?: Record<string, any>;
}

export interface ClientResponse {
  client_id: string;
  firm_id: string;
  name: string;
  client_type?: ClientType;
  client_size?: ClientSize;
  industry?: string;
  website?: string;
  description?: string;
  status: ClientStatus;
  primary_contact?: ContactInfo;
  billing_contact?: ContactInfo;
  business_address?: ClientAddress;
  billing_address?: ClientAddress;
  tax_id?: string;
  settings?: ClientSettings;
  custom_fields?: Record<string, any>;
  created_at?: string;
  updated_at?: string;
  entities_count?: number;
  active_entities_count?: number;
}

export interface ClientSummaryEnhanced {
  client_id: string;
  name: string;
  status: ClientStatus;
  client_type?: ClientType;
  industry?: string;
  entities_count: number;
  active_entities_count: number;
  pending_items_count: number;
  error_count: number;
  last_activity?: string;
  overall_status: "ok" | "action_needed" | "error";
}

export interface ClientListResponse {
  clients: ClientSummaryEnhanced[];
  pagination: {
    current_page: number;
    page_size: number;
    total_items: number;
    total_pages: number;
  };
}

// Wizard Step Types
export interface ClientWizardStep1 {
  name: string;
  client_type?: ClientType;
  client_size?: ClientSize;
  industry?: string;
}

export interface ClientWizardStep2 {
  primary_contact?: ContactInfo;
  business_address?: ClientAddress;
  website?: string;
}

export interface ClientWizardStep3 {
  settings?: ClientSettings;
  tax_id?: string;
  description?: string;
}

// Form Types
export interface ClientFormData extends ClientCreate {}

export interface ClientFilters {
  page?: number;
  limit?: number;
  name_filter?: string;
  status_filter?: string;
  client_type_filter?: string;
}

// Enum Options for UI
export interface EnumOption {
  value: string;
  label: string;
}

export interface ClientEnums {
  client_types: EnumOption[];
  client_sizes: EnumOption[];
  client_statuses: EnumOption[];
}

// API Response Types
export interface CreateClientResponse {
  message: string;
  client_id: string;
  name: string;
}

export interface UpdateClientResponse {
  message: string;
}

export interface DeleteClientResponse {
  message: string;
} 