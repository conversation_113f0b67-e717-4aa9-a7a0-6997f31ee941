# Create a Pub/Sub topic for Xero sync
resource "google_pubsub_topic" "xero_sync_topic" {
  name = var.xero_sync_topic_name
  
  depends_on = [google_project_service.services]
}

# Create a Pub/Sub subscription for the Xero sync consumer
resource "google_pubsub_subscription" "xero_sync_subscription" {
  name  = var.xero_sync_subscription_name
  topic = google_pubsub_topic.xero_sync_topic.name
  
  # Configure message retention
  message_retention_duration = "604800s"  # 7 days
  
  # Configure retry policy
  retry_policy {
    minimum_backoff = "10s"
    maximum_backoff = "600s"  # 10 minutes
  }
  
  # Configure dead letter policy
  dead_letter_policy {
    dead_letter_topic     = google_pubsub_topic.xero_sync_dead_letter_topic.id
    max_delivery_attempts = 5
  }
  
  depends_on = [google_pubsub_topic.xero_sync_dead_letter_topic]
}

# Create a dead letter topic for failed messages
resource "google_pubsub_topic" "xero_sync_dead_letter_topic" {
  name = "${var.xero_sync_topic_name}-dead-letter"
  
  depends_on = [google_project_service.services]
}

# Create a subscription for the dead letter topic
resource "google_pubsub_subscription" "xero_sync_dead_letter_subscription" {
  name  = "${var.xero_sync_subscription_name}-dead-letter"
  topic = google_pubsub_topic.xero_sync_dead_letter_topic.name
  
  # Configure message retention
  message_retention_duration = "604800s"  # 7 days
  
  depends_on = [google_pubsub_topic.xero_sync_dead_letter_topic]
}
