import React, { useState, useEffect } from 'react';
import { DraggableDialog, DraggableDialogContent, DraggableDialogHeader, DraggableDialogTitle, DraggableDialogFooter } from '@/components/ui/draggable-dialog';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, Building2, Users, Settings, AlertCircle, Save, X } from 'lucide-react';
import { ClientsService } from '@/services/clients.service';
import type {
  ClientResponse,
  ClientUpdate,
  ClientType,
  ClientSize,
  ClientStatus,
  ContactInfo,
  ClientAddress,
  ClientSettings,
  ClientEnums
} from '@/types/client.types';

interface EditClientModalProps {
  isOpen: boolean;
  onClose: () => void;
  clientId: string;
  onSuccess?: () => void;
}

interface TabInfo {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
}

export function EditClientModal({ isOpen, onClose, clientId, onSuccess }: EditClientModalProps) {
  // State
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('basic');
  const [clientData, setClientData] = useState<ClientResponse | null>(null);
  const [clientEnums, setClientEnums] = useState<ClientEnums | null>(null);
  
  // Form data
  const [formData, setFormData] = useState<ClientUpdate>({});

  const tabs: TabInfo[] = [
    {
      id: 'basic',
      title: 'Basic Information',
      description: 'Client name, type, and industry',
      icon: <Building2 className="h-4 w-4" />
    },
    {
      id: 'contact',
      title: 'Contact & Address',
      description: 'Contact details and business address',
      icon: <Users className="h-4 w-4" />
    },
    {
      id: 'settings',
      title: 'Settings & Preferences',
      description: 'Configuration and additional details',
      icon: <Settings className="h-4 w-4" />
    }
  ];

  // Load client data and enums when modal opens
  useEffect(() => {
    if (isOpen && clientId) {
      loadClientData();
      loadClientEnums();
    }
  }, [isOpen, clientId]);

  const loadClientData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const data = await ClientsService.getClientDetails(clientId);
      setClientData(data);
      
      // Initialize form data with current client data
      setFormData({
        name: data.name,
        client_type: data.client_type,
        client_size: data.client_size,
        industry: data.industry,
        website: data.website,
        description: data.description,
        status: data.status,
        primary_contact: data.primary_contact,
        business_address: data.business_address,
        tax_id: data.tax_id,
        settings: data.settings
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load client data');
    } finally {
      setIsLoading(false);
    }
  };

  const loadClientEnums = async () => {
    try {
      const enums = await ClientsService.getClientEnums();
      setClientEnums(enums);
    } catch (err) {
      console.error('Failed to load client enums:', err);
    }
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      setError(null);
      
      await ClientsService.updateClientEnhanced(clientId, formData);
      onSuccess?.();
      handleClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update client');
    } finally {
      setIsSaving(false);
    }
  };

  const handleClose = () => {
    setActiveTab('basic');
    setClientData(null);
    setFormData({});
    setError(null);
    onClose();
  };

  const updateFormData = (updates: Partial<ClientUpdate>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  };

  const updateNestedField = <T extends keyof ClientUpdate>(
    field: T,
    updates: Partial<NonNullable<ClientUpdate[T]>>
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: { ...(prev[field] as object || {}), ...updates }
    }));
  };

  const renderTabIndicator = () => (
    <div className="flex border-b mb-6">
      {tabs.map((tab) => (
        <button
          key={tab.id}
          onClick={() => setActiveTab(tab.id)}
          className={`flex items-center px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
            activeTab === tab.id
              ? 'border-blue-500 text-blue-600 bg-blue-50'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          }`}
        >
          {tab.icon}
          <span className="ml-2">{tab.title}</span>
        </button>
      ))}
    </div>
  );

  const renderBasicTab = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="client-name" className="text-sm font-medium">
            Client Name *
          </Label>
          <Input
            id="client-name"
            value={formData.name || ''}
            onChange={(e) => updateFormData({ name: e.target.value })}
            placeholder="Enter client name"
            className="mt-1"
          />
        </div>
        <div>
          <Label htmlFor="status" className="text-sm font-medium">
            Status
          </Label>
          <Select
            value={formData.status}
            onValueChange={(value) => updateFormData({ status: value as ClientStatus })}
          >
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              {clientEnums?.client_statuses.map((status) => (
                <SelectItem key={status.value} value={status.value}>
                  {status.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="client-type" className="text-sm font-medium">
            Client Type
          </Label>
          <Select
            value={formData.client_type}
            onValueChange={(value) => updateFormData({ client_type: value as ClientType })}
          >
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select client type" />
            </SelectTrigger>
            <SelectContent>
              {clientEnums?.client_types.map((type) => (
                <SelectItem key={type.value} value={type.value}>
                  {type.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="client-size" className="text-sm font-medium">
            Client Size
          </Label>
          <Select
            value={formData.client_size}
            onValueChange={(value) => updateFormData({ client_size: value as ClientSize })}
          >
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select client size" />
            </SelectTrigger>
            <SelectContent>
              {clientEnums?.client_sizes.map((size) => (
                <SelectItem key={size.value} value={size.value}>
                  {size.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div>
        <Label htmlFor="industry" className="text-sm font-medium">
          Industry
        </Label>
        <Input
          id="industry"
          value={formData.industry || ''}
          onChange={(e) => updateFormData({ industry: e.target.value })}
          placeholder="e.g., Financial Services, Retail, Professional Services"
          className="mt-1"
        />
      </div>

      <div>
        <Label htmlFor="website" className="text-sm font-medium">
          Website
        </Label>
        <Input
          id="website"
          value={formData.website || ''}
          onChange={(e) => updateFormData({ website: e.target.value })}
          placeholder="https://www.example.com"
          className="mt-1"
        />
      </div>
    </div>
  );

  const renderContactTab = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Primary Contact</CardTitle>
          <CardDescription>Main point of contact for this client</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="contact-name" className="text-sm font-medium">
                Contact Name
              </Label>
              <Input
                id="contact-name"
                value={formData.primary_contact?.name || ''}
                onChange={(e) => updateNestedField('primary_contact', { name: e.target.value })}
                placeholder="Full name"
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="contact-title" className="text-sm font-medium">
                Title
              </Label>
              <Input
                id="contact-title"
                value={formData.primary_contact?.title || ''}
                onChange={(e) => updateNestedField('primary_contact', { title: e.target.value })}
                placeholder="Job title"
                className="mt-1"
              />
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="contact-email" className="text-sm font-medium">
                Email
              </Label>
              <Input
                id="contact-email"
                type="email"
                value={formData.primary_contact?.email || ''}
                onChange={(e) => updateNestedField('primary_contact', { email: e.target.value })}
                placeholder="<EMAIL>"
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="contact-phone" className="text-sm font-medium">
                Phone
              </Label>
              <Input
                id="contact-phone"
                value={formData.primary_contact?.phone || ''}
                onChange={(e) => updateNestedField('primary_contact', { phone: e.target.value })}
                placeholder="+44 20 7946 0958"
                className="mt-1"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Business Address</CardTitle>
          <CardDescription>Primary business location</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="street" className="text-sm font-medium">
              Street Address
            </Label>
            <Input
              id="street"
              value={formData.business_address?.street || ''}
              onChange={(e) => updateNestedField('business_address', { street: e.target.value })}
              placeholder="123 Main Street"
              className="mt-1"
            />
          </div>
          <div className="grid grid-cols-3 gap-4">
            <div>
              <Label htmlFor="city" className="text-sm font-medium">
                City
              </Label>
              <Input
                id="city"
                value={formData.business_address?.city || ''}
                onChange={(e) => updateNestedField('business_address', { city: e.target.value })}
                placeholder="City"
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="state" className="text-sm font-medium">
                County
              </Label>
              <Input
                id="state"
                value={formData.business_address?.state || ''}
                onChange={(e) => updateNestedField('business_address', { state: e.target.value })}
                placeholder="Greater London"
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="postal-code" className="text-sm font-medium">
                Postcode
              </Label>
              <Input
                id="postal-code"
                value={formData.business_address?.postal_code || ''}
                onChange={(e) => updateNestedField('business_address', { postal_code: e.target.value })}
                placeholder="SW1A 1AA"
                className="mt-1"
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderSettingsTab = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Client Settings</CardTitle>
          <CardDescription>Configure preferences and defaults</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="fiscal-year-end" className="text-sm font-medium">
                Fiscal Year End
              </Label>
              <Input
                id="fiscal-year-end"
                value={formData.settings?.fiscal_year_end || ''}
                onChange={(e) => updateNestedField('settings', { fiscal_year_end: e.target.value })}
                placeholder="04-05"
                className="mt-1"
              />
              <p className="text-xs text-gray-500 mt-1">Format: MM-DD (UK tax year: 04-05)</p>
            </div>
            <div>
              <Label htmlFor="currency" className="text-sm font-medium">
                Default Currency
              </Label>
              <Select
                value={formData.settings?.default_currency}
                onValueChange={(value) => updateNestedField('settings', { default_currency: value })}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select currency" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="GBP">GBP - British Pound</SelectItem>
                  <SelectItem value="EUR">EUR - Euro</SelectItem>
                  <SelectItem value="USD">USD - US Dollar</SelectItem>
                  <SelectItem value="CAD">CAD - Canadian Dollar</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div>
            <Label htmlFor="timezone" className="text-sm font-medium">
              Timezone
            </Label>
            <Select
              value={formData.settings?.timezone}
              onValueChange={(value) => updateNestedField('settings', { timezone: value })}
            >
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Select timezone" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Europe/London">GMT/BST - London</SelectItem>
                <SelectItem value="Europe/Dublin">GMT/IST - Dublin</SelectItem>
                <SelectItem value="Europe/Paris">CET/CEST - Paris</SelectItem>
                <SelectItem value="Europe/Berlin">CET/CEST - Berlin</SelectItem>
                <SelectItem value="America/New_York">EST/EDT - New York</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Additional Information</CardTitle>
          <CardDescription>Optional details about the client</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="tax-id" className="text-sm font-medium">
              Company Number / VAT Number
            </Label>
            <Input
              id="tax-id"
              value={formData.tax_id || ''}
              onChange={(e) => updateFormData({ tax_id: e.target.value })}
              placeholder="12345678 or GB123456789"
              className="mt-1"
            />
          </div>
          <div>
            <Label htmlFor="description" className="text-sm font-medium">
              Description
            </Label>
            <Textarea
              id="description"
              value={formData.description || ''}
              onChange={(e) => updateFormData({ description: e.target.value })}
              placeholder="Additional notes about this client..."
              className="mt-1"
              rows={3}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );

  if (!isOpen) return null;

  return (
    <DraggableDialog open={isOpen} onOpenChange={handleClose}>
      <DraggableDialogContent className="flex flex-col p-0 gap-0">
        <DraggableDialogHeader className="p-6 border-b flex-shrink-0">
          <DraggableDialogTitle className="text-xl font-semibold">
            Edit Client Settings
            {clientData && <span className="text-gray-500 font-normal"> - {clientData.name}</span>}
          </DraggableDialogTitle>
        </DraggableDialogHeader>

        <div className="flex flex-col flex-grow overflow-hidden">
          {/* Tab Navigation - Fixed at top */}
          <div className="flex-shrink-0 px-6 pt-6">
            {renderTabIndicator()}

            {error && (
              <Alert variant="destructive" className="mb-4">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
          </div>

          {/* Scrollable Content Area */}
          <div className="flex-1 overflow-y-auto px-6">
            <div className="pb-6">
              {isLoading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p className="text-gray-500">Loading client data...</p>
                  </div>
                </div>
              ) : (
                <>
                  {activeTab === 'basic' && renderBasicTab()}
                  {activeTab === 'contact' && renderContactTab()}
                  {activeTab === 'settings' && renderSettingsTab()}
                </>
              )}
            </div>
          </div>

          {/* Footer - Fixed at bottom */}
          <DraggableDialogFooter className="p-6 border-t flex-shrink-0">
            <Button variant="outline" onClick={handleClose} disabled={isSaving}>
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button onClick={handleSave} disabled={isSaving || isLoading}>
              {isSaving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </>
              )}
            </Button>
          </DraggableDialogFooter>
        </div>
      </DraggableDialogContent>
    </DraggableDialog>
  );
} 