# DRCR Backend Development Guide

Welcome to the DRCR Backend Development Guide! This guide is intended for developers contributing to or maintaining the DRCR backend application. It provides essential information on setting up your development environment, understanding the codebase structure, running the application locally, testing procedures, coding standards, and deployment processes.

A consistent and well-understood development workflow is key to building a robust and maintainable application. Please familiarize yourself with the sections below.

## Overview of Topics

This guide is organized into the following sections:

*   **[Prerequisites & Setup](./setup.md)**:
    *   Software requirements (Python version, Pip, Docker, Google Cloud SDK, Firebase CLI, etc.).
    *   Cloning the repository.
    *   Setting up Python virtual environments.
    *   Installing dependencies.
    *   Configuring required environment variables and obtaining necessary credentials (e.g., Firebase service accounts, Xero API keys for testing).

*   **[Running Locally](./running_locally.md)**:
    *   Instructions for running the FastAPI application locally.
    *   Running individual cloud functions locally (if applicable, using Firebase Emulator Suite or similar tools).
    *   Connecting to local or development Firestore instances (using Firebase Emulator Suite).

*   **[Codebase Structure](./codebase_structure.md)** (Potentially link to a higher-level architecture doc or summarize here):
    *   Brief overview of key directories (`rest_api/`, `cloud_functions/`, `drcr_shared_logic/`, `terraform/`, `tests/`).
    *   Explanation of how shared logic is organized.

*   **[Testing Guide](./testing.md)**:
    *   Overview of the testing strategy (unit tests, integration tests).
    *   Instructions for running tests.
    *   Writing new tests: conventions and best practices.
    *   Using test fixtures and mock data.
    *   **Firebase token management** for API testing (token refresh procedures).

*   **[Coding Standards & Style Guide](./style_guide.md)**:
    *   Python version compatibility.
    *   Code formatting (e.g., Black, Flake8).
    *   Import ordering (e.g., isort).
    *   Naming conventions.
    *   Type hinting requirements.
    *   Logging practices.
    *   Error handling best practices (linking to the main Error Handling documentation).

*   **[Database Migrations & Seeding](./database_management.md)** (If applicable):
    *   Procedures for handling schema changes in Firestore.
    *   Scripts or methods for seeding development/testing databases.

*   **[Deployment](./deployment.md)**:
    *   Overview of the deployment process for the REST API (e.g., to Google Cloud Run).
    *   Deployment process for Cloud Functions.
    *   Managing different environments (development, staging, production).
    *   Terraform usage for infrastructure management.

*   **[Troubleshooting Common Issues](./troubleshooting.md)**:
    *   Tips for resolving common setup or runtime problems.

## Contribution Workflow

(Details to be added here or in a separate `CONTRIBUTING.md` file, covering branching strategy, pull requests, code reviews, etc.)

---

This guide aims to provide a clear path for developers to contribute effectively. If you find any information missing or unclear, please contribute to improving this documentation.