"""
Transaction Service - Business logic for transaction operations
Extracted from rest_api/routes/transactions.py for better maintainability
"""
from typing import List, Optional, Dict, Any, Tuple
from google.cloud.firestore import SERVER_TIMESTAMP
import uuid
from datetime import datetime, timezone
import logging

from ..models import invoice as invoice_models, schedule as schedule_models, attachment as attachment_models
from ..utils.transformers import (
    firestore_transaction_to_invoice_model, 
    firestore_to_schedule_model,
    firestore_to_attachment_model
)

logger = logging.getLogger(__name__)


class TransactionService:
    """Service for handling transaction business logic"""
    
    def __init__(self, db):
        self.db = db
    
    async def list_transactions_paginated(
        self,
        client_id: str,
        entity_id: Optional[str] = None,
        transaction_type: Optional[str] = None,
        status: Optional[str] = None,
        page: int = 1,
        limit: int = 50
    ) -> Tuple[List[invoice_models.Invoice], int, int]:
        """
        List transactions with pagination and filtering.
        Returns: (transactions, total_count, total_pages)
        """
        offset = (page - 1) * limit
        base_query = self.db.collection("TRANSACTIONS").where("clientId", "==", client_id)

        # Apply filters
        query_filters = []
        if entity_id:
            query_filters.append(("entityId", "==", entity_id))
        if status:
            query_filters.append(("status", "==", status))
        if transaction_type:
            query_filters.append(("transactionType", "==", transaction_type))

        current_query = base_query
        for field, op, value in query_filters:
            current_query = current_query.where(field, op, value)
        
        # Get paginated data
        transaction_docs_snapshot = await current_query.limit(limit).offset(offset).get()

        transformed_transactions: List[invoice_models.Invoice] = []
        for doc in transaction_docs_snapshot:
            transformed_invoice = firestore_transaction_to_invoice_model(doc.id, doc.to_dict())
            if transformed_invoice:
                transformed_transactions.append(transformed_invoice)
            else:
                logger.warning(f"Could not transform transaction {doc.id} for list view. Skipping.")

        # Get total count
        total_query = base_query
        for field, op, value in query_filters:
            total_query = total_query.where(field, op, value)
        
        total_docs_snapshot = await total_query.get()
        total = len(total_docs_snapshot)
        total_pages = (total + limit - 1) // limit if total > 0 else 0

        return transformed_transactions, total, total_pages

    async def get_dashboard_transactions(
        self,
        user_client_ids: List[str],
        entity_id: Optional[str] = None,
        status: Optional[str] = None,
        transaction_type: Optional[str] = None,
        require_action: bool = False,
        page: int = 1,
        limit: int = 20
    ) -> Tuple[List[Dict[str, Any]], int, int]:
        """
        Get dashboard transactions with schedules.
        Returns: (dashboard_items, total_count, total_pages)
        """
        offset = (page - 1) * limit

        if not user_client_ids:
            return [], 0, 0

        # Collect all potential transactions
        all_potential_transaction_docs = []
        for c_id in user_client_ids:
            query = self.db.collection("TRANSACTIONS").where("clientId", "==", c_id)
            if entity_id:
                query = query.where("entityId", "==", entity_id)
            if status:
                query = query.where("status", "==", status)
            if transaction_type:
                query = query.where("transactionType", "==", transaction_type)
            
            docs_snapshot = await query.get()
            for doc_snap in docs_snapshot:
                all_potential_transaction_docs.append(doc_snap)

        # Process dashboard items
        dashboard_items = []
        for doc in all_potential_transaction_docs:
            dashboard_item = await self._process_dashboard_transaction(doc, require_action)
            if dashboard_item:
                dashboard_items.append(dashboard_item)

        # Apply pagination
        total = len(dashboard_items)
        total_pages = (total + limit - 1) // limit if total > 0 else 0
        
        start_idx = offset
        end_idx = min(offset + limit, total)
        paginated_items = dashboard_items[start_idx:end_idx]

        return paginated_items, total, total_pages

    async def _process_dashboard_transaction(
        self, 
        doc, 
        require_action: bool = False
    ) -> Optional[Dict[str, Any]]:
        """Process a single transaction for dashboard view"""
        transaction_dict = doc.to_dict()
        api_transaction = firestore_transaction_to_invoice_model(doc.id, transaction_dict)

        if not api_transaction:
            logger.warning(f"Dashboard: Skipping transaction {doc.id}, failed to transform.")
            return None

        transformed_schedules: List[schedule_models.Schedule] = []
        schedule_ids = transaction_dict.get("_system_amortizationScheduleIDs", [])
        has_actionable_schedule = False

        if schedule_ids:
            for schedule_id_str in schedule_ids:
                schedule_ref = self.db.collection("AMORTIZATION_SCHEDULES").document(schedule_id_str)
                schedule_doc_snap = await schedule_ref.get()
                if schedule_doc_snap.exists:
                    schedule_dict = schedule_doc_snap.to_dict()
                    api_schedule = firestore_to_schedule_model(schedule_doc_snap.id, schedule_dict)
                    if api_schedule:
                        transformed_schedules.append(api_schedule)
                        if api_schedule.status in [
                            schedule_models.ScheduleStatus.PENDING_REVIEW, 
                            schedule_models.ScheduleStatus.PENDING_CONFIRMATION
                        ]:
                            has_actionable_schedule = True

        # Filter by action requirement
        if require_action and not has_actionable_schedule:
            return None

        return {
            "transaction": api_transaction,
            "schedules": transformed_schedules
        }

    async def get_transaction_by_id(self, transaction_id: str) -> Optional[invoice_models.Invoice]:
        """Get a single transaction by ID"""
        transaction_ref = self.db.collection("TRANSACTIONS").document(transaction_id)
        transaction_doc = await transaction_ref.get()
        
        if not transaction_doc.exists:
            return None
        
        transaction_dict = transaction_doc.to_dict()
        return firestore_transaction_to_invoice_model(transaction_id, transaction_dict)

    async def get_transaction_schedules(self, transaction_id: str) -> List[schedule_models.Schedule]:
        """Get schedules for a transaction"""
        transaction_ref = self.db.collection("TRANSACTIONS").document(transaction_id)
        transaction_doc = await transaction_ref.get()
        
        if not transaction_doc.exists:
            return []
        
        transaction_dict = transaction_doc.to_dict()
        schedule_ids = transaction_dict.get("_system_amortizationScheduleIDs", [])
        
        schedules = []
        for schedule_id in schedule_ids:
            schedule_ref = self.db.collection("AMORTIZATION_SCHEDULES").document(schedule_id)
            schedule_doc = await schedule_ref.get()
            if schedule_doc.exists:
                schedule_dict = schedule_doc.to_dict()
                api_schedule = firestore_to_schedule_model(schedule_id, schedule_dict)
                if api_schedule:
                    schedules.append(api_schedule)
        
        return schedules

    async def get_transaction_attachments(self, transaction_id: str) -> List[attachment_models.Attachment]:
        """Get attachments for a transaction"""
        attachments_query = self.db.collection("ATTACHMENTS").where("transactionId", "==", transaction_id)
        attachments_snapshot = await attachments_query.get()
        
        attachments = []
        for doc in attachments_snapshot:
            attachment_dict = doc.to_dict()
            api_attachment = firestore_to_attachment_model(doc.id, attachment_dict)
            if api_attachment:
                attachments.append(api_attachment)
        
        return attachments

    async def create_transaction(
        self, 
        invoice_data: invoice_models.InvoiceCreate, 
        client_id: str, 
        entity_id: str,
        current_user
    ) -> invoice_models.Invoice:
        """Create a new transaction"""
        transaction_id = str(uuid.uuid4())
        
        transaction_data = {
            "clientId": client_id,
            "entityId": entity_id,
            "type": invoice_data.type.value if invoice_data.type else None,
            "subType": invoice_data.sub_type.value if invoice_data.sub_type else None,
            "total": invoice_data.total,
            "currency": invoice_data.currency,
            "date": invoice_data.date.isoformat() if invoice_data.date else None,
            "dueDate": invoice_data.due_date.isoformat() if invoice_data.due_date else None,
            "contact": invoice_data.contact.dict() if invoice_data.contact else None,
            "lineItems": [item.dict() for item in invoice_data.line_items] if invoice_data.line_items else [],
            "reference": invoice_data.reference,
            "status": invoice_data.status.value if invoice_data.status else None,
            "_system_createdBy": current_user.uid,
            "_system_createdAt": SERVER_TIMESTAMP,
            "_system_modifiedAt": SERVER_TIMESTAMP,
            "_system_amortizationScheduleIDs": []
        }
        
        transaction_ref = self.db.collection("TRANSACTIONS").document(transaction_id)
        await transaction_ref.set(transaction_data)
        
        return firestore_transaction_to_invoice_model(transaction_id, transaction_data)

    async def update_transaction(
        self, 
        transaction_id: str, 
        invoice_data: invoice_models.InvoiceUpdate,
        current_user
    ) -> Optional[invoice_models.Invoice]:
        """Update an existing transaction"""
        transaction_ref = self.db.collection("TRANSACTIONS").document(transaction_id)
        transaction_doc = await transaction_ref.get()
        
        if not transaction_doc.exists:
            return None
        
        update_data = {}
        
        if invoice_data.type is not None:
            update_data["type"] = invoice_data.type.value
        if invoice_data.sub_type is not None:
            update_data["subType"] = invoice_data.sub_type.value
        if invoice_data.total is not None:
            update_data["total"] = invoice_data.total
        if invoice_data.currency is not None:
            update_data["currency"] = invoice_data.currency
        if invoice_data.date is not None:
            update_data["date"] = invoice_data.date.isoformat()
        if invoice_data.due_date is not None:
            update_data["dueDate"] = invoice_data.due_date.isoformat()
        if invoice_data.contact is not None:
            update_data["contact"] = invoice_data.contact.dict()
        if invoice_data.line_items is not None:
            update_data["lineItems"] = [item.dict() for item in invoice_data.line_items]
        if invoice_data.reference is not None:
            update_data["reference"] = invoice_data.reference
        if invoice_data.status is not None:
            update_data["status"] = invoice_data.status.value
        
        update_data["_system_modifiedAt"] = SERVER_TIMESTAMP
        update_data["_system_modifiedBy"] = current_user.uid
        
        await transaction_ref.update(update_data)
        
        updated_doc = await transaction_ref.get()
        updated_dict = updated_doc.to_dict()
        
        return firestore_transaction_to_invoice_model(transaction_id, updated_dict)

    async def delete_transaction(self, transaction_id: str) -> bool:
        """Delete a transaction"""
        transaction_ref = self.db.collection("TRANSACTIONS").document(transaction_id)
        transaction_doc = await transaction_ref.get()
        
        if not transaction_doc.exists:
            return False
        
        await transaction_ref.delete()
        return True

    async def get_user_client_ids(self, current_user, client_id: Optional[str] = None) -> List[str]:
        """Get list of client IDs the user has access to"""
        if client_id:
            return [client_id]
        
        user_client_ids = current_user.assigned_client_ids or []
        if not user_client_ids and current_user.role == "firm_admin":
            clients_query = self.db.collection("CLIENTS").where("firm_id", "==", current_user.firm_id)
            client_docs_snapshot = await clients_query.get()
            user_client_ids = [doc.id for doc in client_docs_snapshot]
        
        return user_client_ids 