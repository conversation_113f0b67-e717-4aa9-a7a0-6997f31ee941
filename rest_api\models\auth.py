from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field
from datetime import datetime

# Base models with common fields
class BaseFirestoreModel(BaseModel):
    """Base model with common fields for all Firestore documents"""
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

# Firm-related models
class FirmModel(BaseFirestoreModel):
    """Represents a firm in the FIRMS collection"""
    firm_id: str
    name: str
    address: Optional[Dict[str, Any]] = None
    contact_email: Optional[str] = None
    contact_phone: Optional[str] = None
    status: str = "active"  # active, inactive, suspended
    subscription_tier: str = "standard"  # free, standard, premium
    subscription_expiry: Optional[datetime] = None
    settings: Optional[Dict[str, Any]] = None

class FirmUserModel(BaseFirestoreModel):
    """Represents a user in the FIRM_USERS collection"""
    user_id: str  # Firebase Auth UID
    firm_id: str
    email: str
    display_name: Optional[str] = None
    role: str  # firm_admin, firm_staff
    assigned_client_ids: List[str] = []  # List of client IDs this user can access
    status: str = "active"  # active, inactive, invited
    last_login: Optional[datetime] = None
    permissions: Optional[Dict[str, bool]] = None

# Client-related models
class ClientModel(BaseFirestoreModel):
    """Represents a client in the CLIENTS collection"""
    client_id: str
    firm_id: str
    name: str
    contact_name: Optional[str] = None
    contact_email: Optional[str] = None
    contact_phone: Optional[str] = None
    status: str = "active"  # active, inactive
    settings: Optional[Dict[str, Any]] = None

class ClientUserModel(BaseFirestoreModel):
    """Represents a user in the CLIENT_USERS collection"""
    user_id: str  # Firebase Auth UID
    client_id: str
    email: str
    display_name: Optional[str] = None
    role: str  # client_admin, client_staff
    status: str = "active"  # active, inactive, invited
    last_login: Optional[datetime] = None
    permissions: Optional[Dict[str, bool]] = None

# Entity-related models
class EntityModel(BaseFirestoreModel):
    """Represents an entity in the ENTITIES collection"""
    entity_id: str  # For Xero, this is the Xero Tenant ID
    client_id: str
    entity_name: str
    type: str = "xero"  # xero, quickbooks, etc.
    status: str = "active"  # active, inactive, disconnected
    connection_details: Dict[str, Any] = Field(
        default_factory=lambda: {
            "status": "pending"
        }
    )

class EntitySettingsModel(BaseFirestoreModel):
    """Represents settings in the ENTITY_SETTINGS collection"""
    entity_id: str
    client_id: str
    prepayment_asset_account_codes: List[str] = []
    excluded_pnl_account_codes: List[str] = []
    default_amortization_months: int = 12  # Default duration for derived service periods