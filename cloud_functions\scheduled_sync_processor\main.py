import json
import os
import logging
import asyncio
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any
import base64

import functions_framework
from google.cloud import firestore
from google.cloud import pubsub_v1

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Firestore client
db = firestore.AsyncClient()

# Initialize Pub/Sub publisher
publisher = pubsub_v1.PublisherClient()


@functions_framework.cloud_event
def scheduled_sync_processor(cloud_event):
    """
    Cloud Function triggered by Cloud Scheduler via Pub/Sub to process scheduled syncs.
    
    This function:
    1. Receives scheduled sync messages (hourly, daily, weekly)
    2. Queries entities with matching sync frequencies
    3. Checks if entities need syncing based on last sync timestamps
    4. Triggers individual entity syncs for those that are due
    """
    try:
        # Decode the Pub/Sub message
        message_data = base64.b64decode(cloud_event.data["message"]["data"]).decode()
        sync_request = json.loads(message_data)
        
        logger.info(f"Processing scheduled sync request: {sync_request}")
        
        # Run the async processing
        asyncio.run(process_scheduled_sync(sync_request))
        
    except Exception as e:
        logger.error(f"Error processing scheduled sync: {e}")
        raise


async def process_scheduled_sync(sync_request: Dict[str, Any]):
    """Process the scheduled sync request asynchronously."""
    
    sync_frequency = sync_request.get("sync_frequency")
    trigger_type = sync_request.get("trigger_type", "scheduled")
    
    if not sync_frequency:
        logger.error("No sync_frequency provided in scheduled sync request")
        return
    
    logger.info(f"Processing {sync_frequency} scheduled sync")
    
    try:
        # Query entities with matching sync frequency and auto_sync_enabled
        entities_to_sync = await get_entities_due_for_sync(sync_frequency)
        
        logger.info(f"Found {len(entities_to_sync)} entities due for {sync_frequency} sync")
        
        # Process each entity
        sync_results = []
        for entity in entities_to_sync:
            try:
                result = await trigger_entity_sync(entity, trigger_type)
                sync_results.append(result)
            except Exception as e:
                logger.error(f"Failed to trigger sync for entity {entity['entity_id']}: {e}")
                sync_results.append({
                    "entity_id": entity["entity_id"],
                    "status": "failed",
                    "error": str(e)
                })
        
        # Log summary
        successful_syncs = len([r for r in sync_results if r["status"] == "triggered"])
        failed_syncs = len([r for r in sync_results if r["status"] == "failed"])
        
        logger.info(f"Scheduled sync summary - Frequency: {sync_frequency}, "
                   f"Successful: {successful_syncs}, Failed: {failed_syncs}")
        
    except Exception as e:
        logger.error(f"Error in process_scheduled_sync: {e}")
        raise


async def get_entities_due_for_sync(sync_frequency: str) -> List[Dict[str, Any]]:
    """
    Get entities that are due for sync based on their frequency and last sync timestamps.
    """
    entities_due = []
    
    try:
        # Query entity settings with matching sync frequency and auto_sync_enabled
        settings_query = db.collection("ENTITY_SETTINGS").where(
            "sync_frequency", "==", sync_frequency
        ).where(
            "auto_sync_enabled", "==", True
        )
        
        settings_docs = settings_query.stream()
        
        async for settings_doc in settings_docs:
            settings_data = settings_doc.to_dict()
            entity_id = settings_data.get("entity_id")
            
            if not entity_id:
                continue
            
            # Check if entity needs syncing based on last sync timestamps
            if await should_sync_entity(settings_data, sync_frequency):
                # Get entity details
                entity_ref = db.collection("ENTITIES").document(entity_id)
                entity_doc = await entity_ref.get()
                
                if entity_doc.exists:
                    entity_data = entity_doc.to_dict()
                    entities_due.append({
                        "entity_id": entity_id,
                        "client_id": settings_data.get("client_id"),
                        "entity_name": entity_data.get("entity_name"),
                        "settings": settings_data
                    })
    
    except Exception as e:
        logger.error(f"Error querying entities for sync: {e}")
        raise
    
    return entities_due


async def should_sync_entity(settings_data: Dict[str, Any], sync_frequency: str) -> bool:
    """
    Determine if an entity should be synced based on its last sync timestamps and frequency.
    """
    try:
        # Get the most recent sync timestamp across all document types
        last_sync_timestamps = []
        
        sync_timestamp_fields = [
            "_system_lastSyncTimestampUtc_Accounts",
            "_system_lastSyncTimestampUtc_Bills", 
            "_system_lastSyncTimestampUtc_Contacts",
            "_system_lastSyncTimestampUtc_Invoices",
            "_system_lastSyncTimestampUtc_JournalEntries",
            "_system_lastSyncTimestampUtc_SpendMoney"
        ]
        
        for field in sync_timestamp_fields:
            timestamp_str = settings_data.get(field)
            if timestamp_str:
                try:
                    # Parse the timestamp string
                    timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                    last_sync_timestamps.append(timestamp)
                except ValueError as e:
                    logger.warning(f"Could not parse timestamp {timestamp_str}: {e}")
        
        # If no sync timestamps found, entity needs initial sync
        if not last_sync_timestamps:
            logger.info(f"Entity {settings_data.get('entity_id')} has no sync timestamps - needs initial sync")
            return True
        
        # Get the most recent sync timestamp
        last_sync = max(last_sync_timestamps)
        now = datetime.now(last_sync.tzinfo)
        time_since_last_sync = now - last_sync
        
        # Determine if sync is due based on frequency
        if sync_frequency == "hourly":
            return time_since_last_sync >= timedelta(hours=1)
        elif sync_frequency == "daily":
            return time_since_last_sync >= timedelta(days=1)
        elif sync_frequency == "weekly":
            return time_since_last_sync >= timedelta(weeks=1)
        else:
            # Manual sync frequency - don't auto-sync
            return False
    
    except Exception as e:
        logger.error(f"Error checking if entity should sync: {e}")
        # Default to not syncing if there's an error
        return False


async def trigger_entity_sync(entity: Dict[str, Any], trigger_type: str) -> Dict[str, Any]:
    """
    Trigger a sync for a specific entity by publishing to the xero-sync topic.
    """
    try:
        entity_id = entity["entity_id"]
        client_id = entity["client_id"]
        settings = entity["settings"]
        
        # Determine endpoints to sync based on settings
        endpoints_to_sync = ["Contacts", "Accounts"]  # Always sync these
        
        if settings.get("sync_invoices", True):
            endpoints_to_sync.append("Invoices")
        if settings.get("sync_bills", True):
            endpoints_to_sync.append("Bills")
        if settings.get("sync_spend_money", True):
            endpoints_to_sync.append("SpendMoney")
        if settings.get("sync_journal_entries", True):
            endpoints_to_sync.append("JournalEntries")
        if settings.get("sync_payments", False):
            endpoints_to_sync.append("Payments")
        if settings.get("sync_bank_transactions", False):
            endpoints_to_sync.append("BankTransactions")
        
        # Create sync message
        sync_message = {
            "platformOrgId": entity_id,
            "tenantId": client_id,
            "syncJobId": f"scheduled-{datetime.now().strftime('%Y%m%d-%H%M%S')}-{entity_id[:8]}",
            "endpoints": endpoints_to_sync,
            "forceFullSyncEndpoints": [],  # Don't force full sync for scheduled syncs
            "targetDate": datetime.now().strftime("%Y-%m-%d"),
            "reason": f"scheduled_{settings.get('sync_frequency', 'unknown')}_sync",
            "triggered_by_scheduler": True,
            "trigger_type": trigger_type
        }
        
        # Add transaction sync start date if available
        if settings.get("transaction_sync_start_date"):
            sync_message["transactionSyncStartDate"] = settings["transaction_sync_start_date"]
        
        # Publish to xero-sync topic
        project_id = os.getenv("GCP_PROJECT_ID")
        topic_name = os.getenv("PUBSUB_TOPIC_XERO_SYNC", "xero-sync-topic")
        
        if not project_id:
            raise ValueError("GCP_PROJECT_ID environment variable not set")
        
        topic_path = publisher.topic_path(project_id, topic_name)
        message_data = json.dumps(sync_message).encode("utf-8")
        
        # Publish the message
        future = publisher.publish(topic_path, message_data)
        message_id = future.result(timeout=30)
        
        logger.info(f"Triggered scheduled sync for entity {entity_id} "
                   f"({entity.get('entity_name', 'Unknown')}) - Message ID: {message_id}")
        
        return {
            "entity_id": entity_id,
            "entity_name": entity.get("entity_name"),
            "status": "triggered",
            "message_id": message_id,
            "endpoints": endpoints_to_sync,
            "sync_frequency": settings.get("sync_frequency")
        }
        
    except Exception as e:
        logger.error(f"Failed to trigger sync for entity {entity['entity_id']}: {e}")
        return {
            "entity_id": entity["entity_id"],
            "entity_name": entity.get("entity_name"),
            "status": "failed",
            "error": str(e)
        }


if __name__ == "__main__":
    # For local testing
    import sys
    if len(sys.argv) > 1:
        test_frequency = sys.argv[1]
        test_request = {
            "sync_frequency": test_frequency,
            "trigger_type": "manual_test",
            "timestamp": datetime.now().isoformat()
        }
        asyncio.run(process_scheduled_sync(test_request)) 