import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { XeroOperationStatus, useXeroOperationState, XeroConnectionBadge } from '@/components/ui/xero-operation-status';
import { toast } from 'sonner';

export function XeroOperationDemo() {
  const [demoConnectionStatus, setDemoConnectionStatus] = useState<'connected' | 'disconnected' | 'error' | 'pending' | 'expired' | 'syncing'>('disconnected');
  
  // Demo operation states
  const connectState = useXeroOperationState(45);
  const disconnectState = useXeroOperationState(30);
  const reconnectState = useXeroOperationState(45);
  const syncState = useXeroOperationState(60);

  const simulateOperation = async (operationState: any, operationType: string, duration: number = 3000) => {
    operationState.startOperation(operationType);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, duration));
    
    // Simulate success (90% success rate for demo)
    const success = Math.random() > 0.1;
    operationState.completeOperation(success);
    
    if (success) {
      toast.success(`${operationType} completed successfully!`);
      if (operationType === 'connect' || operationType === 'reconnect') {
        setDemoConnectionStatus('connected');
      } else if (operationType === 'disconnect') {
        setDemoConnectionStatus('disconnected');
      }
    } else {
      toast.error(`${operationType} failed`);
    }
  };

  const handleConnect = () => {
    setDemoConnectionStatus('pending');
    simulateOperation(connectState, 'connect', 4000);
  };

  const handleDisconnect = () => {
    simulateOperation(disconnectState, 'disconnect', 2500);
  };

  const handleReconnect = () => {
    setDemoConnectionStatus('pending');
    simulateOperation(reconnectState, 'reconnect', 3500);
  };

  const handleSync = () => {
    setDemoConnectionStatus('syncing');
    simulateOperation(syncState, 'sync', 5000).then(() => {
      if (demoConnectionStatus !== 'disconnected') {
        setDemoConnectionStatus('connected');
      }
    });
  };

  const resetDemo = () => {
    connectState.resetOperation();
    disconnectState.resetOperation();
    reconnectState.resetOperation();
    syncState.resetOperation();
    setDemoConnectionStatus('disconnected');
    toast.info('Demo reset');
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">Xero Operation Status Demo</h1>
        <p className="text-muted-foreground">
          Demonstration of enhanced loading states and progress indicators for Xero API operations
        </p>
      </div>

      {/* Current Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            Current Connection Status
            <XeroConnectionBadge status={demoConnectionStatus} size="md" />
          </CardTitle>
          <CardDescription>
            This shows the current state of the demo Xero connection
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Operation Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Operation Controls</CardTitle>
          <CardDescription>
            Click these buttons to simulate different Xero operations and see the enhanced loading states
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button 
              onClick={handleConnect}
              disabled={connectState.isLoading || demoConnectionStatus === 'connected'}
              variant={demoConnectionStatus === 'connected' ? 'secondary' : 'default'}
            >
              {connectState.isLoading ? 'Connecting...' : 'Connect'}
            </Button>
            
            <Button 
              onClick={handleDisconnect}
              disabled={disconnectState.isLoading || demoConnectionStatus === 'disconnected'}
              variant="outline"
            >
              {disconnectState.isLoading ? 'Disconnecting...' : 'Disconnect'}
            </Button>
            
            <Button 
              onClick={handleReconnect}
              disabled={reconnectState.isLoading || demoConnectionStatus === 'connected'}
              variant="outline"
            >
              {reconnectState.isLoading ? 'Reconnecting...' : 'Reconnect'}
            </Button>
            
            <Button 
              onClick={handleSync}
              disabled={syncState.isLoading || demoConnectionStatus === 'disconnected'}
              variant="outline"
            >
              {syncState.isLoading ? 'Syncing...' : 'Sync Data'}
            </Button>
          </div>
          
          <Separator />
          
          <div className="flex justify-center">
            <Button onClick={resetDemo} variant="ghost" size="sm">
              Reset Demo
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Active Operations Display */}
      {(connectState.isLoading || disconnectState.isLoading || reconnectState.isLoading || syncState.isLoading) && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Active Operations</h3>
          
          {connectState.isLoading && (
            <XeroOperationStatus
              operation="connect"
              status={connectState.status}
              variant="detailed"
              showProgress={true}
              showElapsedTime={true}
              timeoutSeconds={45}
              elapsedSeconds={connectState.elapsedSeconds}
              onRetry={() => {
                connectState.resetOperation();
                handleConnect();
              }}
              onCancel={() => {
                connectState.resetOperation();
                setDemoConnectionStatus('disconnected');
              }}
            />
          )}
          
          {disconnectState.isLoading && (
            <XeroOperationStatus
              operation="disconnect"
              status={disconnectState.status}
              variant="detailed"
              showProgress={true}
              showElapsedTime={true}
              timeoutSeconds={30}
              elapsedSeconds={disconnectState.elapsedSeconds}
              onRetry={() => {
                disconnectState.resetOperation();
                handleDisconnect();
              }}
              onCancel={() => {
                disconnectState.resetOperation();
                setDemoConnectionStatus('connected');
              }}
            />
          )}
          
          {reconnectState.isLoading && (
            <XeroOperationStatus
              operation="reconnect"
              status={reconnectState.status}
              variant="detailed"
              showProgress={true}
              showElapsedTime={true}
              timeoutSeconds={45}
              elapsedSeconds={reconnectState.elapsedSeconds}
              onRetry={() => {
                reconnectState.resetOperation();
                handleReconnect();
              }}
              onCancel={() => {
                reconnectState.resetOperation();
                setDemoConnectionStatus('error');
              }}
            />
          )}
          
          {syncState.isLoading && (
            <XeroOperationStatus
              operation="sync"
              status={syncState.status}
              variant="detailed"
              showProgress={true}
              showElapsedTime={true}
              timeoutSeconds={60}
              elapsedSeconds={syncState.elapsedSeconds}
              onRetry={() => {
                syncState.resetOperation();
                handleSync();
              }}
              onCancel={() => {
                syncState.resetOperation();
                setDemoConnectionStatus('connected');
              }}
            />
          )}
        </div>
      )}

      {/* Compact Variants Demo */}
      <Card>
        <CardHeader>
          <CardTitle>Compact Status Indicators</CardTitle>
          <CardDescription>
            These compact variants can be used inline within other components
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex items-center gap-4">
              <span className="w-24 text-sm font-medium">Connect:</span>
              {connectState.isLoading ? (
                <XeroOperationStatus
                  operation="connect"
                  status={connectState.status}
                  variant="compact"
                  showElapsedTime={true}
                  elapsedSeconds={connectState.elapsedSeconds}
                  size="sm"
                />
              ) : (
                <Badge variant="outline">Idle</Badge>
              )}
            </div>
            
            <div className="flex items-center gap-4">
              <span className="w-24 text-sm font-medium">Disconnect:</span>
              {disconnectState.isLoading ? (
                <XeroOperationStatus
                  operation="disconnect"
                  status={disconnectState.status}
                  variant="compact"
                  showElapsedTime={true}
                  elapsedSeconds={disconnectState.elapsedSeconds}
                  size="sm"
                />
              ) : (
                <Badge variant="outline">Idle</Badge>
              )}
            </div>
            
            <div className="flex items-center gap-4">
              <span className="w-24 text-sm font-medium">Reconnect:</span>
              {reconnectState.isLoading ? (
                <XeroOperationStatus
                  operation="reconnect"
                  status={reconnectState.status}
                  variant="compact"
                  showElapsedTime={true}
                  elapsedSeconds={reconnectState.elapsedSeconds}
                  size="sm"
                />
              ) : (
                <Badge variant="outline">Idle</Badge>
              )}
            </div>
            
            <div className="flex items-center gap-4">
              <span className="w-24 text-sm font-medium">Sync:</span>
              {syncState.isLoading ? (
                <XeroOperationStatus
                  operation="sync"
                  status={syncState.status}
                  variant="compact"
                  showElapsedTime={true}
                  elapsedSeconds={syncState.elapsedSeconds}
                  size="sm"
                />
              ) : (
                <Badge variant="outline">Idle</Badge>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Features Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Enhanced Features</CardTitle>
          <CardDescription>
            Key improvements implemented for better user experience
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-2">Loading States & Progress</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Real-time elapsed time tracking</li>
                <li>• Progress bars with timeout indicators</li>
                <li>• Descriptive status messages</li>
                <li>• Visual loading spinners</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-2">Error Handling & Recovery</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Timeout detection and warnings</li>
                <li>• Retry functionality for failed operations</li>
                <li>• Cancel operation capability</li>
                <li>• Detailed error messages</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-2">User Interface</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Multiple display variants (detailed, compact)</li>
                <li>• Disabled buttons during operations</li>
                <li>• Toast notifications for feedback</li>
                <li>• Connection status badges</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-2">Performance</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Prevents multiple simultaneous operations</li>
                <li>• Automatic cleanup of timers</li>
                <li>• Responsive design for all screen sizes</li>
                <li>• Optimized re-renders</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 