# Windows Development Setup Guide

## 🪟 **Windows-Specific Performance Optimizations**

This guide provides Windows-specific optimizations for the DRCR frontend development environment.

### **1. Environment Configuration**

Create a `.env.local` file in your project root with these Windows-optimized settings:

```env
# Windows-specific development environment configuration
VITE_API_BASE_URL=http://127.0.0.1:8081
VITE_API_TIMEOUT=8000

# Performance settings for Windows
VITE_CACHE_TTL=300000
VITE_REQUEST_DEDUP_WINDOW=500

# Debug settings
VITE_DEBUG_API=true
VITE_DEBUG_AUTH=true
```

### **2. Windows Networking Optimizations**

#### **Use 127.0.0.1 instead of localhost**
Windows DNS resolution for `localhost` can be slow. Use `127.0.0.1` instead:

```env
VITE_API_BASE_URL=http://127.0.0.1:8081
```

#### **Disable IPv6 for Development (Optional)**
If you're experiencing slow connections, you can disable IPv6:

1. Open Command Prompt as Administrator
2. Run: `netsh interface ipv6 set global randomizeidentifiers=disabled`
3. Run: `netsh interface ipv6 set privacy state=disabled`

### **3. Windows Firewall Configuration**

Ensure Windows Firewall allows your development servers:

1. Open Windows Defender Firewall
2. Click "Allow an app or feature through Windows Defender Firewall"
3. Add Node.js and your development server ports (3000, 8081)

### **4. Performance Monitoring**

#### **Task Manager Monitoring**
Monitor these processes during development:
- Node.js (frontend dev server)
- Python (backend server)
- Browser processes

#### **Network Performance**
Use these commands to check network performance:

```cmd
# Check localhost connectivity
ping 127.0.0.1

# Check port availability
netstat -an | findstr :8081
netstat -an | findstr :3000

# Test API connectivity
curl http://127.0.0.1:8081/health
```

### **5. Development Server Optimizations**

#### **Vite Configuration for Windows**
Add these optimizations to your `vite.config.ts`:

```typescript
export default defineConfig({
  server: {
    host: '127.0.0.1', // Use IPv4 explicitly
    port: 3000,
    strictPort: true,
    // Windows-specific optimizations
    fs: {
      strict: false // Allow serving files outside root
    }
  },
  // Optimize for Windows file system
  optimizeDeps: {
    force: true // Force dependency optimization
  }
});
```

### **6. Common Windows Issues & Solutions**

#### **Slow API Requests**
If `/auth/me` or other API requests are slow (>1s):

1. **Check antivirus software**: Temporarily disable real-time scanning for your project folder
2. **Use 127.0.0.1**: Replace `localhost` with `127.0.0.1` in all configurations
3. **Check Windows Defender**: Add your project folder to exclusions
4. **Monitor Task Manager**: Look for high CPU/memory usage

#### **Port Conflicts**
If ports are already in use:

```cmd
# Find what's using port 8081
netstat -ano | findstr :8081

# Kill process by PID (replace XXXX with actual PID)
taskkill /PID XXXX /F
```

#### **File Watching Issues**
If hot reload isn't working:

```cmd
# Increase file watcher limit
echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf
```

### **7. Backend Performance on Windows**

#### **Python/FastAPI Optimizations**
For the backend server, use these optimizations:

```bash
# Use uvicorn with Windows-specific settings
uvicorn main:app --host 127.0.0.1 --port 8081 --reload --loop asyncio
```

#### **Environment Variables**
Set these for better Windows performance:

```env
PYTHONUNBUFFERED=1
PYTHONDONTWRITEBYTECODE=1
```

### **8. Browser Optimizations**

#### **Chrome DevTools Settings**
1. Open DevTools (F12)
2. Go to Settings (gear icon)
3. Enable "Disable cache (while DevTools is open)"
4. Set Network throttling to "No throttling"

#### **Browser Extensions**
Disable unnecessary extensions during development to improve performance.

### **9. Troubleshooting Checklist**

When experiencing slow performance:

- [ ] Check if using `127.0.0.1` instead of `localhost`
- [ ] Verify Windows Firewall settings
- [ ] Check antivirus real-time scanning
- [ ] Monitor Task Manager for resource usage
- [ ] Test API connectivity with curl
- [ ] Check for port conflicts
- [ ] Verify environment variables
- [ ] Clear browser cache and cookies
- [ ] Restart development servers

### **10. Performance Benchmarks**

Expected performance on Windows 10:

- **Frontend dev server startup**: < 10 seconds
- **Backend server startup**: < 5 seconds
- **API health check**: < 100ms
- **Authentication requests**: < 500ms
- **Dashboard data loading**: < 1 second

If you're not meeting these benchmarks, review the optimizations above.

---

**Note**: These optimizations are specifically for Windows development environments. Production deployments should use the standard configuration. 