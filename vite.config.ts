/// <reference types="vitest" />
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'
import dns from 'dns'

// Configure DNS resolution for localhost
dns.setDefaultResultOrder('verbatim')

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react()
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  define: {
    global: 'globalThis',
  },
  css: {
    postcss: './postcss.config.cjs'
  },
  build: {
    rollupOptions: {
      external: ['react', 'react-dom'], // Treat React as external
      output: {
        globals: {
          'react': 'React',
          'react-dom': 'ReactDOM'
        },
        // Disable manual chunking to behave more like dev
        manualChunks: undefined,
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name!.split('.');
          const ext = info[info.length - 1];
          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
            return `images/[name]-[hash].[ext]`;
          }
          if (/woff2?|eot|ttf|otf/i.test(ext)) {
            return `fonts/[name]-[hash].[ext]`;
          }
          return `assets/[name]-[hash].[ext]`;
        }
      }
    },
    target: 'esnext',
    minify: false, // Disable minification to preserve useLayoutEffect
    terserOptions: {
      compress: {
        drop_console: false, // Keep console logs like dev
        drop_debugger: false, // Keep debugger statements like dev
        pure_funcs: [],
        dead_code: false,
        unused: false,
        reduce_vars: false,
        collapse_vars: false
      },
      mangle: false // Disable mangling completely
    },
    chunkSizeWarningLimit: 5000, // Increase limit since we're not optimizing
    sourcemap: true, // Enable sourcemaps like dev
    cssCodeSplit: false, // Don't split CSS like dev
    assetsInlineLimit: 0, // Don't inline assets like dev
    modulePreload: {
      polyfill: false // Disable module preload polyfill
    }
  },
  optimizeDeps: {
    exclude: ['react', 'react-dom'], // Don't pre-bundle React
    include: [
      'react-router-dom',
      'lucide-react',
      'clsx',
      'tailwind-merge',
      'axios',
      'firebase/app',
      'firebase/auth',
      'zustand'
    ],
    force: true
  },
  server: {
    host: 'localhost',
    port: 5173,
    strictPort: false,
    hmr: {
      overlay: true
    },
    watch: {
      usePolling: false,
      interval: 100
    },
    proxy: {
      '/auth': {
        target: 'http://localhost:8081',
        changeOrigin: true,
        secure: false,
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('proxy error', err);
          });
          proxy.on('proxyReq', (_proxyReq, req, _res) => {
            console.log('Sending Request to the Target:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
          });
        },
      },
      '/clients': {
        target: 'http://localhost:8081',
        changeOrigin: true,
        secure: false,
        bypass: (req) => {
          // Don't proxy Xero organization selector - let frontend handle it
          if (req.url?.includes('/xero/select-organization')) {
            return req.url;
          }
          // Don't proxy other frontend-only routes
          if (req.url?.includes('/xero/configure')) {
            return req.url;
          }
          if (req.url?.includes('/entities') && !req.url?.includes('/api/')) {
            return req.url;
          }
        },
      },
      '/entities': {
        target: 'http://localhost:8081',
        changeOrigin: true,
        secure: false,
      },
      '/xero': {
        target: 'http://localhost:8081',
        changeOrigin: true,
        secure: false,
      }
    }
  },
  preview: {
    port: 5173,
    headers: {
      'Cache-Control': 'public, max-age=31536000, immutable'
    }
  },
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    css: true,
  }
})
