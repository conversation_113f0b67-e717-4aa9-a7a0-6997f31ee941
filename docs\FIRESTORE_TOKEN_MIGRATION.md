# Migration from Secret Manager to Firestore Token Storage

## Overview

We've migrated from Google Cloud Secret Manager to Firestore for OAuth token storage to significantly reduce costs. Secret Manager charges per access and can become expensive for frequently accessed tokens like OAuth refresh tokens.

## Cost Comparison

- **Secret Manager**: ~$0.03 per 10,000 accesses + storage costs
- **Firestore**: ~$0.18 per 100,000 reads (much cheaper for frequent access)
- **Estimated Savings**: 80-90% reduction in token storage costs

## Changes Made

### 1. New Firestore Token Storage Service
- Created `FirestoreTokenStorage` class in `drcr_shared_logic/clients/firestore_token_storage.py`
- Implements encryption for sensitive token data using Ferne<PERSON> (cryptography library)
- Stores tokens in Firestore collection: `oauth_tokens/{app_tenant_id}/platforms/{platform_org_id}`

### 2. Updated XeroApiClient
- Modified to use `FirestoreTokenStorage` instead of Secret Manager
- Updated methods:
  - `_load_tokens_from_secret()` → now loads from Firestore
  - `_save_tokens_to_secret()` → now saves to Firestore
  - `save_raw_tokens_to_secret_manager()` → renamed to `save_raw_tokens_to_firestore()`
  - `revoke_tokens()` → now deletes from Firestore

### 3. Environment Variables

Add to your `.env` file:
```env
# Token encryption key for Firestore storage (generate once and store securely)
TOKEN_ENCRYPTION_KEY="your-fernet-encryption-key-here"
```

To generate a new encryption key:
```python
from cryptography.fernet import Fernet
key = Fernet.generate_key().decode()
print(f"TOKEN_ENCRYPTION_KEY={key}")
```

## Migration Steps

### 1. Install Dependencies
```bash
pip install cryptography>=3.4.8
```

### 2. Generate Encryption Key
```python
from cryptography.fernet import Fernet
key = Fernet.generate_key().decode()
# Store this key securely in your environment variables
```

### 3. Update Environment Variables
Add `TOKEN_ENCRYPTION_KEY` to your environment configuration.

### 4. Deploy Updated Code
The new code will automatically start using Firestore for new token storage.

### 5. Optional: Migrate Existing Tokens
If you have existing tokens in Secret Manager, you can create a migration script:

```python
# Example migration script (customize as needed)
import asyncio
from drcr_shared_logic.clients.xero_client import XeroApiClient
from drcr_shared_logic.clients.firestore_token_storage import FirestoreTokenStorage

async def migrate_tokens():
    # This would need to be customized based on your existing secret naming
    # and tenant structure
    pass
```

## Security Considerations

1. **Encryption**: All tokens are encrypted using Fernet before storage in Firestore
2. **Key Management**: Store the `TOKEN_ENCRYPTION_KEY` securely (consider using Secret Manager for this single key)
3. **Access Control**: Ensure proper Firestore security rules are in place
4. **Backup**: Consider backing up the encryption key securely

## Firestore Security Rules

Add these rules to your Firestore to secure token storage:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // OAuth tokens - only accessible by authenticated users for their own tenant
    match /oauth_tokens/{tenantId}/platforms/{platformId} {
      allow read, write: if request.auth != null && 
                           request.auth.token.tenant_id == tenantId;
    }
  }
}
```

## Monitoring

Monitor the migration by checking:
1. Firestore usage in Google Cloud Console
2. Application logs for successful token operations
3. Cost reduction in billing

## Rollback Plan

If needed, you can rollback by:
1. Reverting the code changes
2. Ensuring Secret Manager tokens are still available
3. Updating environment variables

The old Secret Manager tokens will remain until manually deleted. 