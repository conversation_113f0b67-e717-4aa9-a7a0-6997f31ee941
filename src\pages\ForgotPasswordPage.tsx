import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { toast } from 'sonner';

// Add global styles to prevent scrolling
const GlobalStyles = () => {
  useEffect(() => {
    // Save original styles
    const originalStyle = window.getComputedStyle(document.body).overflow;

    // Apply no-scroll style
    document.body.style.overflow = 'hidden';
    document.documentElement.style.overflow = 'hidden';

    // Cleanup function to restore original styles
    return () => {
      document.body.style.overflow = originalStyle;
      document.documentElement.style.overflow = originalStyle;
    };
  }, []);

  return null;
};

// Import Shadcn UI components
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '../components/ui/card';
import { Alert, AlertDescription, AlertTitle } from "../components/ui/alert";

// Import Lucide icons
import {
  Loader2,
  Mail,
  AlertCircle,
  CheckCircle,
  ArrowLeft,
} from 'lucide-react';

// Import the DRCR logo
import drcrLogo from '../assets/logo.png';

export default function ForgotPasswordPage() {
    const [email, setEmail] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [emailError, setEmailError] = useState<string | null>(null);
    const [success, setSuccess] = useState(false);

    const validateEmail = (emailToValidate: string) => {
        if (!emailToValidate) {
            setEmailError("Email is required.");
            return false;
        }
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(emailToValidate)) {
            setEmailError("Invalid email format.");
            return false;
        }
        setEmailError(null);
        return true;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setError(null);

        const isEmailValid = validateEmail(email);
        if (!isEmailValid) {
            return;
        }

        setIsLoading(true);
        try {
            const response = await fetch('/auth/forgot-password', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email }),
            });

            const data = await response.json();

            if (response.ok) {
                setSuccess(true);
                toast.success('Password reset email sent!');
            } else {
                setError(data.detail || 'Failed to send reset email');
                toast.error('Failed to send reset email');
            }
        } catch (err) {
            setError('An error occurred. Please try again.');
            toast.error('An error occurred. Please try again.');
        } finally {
            setIsLoading(false);
        }
    };

    if (success) {
        return (
            <>
                <GlobalStyles />
                <div className="h-screen flex flex-col items-center justify-center bg-background p-0 m-0 overflow-hidden font-sans">
                    <Card className="w-full max-w-lg shadow-sm bg-card rounded-xl border">
                        <CardHeader className="text-center pt-4 pb-2">
                            <div className="w-32 h-auto mx-auto mb-1">
                                <img
                                    src={drcrLogo}
                                    alt="DRCR Logo"
                                    className="w-full h-auto"
                                />
                            </div>
                        </CardHeader>
                        <CardContent className="px-10 pb-3">
                            <div className="flex flex-col items-center space-y-4">
                                <CheckCircle className="h-12 w-12 text-green-500" />
                                <div className="text-center">
                                    <h2 className="text-xl font-semibold text-foreground mb-2">Check Your Email</h2>
                                    <p className="text-base text-muted-foreground mb-4">
                                        If an account with this email exists, you will receive a password reset link shortly.
                                    </p>
                                    <p className="text-sm text-muted-foreground mb-4">
                                        Didn't receive the email? Check your spam folder or try again.
                                    </p>
                                    <div className="space-y-2">
                                        <Button 
                                            onClick={() => setSuccess(false)}
                                            variant="outline" 
                                            className="w-full"
                                        >
                                            Send Another Email
                                        </Button>
                                        <Link to="/login">
                                            <Button className="w-full">
                                                <ArrowLeft className="h-4 w-4 mr-2" />
                                                Back to Login
                                            </Button>
                                        </Link>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                        <CardFooter className="flex flex-col items-center text-xs text-muted-foreground pt-4 pb-6">
                            <p>&copy; {new Date().getFullYear()} DRCR Labs. All rights reserved.</p>
                        </CardFooter>
                    </Card>
                </div>
            </>
        );
    }

    return (
        <>
            <GlobalStyles />
            <div className="h-screen flex flex-col items-center justify-center bg-background p-0 m-0 overflow-hidden font-sans">
                <Card className="w-full max-w-lg shadow-sm bg-card rounded-xl border">
                    <CardHeader className="text-center pt-4 pb-2">
                        <div className="w-32 h-auto mx-auto mb-1">
                            <img
                                src={drcrLogo}
                                alt="DRCR Logo"
                                className="w-full h-auto"
                            />
                        </div>
                        <CardTitle className="text-xl font-semibold text-foreground">Forgot Your Password?</CardTitle>
                        <CardDescription className="text-sm text-muted-foreground">
                            Enter your email address and we'll send you a link to reset your password
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="px-10 pb-3">
                        <form onSubmit={handleSubmit} className="flex flex-col space-y-4">
                            <div className={`h-${error ? 'auto' : '0'} mb-${error ? '4' : '0'} transition-all duration-200`}>
                                {error && (
                                    <Alert variant="destructive" className="p-3 text-sm">
                                        <AlertCircle className="h-4 w-4" />
                                        <AlertTitle className="text-sm font-medium">Error</AlertTitle>
                                        <AlertDescription className="text-xs">{error}</AlertDescription>
                                    </Alert>
                                )}
                            </div>
                            
                            <div className="space-y-2">
                                <Label htmlFor="email" className="text-base font-medium text-foreground block mb-1">
                                    Email Address
                                </Label>
                                <div className="relative">
                                    <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                    <Input
                                        id="email"
                                        type="email"
                                        placeholder="<EMAIL>"
                                        value={email}
                                        onChange={(e) => { 
                                            setEmail(e.target.value); 
                                            if (emailError) validateEmail(e.target.value); 
                                        }}
                                        onBlur={() => validateEmail(email)}
                                        className={`w-full pl-10 h-10 text-base ${emailError ? 'border-destructive focus:ring-destructive' : 'border-border focus:ring-primary'}`}
                                        autoComplete="email"
                                    />
                                </div>
                                <div className="h-5 mt-1">
                                    {emailError && <p className="text-xs text-destructive m-0">{emailError}</p>}
                                </div>
                            </div>

                            <Button
                                type="submit"
                                className="w-full h-10 text-base font-medium flex items-center justify-center gap-2 rounded-md disabled:opacity-70 disabled:cursor-not-allowed transition-colors"
                                disabled={isLoading}
                            >
                                {isLoading ? (
                                    <Loader2 className="h-5 w-5 animate-spin" />
                                ) : (
                                    <Mail className="h-5 w-5" />
                                )}
                                {isLoading ? 'Sending Reset Link...' : 'Send Reset Link'}
                            </Button>
                            
                            {/* Back to Login Link */}
                            <div className="text-center pt-2">
                                <Link 
                                    to="/login" 
                                    className="text-sm text-muted-foreground hover:text-primary transition-colors underline"
                                >
                                    <ArrowLeft className="h-3 w-3 inline mr-1" />
                                    Back to Login
                                </Link>
                            </div>
                        </form>
                    </CardContent>
                    <CardFooter className="flex flex-col items-center text-xs text-muted-foreground pt-4 pb-6">
                        <p>&copy; {new Date().getFullYear()} DRCR Labs. All rights reserved.</p>
                    </CardFooter>
                </Card>
            </div>
        </>
    );
} 