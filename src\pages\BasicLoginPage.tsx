import React, { useState } from 'react';
import { Mail, Lock, LogIn } from 'lucide-react';

// Login page with inline styles instead of Tailwind
export default function BasicLoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      console.log('Login attempt with:', email, password);
      alert(`Login successful with: ${email}`);
      setIsLoading(false);
    }, 1500);
  };

  // Inline styles
  const styles = {
    container: {
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '1rem',
      background: 'linear-gradient(to bottom right, #3b82f6, #8b5cf6)'
    },
    card: {
      backgroundColor: 'white',
      padding: '2rem',
      borderRadius: '0.75rem',
      boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
      maxWidth: '28rem',
      width: '100%'
    },
    logoContainer: {
      textAlign: 'center' as const,
      marginBottom: '2rem'
    },
    logoCircle: {
      width: '5rem',
      height: '5rem',
      backgroundColor: '#dbeafe',
      borderRadius: '9999px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      margin: '0 auto',
      marginBottom: '1rem'
    },
    logoText: {
      color: '#2563eb',
      fontSize: '1.5rem',
      fontWeight: 'bold'
    },
    heading: {
      fontSize: '1.5rem',
      fontWeight: 'bold',
      color: '#1f2937'
    },
    subheading: {
      color: '#6b7280',
      marginTop: '0.25rem'
    },
    form: {
      marginTop: '1.5rem'
    },
    formGroup: {
      marginBottom: '1.5rem'
    },
    label: {
      display: 'block',
      color: '#374151',
      fontSize: '0.875rem',
      fontWeight: '500',
      marginBottom: '0.5rem'
    },
    inputWrapper: {
      position: 'relative' as const
    },
    iconWrapper: {
      position: 'absolute' as const,
      left: '0.75rem',
      top: '50%',
      transform: 'translateY(-50%)',
      pointerEvents: 'none' as const
    },
    icon: {
      width: '1.25rem',
      height: '1.25rem',
      color: '#9ca3af'
    },
    input: {
      width: '100%',
      paddingLeft: '2.5rem',
      paddingRight: '0.75rem',
      paddingTop: '0.5rem',
      paddingBottom: '0.5rem',
      borderWidth: '1px',
      borderColor: '#d1d5db',
      borderRadius: '0.375rem',
      outline: 'none'
    },
    button: {
      width: '100%',
      backgroundColor: '#2563eb',
      color: 'white',
      fontWeight: '500',
      padding: '0.5rem 1rem',
      borderRadius: '0.375rem',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      cursor: 'pointer',
      border: 'none'
    },
    buttonIcon: {
      marginRight: '0.5rem'
    },
    footer: {
      marginTop: '2rem',
      textAlign: 'center' as const,
      fontSize: '0.875rem',
      color: '#6b7280'
    }
  };

  return (
    <div style={styles.container}>
      <div style={styles.card}>
        {/* Logo/Header */}
        <div style={styles.logoContainer}>
          <div style={styles.logoCircle}>
            <span style={styles.logoText}>DRCR</span>
          </div>
          <h1 style={styles.heading}>Welcome Back</h1>
          <p style={styles.subheading}>Sign in to your account</p>
        </div>

        <form onSubmit={handleSubmit} style={styles.form}>
          {/* Email Field */}
          <div style={styles.formGroup}>
            <label style={styles.label}>
              Email Address
            </label>
            <div style={styles.inputWrapper}>
              <div style={styles.iconWrapper}>
                <Mail style={styles.icon} />
              </div>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                style={styles.input}
                placeholder="<EMAIL>"
                required
              />
            </div>
          </div>

          {/* Password Field */}
          <div style={styles.formGroup}>
            <label style={styles.label}>
              Password
            </label>
            <div style={styles.inputWrapper}>
              <div style={styles.iconWrapper}>
                <Lock style={styles.icon} />
              </div>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                style={styles.input}
                placeholder="••••••••"
                required
              />
            </div>
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isLoading}
            style={styles.button}
          >
            {isLoading ? (
              <>
                <svg style={{...styles.buttonIcon, animation: 'spin 1s linear infinite'}} width="16" height="16" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle style={{opacity: 0.25}} cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path style={{opacity: 0.75}} fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Signing In...
              </>
            ) : (
              <>
                <LogIn style={styles.buttonIcon} />
                Sign In
              </>
            )}
          </button>
        </form>

        {/* Footer */}
        <div style={styles.footer}>
          <p>&copy; {new Date().getFullYear()} DRCR Labs. All rights reserved.</p>
        </div>
      </div>
    </div>
  );
}
