#!/bin/sh
# <PERSON>ript for preparing frontend deployment environment

echo "Current directory: $(pwd)"
echo "Listing root directory:"
ls -la

# Проверяем, находимся ли мы в директории app
if [ -d "../drcr_front" ]; then
  echo "Found drcr_front in parent directory, moving up..."
  cd ..
  echo "New current directory: $(pwd)"
fi

# Проверяем наличие директории drcr_front
if [ ! -d "drcr_front" ]; then
  echo "Error: drcr_front directory not found in $(pwd)!"
  echo "Listing all directories:"
  find . -type d -maxdepth 2
  exit 1
fi

cd drcr_front
echo "Current directory after cd: $(pwd)"

# Проверяем наличие директории dist
if [ ! -d "dist" ]; then
  echo "Error: dist directory not found! Make sure the build step completed successfully."
  exit 1
fi

# Проверяем наличие токена Firebase
if [ -z "$FIREBASE_TOKEN" ]; then
  echo "Error: FIREBASE_TOKEN is not set. Please add it in CI/CD Variables."
  exit 1
fi

echo "✅ Frontend deployment environment prepared successfully!"
