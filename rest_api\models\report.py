from pydantic import BaseModel
from typing import List, Optional, Dict, Any, Union
from datetime import date
from enum import Enum

class ReportType(str, Enum):
    """Enum for report types."""
    PROFIT_LOSS = "profit_loss"
    BALANCE_SHEET = "balance_sheet"
    CASH_FLOW = "cash_flow"
    TAX_SUMMARY = "tax_summary"
    AGED_RECEIVABLES = "aged_receivables"
    AGED_PAYABLES = "aged_payables"

class ReportPeriod(str, Enum):
    """Enum for report periods."""
    MONTH = "month"
    QUARTER = "quarter"
    YEAR = "year"
    CUSTOM = "custom"

class ReportRequest(BaseModel):
    """Request model for generating a report."""
    report_type: ReportType
    period: ReportPeriod
    from_date: date
    to_date: date
    comparison_period: Optional[bool] = False
    include_draft: Optional[bool] = False
    currency: Optional[str] = "USD"
    additional_params: Optional[Dict[str, Any]] = None

class ReportRow(BaseModel):
    """Model for a row in a report."""
    label: str
    values: List[float]
    is_total: Optional[bool] = False
    is_subtotal: Optional[bool] = False
    indent_level: Optional[int] = 0
    account_id: Optional[str] = None

class ReportSection(BaseModel):
    """Model for a section in a report."""
    title: str
    rows: List[ReportRow]
    class Config:
        from_attributes = True

class ReportColumn(BaseModel):
    """Model for a column in a report."""
    label: str
    period_start: date
    period_end: date

class ReportResponse(BaseModel):
    """Response model for a generated report."""
    report_type: ReportType
    title: str
    subtitle: str
    from_date: date
    to_date: date
    currency: str
    columns: List[ReportColumn]
    sections: List[ReportSection]
    totals: Optional[ReportRow] = None
    metadata: Optional[Dict[str, Any]] = None
    class Config:
        from_attributes = True
