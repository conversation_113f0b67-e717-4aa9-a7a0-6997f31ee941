"""
Xero Service - Business logic for Xero integration operations
Extracted from rest_api/routes/xero.py for better maintainability
"""
from typing import Dict, Any, List, Optional, Tuple
from google.cloud.firestore import SERVER_TIMESTAMP
import uuid
import json
import logging
import os
import sys
import asyncio
import time
from datetime import datetime, timedelta, timezone
from google.cloud import pubsub_v1

from drcr_shared_logic.clients.xero_client import XeroApiClient
from ..utils.audit import create_audit_log_entry

logger = logging.getLogger(__name__)


class XeroService:
    """Service for handling Xero integration business logic"""

    def __init__(self, db):
        self.db = db
        self.pubsub_client = pubsub_v1.PublisherClient()
        self.project_id = os.getenv("GCP_PROJECT_ID")
        self.sync_topic = os.getenv("PUBSUB_TOPIC_XERO_SYNC", "xero-sync-topic")

    async def initiate_connection(self, client_id: str, current_user) -> Dict[str, str]:
        """
        Initiate Xero OAuth connection for a client
        Returns: {'authorization_url': str}
        """
        # Check if client exists
        client_ref = self.db.collection("CLIENTS").document(client_id)
        client_doc = await client_ref.get()

        if not client_doc.exists:
            raise ValueError("Client not found")

        # Create a temporary XeroApiClient with placeholder tenant ID
        xero_client = await XeroApiClient.create(
            platform_org_id="OAUTH_CALLBACK_PLACEHOLDER",
            tenant_id=client_id
        )

        # Generate state parameter to include the client_id
        state = {
            "client_id": client_id,
            "user_id": current_user.uid
        }
        state_json = json.dumps(state)

        # Get authorization URL
        auth_url = await xero_client.get_authorization_url(state=state_json)

        return {"authorization_url": auth_url}

    async def handle_oauth_callback(
        self,
        code: str,
        state: str
    ) -> Tuple[str, List[Dict[str, Any]]]:
        """
        Handle Xero OAuth callback
        Returns: (client_id, entities_created)
        """
        try:
            # Parse state to get client_id
            state_data = json.loads(state)
            client_id = state_data.get("client_id")
            user_id = state_data.get("user_id")

            if not client_id or not user_id:
                raise ValueError("Invalid state parameter")

            # Check if client exists
            client_ref = self.db.collection("CLIENTS").document(client_id)
            client_doc = await client_ref.get()

            if not client_doc.exists:
                raise ValueError("Client not found")

            # Create a temporary XeroApiClient to exchange the code
            temp_client = await XeroApiClient.create(
                platform_org_id="OAUTH_CALLBACK_PLACEHOLDER",
                tenant_id=client_id
            )

            # Exchange authorization code for tokens
            token_data = await temp_client.exchange_auth_code_for_tokens(
                auth_code=code,
                redirect_uri=temp_client.redirect_uri
            )

            # Get Xero connections to get the tenant IDs (Xero orgs)
            connections = await temp_client.get_xero_connections(token_data["access_token"])

            # Process each connection - but only create entities for organizations that don't already exist
            # This prevents duplicate token storage for the same organizations
            entities_created = []
            for connection in connections:
                xero_tenant_id = connection.get("tenantId")
                if not xero_tenant_id:
                    continue

                # Check if this entity already exists
                entity_ref = self.db.collection("ENTITIES").document(xero_tenant_id)
                entity_doc = await entity_ref.get()

                if entity_doc.exists:
                    existing_client_id = entity_doc.to_dict().get("client_id")
                    if existing_client_id == client_id:
                        # Entity already exists for this client - just update the tokens
                        # OPTIMIZATION: Create client without loading existing tokens since we're overwriting them
                        real_client = XeroApiClient(
                            platform_org_id=xero_tenant_id,
                            tenant_id=client_id
                        )
                        await real_client.save_raw_tokens_to_firestore(token_data)

                        # Update the entity's updated_at timestamp
                        await entity_ref.update({
                            "updated_at": SERVER_TIMESTAMP,
                            "connection_details.status": "active"
                        })
                    else:
                        # Entity belongs to a different client - reject the connection
                        xero_tenant_name = connection.get("tenantName", "Unknown Organization")
                        raise ValueError(
                            f"Xero organization '{xero_tenant_name}' is already connected to another client. "
                            f"A Xero organization can only be connected to one client at a time."
                        )
                else:
                    # Entity doesn't exist - create new connection
                    entity_info = await self._process_xero_connection(
                        connection, token_data, client_id, user_id
                    )
                    if entity_info:
                        entities_created.append(entity_info)

            return client_id, entities_created

        except json.JSONDecodeError:
            raise ValueError("Invalid state parameter format")
        except Exception as e:
            logger.error(f"OAuth callback error: {str(e)}")
            raise

    async def handle_smart_oauth_callback(
        self,
        code: str,
        state: str
    ) -> Tuple[str, List[Dict[str, Any]], bool]:
        """
        Smart OAuth callback that handles both new connections and reconnections
        Returns: (client_id, entities_processed, needs_organization_selection)
        """
        try:
            # Parse state to get client_id
            state_data = json.loads(state)
            client_id = state_data.get("client_id")
            user_id = state_data.get("user_id")

            if not client_id or not user_id:
                raise ValueError("Invalid state parameter")

            # Check if client exists
            client_ref = self.db.collection("CLIENTS").document(client_id)
            client_doc = await client_ref.get()

            if not client_doc.exists:
                raise ValueError("Client not found")

            # Create a temporary XeroApiClient to exchange the code
            temp_client = await XeroApiClient.create(
                platform_org_id="OAUTH_CALLBACK_PLACEHOLDER",
                tenant_id=client_id
            )

            # Exchange authorization code for tokens
            token_data = await temp_client.exchange_auth_code_for_tokens(
                auth_code=code,
                redirect_uri=temp_client.redirect_uri
            )

            # Get Xero connections to get the tenant IDs (Xero orgs)
            connections = await temp_client.get_xero_connections(token_data["access_token"])

            # Log all organizations returned by Xero for debugging
            logger.info(f"Xero returned {len(connections)} organizations for client '{client_id}':")
            for i, conn in enumerate(connections):
                tenant_id = conn.get("tenantId", "NO_TENANT_ID")
                tenant_name = conn.get("tenantName", "NO_NAME")
                logger.info(f"  {i+1}. '{tenant_name}' (tenant_id: {tenant_id})")

            # Categorize organizations: reconnections vs new connections
            reconnections = []
            new_connections = []

            for connection in connections:
                xero_tenant_id = connection.get("tenantId")
                if not xero_tenant_id:
                    continue

                # Check if this entity was previously connected to this client
                entity_ref = self.db.collection("ENTITIES").document(xero_tenant_id)
                entity_doc = await entity_ref.get()

                if entity_doc.exists:
                    entity_data = entity_doc.to_dict()
                    existing_client_id = entity_data.get("client_id")
                    xero_tenant_name = connection.get("tenantName", "Unknown Organization")

                    if existing_client_id == client_id:
                        # This is a reconnection - organization was previously connected to the same client
                        logger.info(
                            f"Found reconnection: Xero organization '{xero_tenant_name}' "
                            f"(tenant_id: {xero_tenant_id}) was previously connected to client '{client_id}'"
                        )
                        reconnections.append(connection)
                    elif existing_client_id:
                        # Organization exists but for different client - skip it
                        # Don't add to new_connections as it cannot be connected to this client
                        logger.warning(
                            f"BLOCKING: Xero organization '{xero_tenant_name}' (tenant_id: {xero_tenant_id}) "
                            f"is already connected to client '{existing_client_id}', cannot connect to client '{client_id}'"
                        )
                        continue
                    else:
                        # Entity exists but has no client_id (edge case)
                        logger.warning(
                            f"Found orphaned entity: Xero organization '{xero_tenant_name}' "
                            f"(tenant_id: {xero_tenant_id}) exists but has no client_id. Treating as new connection."
                        )
                        new_connections.append(connection)
                else:
                    # Completely new organization
                    xero_tenant_name = connection.get("tenantName", "Unknown Organization")
                    logger.info(
                        f"Found new organization: Xero organization '{xero_tenant_name}' "
                        f"(tenant_id: {xero_tenant_id}) is completely new"
                    )
                    new_connections.append(connection)

            # Log categorization summary
            logger.info(
                f"Organization categorization complete for client '{client_id}': "
                f"{len(reconnections)} reconnections, {len(new_connections)} new connections"
            )

            entities_processed = []

            # Process reconnections automatically (these are safe since they're for the same client)
            for connection in reconnections:
                try:
                    entity_info = await self._process_xero_connection(
                        connection, token_data, client_id, user_id
                    )
                    if entity_info:
                        entity_info["connection_type"] = "reconnection"
                        entities_processed.append(entity_info)
                except ValueError as e:
                    xero_tenant_name = connection.get("tenantName", "Unknown Organization")
                    logger.error(
                        f"Unexpected validation error during reconnection for '{xero_tenant_name}': {e}"
                    )

            # ALWAYS store temporary data after successful token exchange and connection fetching.
            # This ensures the select-organization page can always retrieve a session.
            # The 'new_connections' list here is ALREADY FILTERED to only include those available for THIS client.
            temp_tokens_ref = self.db.collection("TEMP_OAUTH_TOKENS").document(f"{client_id}_{user_id}")
            
            current_utc_time = datetime.now(timezone.utc)
            expires_at = current_utc_time + timedelta(minutes=60)

            await temp_tokens_ref.set({
                "client_id": client_id,
                "user_id": user_id,
                "token_data": token_data, # Full token data from Xero for this callback
                "new_connections": [ # List of new, selectable organizations for this client
                    {
                        "tenant_id": conn.get("tenantId"),
                        "tenant_name": conn.get("tenantName"),
                        "connection_type": conn.get("tenantType", "ORGANISATION")
                    }
                    for conn in new_connections 
                ],
                "reconnections_processed": len(reconnections), # Count of successfully reconnected orgs for this client
                "created_at": current_utc_time,
                "expires_at": expires_at
            })
            
            # The 'needs_selection' flag for the UI still depends on whether there are any *new* organizations to actually select.
            # If new_connections is empty, the select-organization page will show 0 selectable orgs but can message about reconnections.
            needs_selection_prompt = bool(new_connections)
            return client_id, entities_processed, needs_selection_prompt

        except json.JSONDecodeError:
            logger.error(f"Smart OAuth callback error: Invalid state parameter format - {state}")
            raise ValueError("Invalid state parameter format")
        except Exception as e:
            logger.error(f"Smart OAuth callback error: {str(e)}")
            raise # Re-raise other exceptions

    async def _process_xero_connection(
        self,
        connection: Dict[str, Any],
        token_data: Dict[str, Any],
        client_id: str,
        user_id: str
    ) -> Optional[Dict[str, Any]]:
        """Process a single Xero connection/tenant - optimized for speed"""
        xero_tenant_id = connection.get("tenantId")
        xero_tenant_name = connection.get("tenantName")

        if not xero_tenant_id:
            return None

        try:
            # CRITICAL VALIDATION: Check if entity exists and belongs to different client
            entity_id = xero_tenant_id
            entity_ref = self.db.collection("ENTITIES").document(entity_id)
            entity_doc = await entity_ref.get()

            if entity_doc.exists:
                existing_data = entity_doc.to_dict()
                existing_client_id = existing_data.get("client_id")

                if existing_client_id and existing_client_id != client_id:
                    # This is a critical security issue - prevent client transfer
                    entity_name = existing_data.get("entity_name", "Unknown Organization")
                    logger.error(
                        f"SECURITY ALERT: Attempt to transfer Xero organization '{entity_name}' "
                        f"(tenant_id: {xero_tenant_id}) from client '{existing_client_id}' to client '{client_id}'. "
                        f"This is not allowed."
                    )
                    raise ValueError(
                        f"Xero organization '{entity_name}' is already connected to another client. "
                        f"A Xero organization can only be connected to one client at a time. "
                        f"Please disconnect it from the other client first if you need to connect it here."
                    )
                elif existing_client_id == client_id:
                    # This is a legitimate reconnection to the same client
                    logger.info(
                        f"Reconnecting Xero organization '{xero_tenant_name}' "
                        f"(tenant_id: {xero_tenant_id}) to the same client '{client_id}'"
                    )
                else:
                    # Entity exists but has no client_id (shouldn't happen, but handle it)
                    logger.warning(
                        f"Xero organization '{xero_tenant_name}' (tenant_id: {xero_tenant_id}) "
                        f"exists but has no client_id. Proceeding with connection to client '{client_id}'"
                    )
            else:
                # New entity - this is fine
                logger.info(
                    f"Creating new Xero organization '{xero_tenant_name}' "
                    f"(tenant_id: {xero_tenant_id}) for client '{client_id}'"
                )

            # OPTIMIZATION: Create XeroApiClient without loading existing tokens
            # since we're about to save new tokens anyway
            real_client = XeroApiClient(
                platform_org_id=xero_tenant_id,
                tenant_id=client_id
            )

            # CRITICAL PATH: Only wait for essential operations
            # 1. Save tokens (essential)
            # 2. Create/update entity (essential)
            # Everything else runs in background

            # Save tokens
            await real_client.save_raw_tokens_to_firestore(token_data)

            entity_data = {
                "entity_id": entity_id,
                "client_id": client_id,
                "entity_name": xero_tenant_name or "Xero Organization",
                "type": "xero",
                "status": "active",
                "connection_details": {
                    "xero_tenant_id": xero_tenant_id,
                    "status": "active",
                    "connected_by": user_id
                },
                "updated_at": SERVER_TIMESTAMP
            }

            if entity_doc.exists:
                # Update existing entity
                await entity_ref.update(entity_data)
            else:
                # Create new entity
                entity_data["created_at"] = SERVER_TIMESTAMP
                await entity_ref.set(entity_data)

            # BACKGROUND OPERATIONS: Fire and forget - don't wait for these

            # Create background tasks for non-critical operations
            background_tasks = []

            # 1. Create default entity settings
            background_tasks.append(
                asyncio.create_task(self._create_default_entity_settings_safe(entity_id, client_id))
            )

            # 2. Record connection for backward compatibility
            background_tasks.append(
                asyncio.create_task(self._record_connection_safe(client_id, entity_id, xero_tenant_id, xero_tenant_name))
            )

            # 3. Create audit log entry
            background_tasks.append(
                asyncio.create_task(self._create_audit_log_safe(client_id, entity_id, xero_tenant_name, user_id, entity_doc.exists))
            )

            # 4. Trigger initial sync
            background_tasks.append(
                asyncio.create_task(self._trigger_initial_sync_safe(entity_id, client_id))
            )

            # Don't await background tasks - let them run independently
            # This allows the user to see the connection immediately

            return {
                "entity_id": entity_id,
                "entity_name": xero_tenant_name,
                "requires_configuration": True
            }

        except Exception as e:
            logger.error(f"Failed to process Xero connection for {xero_tenant_id}: {e}")
            raise

    async def _create_default_entity_settings_safe(self, entity_id: str, client_id: str):
        """Create default ENTITY_SETTINGS document - safe background operation"""
        try:
            await self._create_default_entity_settings(entity_id, client_id)
        except Exception as e:
            logger.warning(f"Background task failed - create entity settings for {entity_id}: {e}")

    async def _record_connection_safe(self, client_id: str, entity_id: str, xero_tenant_id: str, xero_tenant_name: str):
        """Record connection - safe background operation"""
        try:
            await self._record_connection(client_id, entity_id, xero_tenant_id, xero_tenant_name)
        except Exception as e:
            logger.warning(f"Background task failed - record connection for {entity_id}: {e}")

    async def _create_audit_log_safe(self, client_id: str, entity_id: str, xero_tenant_name: str, user_id: str, is_reconnect: bool):
        """Create audit log - safe background operation"""
        try:
            await create_audit_log_entry(
                db=self.db,
                event_category="ENTITY_MANAGEMENT",
                event_type="ENTITY_CONNECTED",
                client_id=client_id,
                entity_id=entity_id,
                status="SUCCESS",
                details={
                    "entity_type": "xero",
                    "entity_name": xero_tenant_name,
                    "connection_type": "reconnect" if is_reconnect else "new"
                },
                user_id=user_id
            )
        except Exception as e:
            logger.warning(f"Background task failed - create audit log for {entity_id}: {e}")

    async def _trigger_initial_sync_safe(self, entity_id: str, client_id: str):
        """Trigger initial sync - safe background operation"""
        try:
            await self._trigger_initial_sync(entity_id, client_id)
        except Exception as e:
            logger.warning(f"Background task failed - trigger sync for {entity_id}: {e}")

    async def _create_default_entity_settings(self, entity_id: str, client_id: str):
        """Create default ENTITY_SETTINGS document if it doesn't exist"""
        settings_ref = self.db.collection("ENTITY_SETTINGS").document(entity_id)
        settings_doc = await settings_ref.get()

        if not settings_doc.exists:
            default_settings = {
                "entity_id": entity_id,
                "client_id": client_id,
                "prepayment_asset_account_codes": [],
                "excluded_pnl_account_codes": [],
                "created_at": SERVER_TIMESTAMP,
                "updated_at": SERVER_TIMESTAMP
            }
            await settings_ref.set(default_settings)

    async def _record_connection(
        self,
        client_id: str,
        entity_id: str,
        xero_tenant_id: str,
        xero_tenant_name: str
    ):
        """Record connection in XERO_APP_TENANT_CONNECTIONS for backward compatibility"""
        connection_ref = self.db.collection("XERO_APP_TENANT_CONNECTIONS").document()
        await connection_ref.set({
            "client_id": client_id,
            "entity_id": entity_id,
            "xero_tenant_id": xero_tenant_id,
            "xero_tenant_name": xero_tenant_name,
            "created_at": SERVER_TIMESTAMP
        })

    async def get_xero_accounts(self, entity_id: str, current_user) -> List[Dict[str, Any]]:
        """Get chart of accounts from Xero"""
        # Verify entity exists and user has access
        entity_ref = self.db.collection("ENTITIES").document(entity_id)
        entity_doc = await entity_ref.get()

        if not entity_doc.exists:
            raise ValueError("Entity not found")

        entity_data = entity_doc.to_dict()
        client_id = entity_data.get("client_id")

        if not client_id:
            raise ValueError("Entity missing client_id")

        # Verify user has access to this client
        # This is a simplified check - implement proper access control
        if hasattr(current_user, 'assigned_client_ids'):
            if current_user.assigned_client_ids and client_id not in current_user.assigned_client_ids:
                raise ValueError("User does not have access to this client")

        # Get Xero connection details
        connection_details = entity_data.get("connection_details", {})
        xero_tenant_id = connection_details.get("xero_tenant_id")

        if not xero_tenant_id:
            raise ValueError("Entity missing Xero tenant ID")

        # OPTIMIZATION: Create XeroApiClient and fetch accounts
        # Use direct instantiation since tokens should already exist
        xero_client = XeroApiClient(
            platform_org_id=xero_tenant_id,
            tenant_id=client_id
        )

        # Load tokens manually only if needed
        await xero_client._load_tokens_from_secret()

        accounts = await xero_client.get_records("Accounts")
        return accounts

    async def update_entity_settings(
        self,
        entity_id: str,
        settings_data: Dict[str, Any],
        current_user
    ) -> Dict[str, Any]:
        """Update entity settings"""
        # Verify entity exists and get client_id
        entity_ref = self.db.collection("ENTITIES").document(entity_id)
        entity_doc = await entity_ref.get()

        if not entity_doc.exists:
            raise ValueError("Entity not found")

        entity_data = entity_doc.to_dict()
        client_id = entity_data.get("client_id")

        if not client_id:
            raise ValueError("Entity missing client_id")

        # Verify user has access to this client
        if hasattr(current_user, 'assigned_client_ids'):
            if current_user.assigned_client_ids and client_id not in current_user.assigned_client_ids:
                raise ValueError("User does not have access to this client")

        # Update entity settings
        settings_ref = self.db.collection("ENTITY_SETTINGS").document(entity_id)

        # Get current settings
        settings_doc = await settings_ref.get()
        current_settings = settings_doc.to_dict() if settings_doc.exists else {}

        # Update with new data
        updated_settings = {
            **current_settings,
            **settings_data,
            "entity_id": entity_id,
            "client_id": client_id,
            "updated_at": SERVER_TIMESTAMP
        }

        # If creating new, add created_at
        if not settings_doc.exists:
            updated_settings["created_at"] = SERVER_TIMESTAMP

        await settings_ref.set(updated_settings)

        # Create audit log entry
        await create_audit_log_entry(
            db=self.db,
            event_category="ENTITY_MANAGEMENT",
            event_type="ENTITY_SETTINGS_UPDATED",
            client_id=client_id,
            entity_id=entity_id,
            status="SUCCESS",
            details={
                "settings_updated": list(settings_data.keys()),
                "entity_type": "xero"
            },
            user_id=current_user.uid
        )

        return updated_settings

    async def revoke_connection(self, entity_id: str, current_user) -> Dict[str, str]:
        """Revoke Xero connection for an entity"""
        # Verify entity exists and get details
        entity_ref = self.db.collection("ENTITIES").document(entity_id)
        entity_doc = await entity_ref.get()

        if not entity_doc.exists:
            raise ValueError("Entity not found")

        entity_data = entity_doc.to_dict()
        client_id = entity_data.get("client_id")

        if not client_id:
            raise ValueError("Entity missing client_id")

        # Verify user has access to this client
        if hasattr(current_user, 'assigned_client_ids'):
            if current_user.assigned_client_ids and client_id not in current_user.assigned_client_ids:
                raise ValueError("User does not have access to this client")

        connection_details = entity_data.get("connection_details", {})
        xero_tenant_id = connection_details.get("xero_tenant_id")

        if not xero_tenant_id:
            raise ValueError("Entity missing Xero tenant ID")

        try:
            # Create XeroApiClient to revoke connection
            xero_client = await XeroApiClient.create(
                platform_org_id=xero_tenant_id,
                tenant_id=client_id
            )

            # Revoke the connection with Xero
            await xero_client.revoke_connection()

            # Update entity status to disconnected
            await entity_ref.update({
                "status": "disconnected",
                "connection_details.status": "disconnected",
                "updated_at": SERVER_TIMESTAMP
            })

            # Create audit log entry
            await create_audit_log_entry(
                db=self.db,
                event_category="ENTITY_MANAGEMENT",
                event_type="ENTITY_DISCONNECTED",
                client_id=client_id,
                entity_id=entity_id,
                status="SUCCESS",
                details={
                    "entity_type": "xero",
                    "disconnection_reason": "user_requested"
                },
                user_id=current_user.uid
            )

            return {"message": "Xero connection revoked successfully"}

        except Exception as e:
            logger.error(f"Failed to revoke Xero connection for entity {entity_id}: {str(e)}")

            # Create audit log entry for failure
            await create_audit_log_entry(
                db=self.db,
                event_category="ENTITY_MANAGEMENT",
                event_type="ENTITY_DISCONNECTED",
                client_id=client_id,
                entity_id=entity_id,
                status="ERROR",
                details={
                    "entity_type": "xero",
                    "error": str(e),
                    "disconnection_reason": "user_requested"
                },
                user_id=current_user.uid
            )

            raise RuntimeError(f"Failed to revoke connection: {str(e)}")

    async def get_entity_by_id(self, entity_id: str) -> Optional[Dict[str, Any]]:
        """Get entity by ID"""
        entity_ref = self.db.collection("ENTITIES").document(entity_id)
        entity_doc = await entity_ref.get()

        if not entity_doc.exists:
            return None

        return entity_doc.to_dict()

    async def get_entity_settings(self, entity_id: str) -> Optional[Dict[str, Any]]:
        """Get entity settings by entity ID"""
        settings_ref = self.db.collection("ENTITY_SETTINGS").document(entity_id)
        settings_doc = await settings_ref.get()

        if settings_doc.exists:
            return settings_doc.to_dict()
        return None

    async def get_client_xero_configuration(
        self,
        client_id: str,
        current_user,
        entities: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get Xero configuration data for a client
        Returns client entities and their Xero connection status
        """
        # Check if client exists and user has access
        client_ref = self.db.collection("CLIENTS").document(client_id)
        client_doc = await client_ref.get()

        if not client_doc.exists:
            raise ValueError("Client not found")

        # Get all entities for this client
        entities_query = self.db.collection("ENTITIES").where("client_id", "==", client_id)
        entities_docs = await entities_query.get()

        entities_data = []
        for entity_doc in entities_docs:
            entity_data = entity_doc.to_dict()
            entity_id = entity_data.get("entity_id")

            # Get entity settings
            settings = await self.get_entity_settings(entity_id)

            # Check connection status if it's a Xero entity
            connection_status = "disconnected"
            if entity_data.get("type") == "xero":
                try:
                    xero_tenant_id = entity_data.get("connection_details", {}).get("xero_tenant_id")
                    if xero_tenant_id:
                        xero_client = await XeroApiClient.create(
                            platform_org_id=xero_tenant_id,
                            tenant_id=client_id
                        )
                        status_info = await xero_client.check_connection_status()
                        connection_status = status_info.get("status", "disconnected")
                except Exception as e:
                    logger.warning(f"Failed to check connection status for entity {entity_id}: {e}")
                    connection_status = "error"

            entities_data.append({
                "entity_id": entity_id,
                "entity_name": entity_data.get("entity_name"),
                "type": entity_data.get("type"),
                "status": entity_data.get("status"),
                "connection_status": connection_status,
                "settings": settings,
                "requires_configuration": not settings or not settings.get("prepaymentAssetAccountCodes")
            })

        # Filter by specific entities if provided
        if entities:
            entity_ids = [e.strip() for e in entities.split(",")]
            entities_data = [e for e in entities_data if e["entity_id"] in entity_ids]

        client_data = client_doc.to_dict()

        return {
            "client": {
                "client_id": client_id,
                "name": client_data.get("name"),
                "status": client_data.get("status")
            },
            "entities": entities_data,
            "has_xero_entities": any(e["type"] == "xero" for e in entities_data),
            "configuration_complete": all(not e["requires_configuration"] for e in entities_data if e["type"] == "xero")
        }

    async def _trigger_initial_sync(self, entity_id: str, client_id: str):
        """Trigger immediate sync for a newly connected entity - optimized for speed"""
        try:
            # Create sync message for initial data sync
            sync_message = {
                "platformOrgId": entity_id,  # Xero tenant ID
                "tenantId": client_id,       # Our client ID
                "syncJobId": str(uuid.uuid4()),
                "endpoints": ["Contacts", "Accounts"],  # Sync contacts and accounts immediately
                "forceFullSyncEndpoints": ["Contacts", "Accounts"],  # Force full sync for initial load
                "targetDate": datetime.now().strftime("%Y-%m-%d"),
                "reason": "initial_connection"
            }

            # OPTIMIZATION: Truly async Pub/Sub publishing
            topic_path = self.pubsub_client.topic_path(self.project_id, self.sync_topic)
            message_data = json.dumps(sync_message).encode("utf-8")

            # Use asyncio to run the blocking publish in a thread pool
            loop = asyncio.get_event_loop()

            async def publish_async():
                try:
                    future = self.pubsub_client.publish(topic_path, message_data)
                    # Run the blocking future.result() in a thread pool
                    message_id = await loop.run_in_executor(None, future.result)
                    logger.info(f"Published sync message {message_id} for entity {entity_id}")
                    return message_id
                except Exception as e:
                    logger.warning(f"Failed to publish sync message for {entity_id}: {e}")
                    return None

            # Start publishing in background - don't wait
            asyncio.create_task(publish_async())

            # Log immediately without waiting for result
            logger.info(f"Triggered initial sync for entity {entity_id}, sync job: {sync_message['syncJobId']}")

        except Exception as e:
            logger.error(f"Failed to trigger initial sync for entity {entity_id}: {str(e)}")
            # Create failure audit log in background
            asyncio.create_task(self._create_sync_failure_audit_log(
                client_id, entity_id, str(e)
            ))

    async def _create_sync_audit_log(self, client_id: str, entity_id: str, sync_message: dict, future):
        """Create audit log for sync trigger - background operation"""
        try:
            # Wait for publish result in background
            message_id = await asyncio.wrap_future(future)

            await create_audit_log_entry(
                db=self.db,
                event_category="SYNC",
                event_type="INITIAL_SYNC_TRIGGERED",
                client_id=client_id,
                entity_id=entity_id,
                status="SUCCESS",
                details={
                    "sync_job_id": sync_message["syncJobId"],
                    "endpoints": sync_message["endpoints"],
                    "message_id": message_id,
                    "reason": "initial_connection"
                }
            )
        except Exception as e:
            logger.warning(f"Failed to create sync audit log for {entity_id}: {e}")

    async def _create_sync_failure_audit_log(self, client_id: str, entity_id: str, error: str):
        """Create failure audit log for sync trigger - background operation"""
        try:
            await create_audit_log_entry(
                db=self.db,
                event_category="SYNC",
                event_type="INITIAL_SYNC_TRIGGER_FAILED",
                client_id=client_id,
                entity_id=entity_id,
                status="FAILURE",
                details={
                    "error": error,
                    "reason": "initial_connection"
                }
            )
        except Exception as e:
            logger.warning(f"Failed to create sync failure audit log for {entity_id}: {e}")

    async def get_available_xero_organizations(
        self,
        code: str,
        state: str
    ) -> Tuple[str, List[Dict[str, Any]]]:
        """
        Get available Xero organizations for user selection
        Returns: (client_id, available_organizations)
        """
        try:
            # Parse state to get client_id
            state_data = json.loads(state)
            client_id = state_data.get("client_id")
            user_id = state_data.get("user_id")

            if not client_id or not user_id:
                raise ValueError("Invalid state parameter")

            # Check if client exists
            client_ref = self.db.collection("CLIENTS").document(client_id)
            client_doc = await client_ref.get()

            if not client_doc.exists:
                raise ValueError("Client not found")

            # Create a temporary XeroApiClient to exchange the code
            temp_client = await XeroApiClient.create(
                platform_org_id="OAUTH_CALLBACK_PLACEHOLDER",
                tenant_id=client_id
            )

            # Exchange authorization code for tokens
            token_data = await temp_client.exchange_auth_code_for_tokens(
                auth_code=code,
                redirect_uri=temp_client.redirect_uri
            )

            # Get Xero connections to show available organizations
            connections = await temp_client.get_xero_connections(token_data["access_token"])

            # Format organizations for selection
            available_organizations = []
            for connection in connections:
                xero_tenant_id = connection.get("tenantId")
                xero_tenant_name = connection.get("tenantName")

                if not xero_tenant_id:
                    continue

                # Check if this organization is already connected
                entity_ref = self.db.collection("ENTITIES").document(xero_tenant_id)
                entity_doc = await entity_ref.get()

                if entity_doc.exists:
                    existing_client_id = entity_doc.to_dict().get("client_id")
                    if existing_client_id == client_id:
                        # Already connected to this client
                        is_already_connected = entity_doc.to_dict().get("status") == "active"
                    elif existing_client_id:
                        # Connected to a different client - skip this organization
                        logger.warning(
                            f"Skipping Xero organization '{xero_tenant_name}' (tenant_id: {xero_tenant_id}) "
                            f"as it is already connected to a different client"
                        )
                        continue
                    else:
                        # Entity exists but no client_id (shouldn't happen, but handle gracefully)
                        is_already_connected = False
                else:
                    # Not connected to any client
                    is_already_connected = False

                available_organizations.append({
                    "tenant_id": xero_tenant_id,
                    "tenant_name": xero_tenant_name or "Unnamed Organization",
                    "is_already_connected": is_already_connected,
                    "connection_type": connection.get("tenantType", "ORGANISATION")
                })

            # Store the token data temporarily for the selected organization
            # We'll use the 'oauth_tokens' collection that expires after 10 minutes
            temp_tokens_ref = self.db.collection("TEMP_OAUTH_TOKENS").document(f"{client_id}_{user_id}")
            await temp_tokens_ref.set({
                "client_id": client_id,
                "user_id": user_id,
                "token_data": token_data,
                "created_at": SERVER_TIMESTAMP,
                "expires_at": datetime.utcnow() + timedelta(minutes=10)
            })

            return client_id, available_organizations

        except json.JSONDecodeError:
            raise ValueError("Invalid state parameter format")
        except Exception as e:
            logger.error(f"OAuth organization selection error: {str(e)}")
            raise

    async def connect_selected_xero_organization(
        self,
        client_id: str,
        selected_tenant_id: str,
        current_user
    ) -> Dict[str, Any]:
        """
        Connect to a specific selected Xero organization - optimized for speed
        """
        start_time = time.time()
        logger.info(f"Starting connection for client {client_id}, tenant {selected_tenant_id}")

        try:
            # Step 1: Get the temporary token data (OPTIMIZED)
            step_start = time.time()
            temp_tokens_ref = self.db.collection("TEMP_OAUTH_TOKENS").document(f"{client_id}_{current_user.uid}")
            temp_tokens_doc = await temp_tokens_ref.get()
            logger.info(f"Step 1 (get temp tokens) took {time.time() - step_start:.2f}s")

            if not temp_tokens_doc.exists:
                raise ValueError("OAuth session expired. Please restart the connection process.")

            temp_data = temp_tokens_doc.to_dict()
            token_data = temp_data.get("token_data")
            stored_connections = temp_data.get("new_connections", [])

            if not token_data:
                raise ValueError("Invalid OAuth session data")

            # Step 2: Find the selected connection from stored data
            step_start = time.time()
            selected_connection = None
            for connection in stored_connections:
                if connection.get("tenant_id") == selected_tenant_id:
                    selected_connection = connection
                    break

            if not selected_connection:
                raise ValueError("Selected organization is not available")
            logger.info(f"Step 2 (find connection) took {time.time() - step_start:.2f}s")

            # Step 3: Convert the stored connection format back to Xero API format
            step_start = time.time()
            xero_format_connection = {
                "tenantId": selected_connection.get("tenant_id"),
                "tenantName": selected_connection.get("tenant_name"),
                "tenantType": selected_connection.get("connection_type", "ORGANISATION")
            }
            logger.info(f"Step 3 (format conversion) took {time.time() - step_start:.2f}s")

            # Step 4: Validate that the selected organization is not already connected to another client
            # Note: This validation is also done in _process_xero_connection, but we do it here for early detection
            step_start = time.time()
            entity_ref = self.db.collection("ENTITIES").document(selected_tenant_id)
            entity_doc = await entity_ref.get()

            if entity_doc.exists:
                existing_data = entity_doc.to_dict()
                existing_client_id = existing_data.get("client_id")

                if existing_client_id and existing_client_id != client_id:
                    entity_name = existing_data.get("entity_name", "Unknown Organization")
                    logger.error(
                        f"SECURITY ALERT: User attempted to connect Xero organization '{entity_name}' "
                        f"(tenant_id: {selected_tenant_id}) to client '{client_id}' but it's already "
                        f"connected to client '{existing_client_id}'"
                    )
                    raise ValueError(
                        f"Xero organization '{entity_name}' is already connected to another client. "
                        f"A Xero organization can only be connected to one client at a time. "
                        f"Please disconnect it from the other client first if you need to connect it here."
                    )

            logger.info(f"Step 4 (validation) took {time.time() - step_start:.2f}s")

            # Step 5: Process the selected connection (this will create the entity and store tokens)
            step_start = time.time()
            entity_info = await self._process_xero_connection(
                xero_format_connection, token_data, client_id, current_user.uid
            )
            logger.info(f"Step 5 (process connection) took {time.time() - step_start:.2f}s")

            # Step 6: Clean up temporary tokens in background (don't wait for this)
            asyncio.create_task(self._cleanup_temp_tokens(temp_tokens_ref))

            total_time = time.time() - start_time
            logger.info(f"Total connection time: {total_time:.2f}s")

            return {
                "message": "Xero organization connected successfully",
                "entity": entity_info
            }

        except ValueError:
            # Re-raise ValueError as-is (these are user-facing errors)
            raise
        except Exception as e:
            total_time = time.time() - start_time
            logger.error(f"Selected organization connection error after {total_time:.2f}s: {str(e)}")
            raise ValueError(f"Failed to connect organization: {str(e)}")

    async def _cleanup_temp_tokens(self, temp_tokens_ref):
        """Clean up temporary tokens - background operation"""
        try:
            await temp_tokens_ref.delete()
        except Exception as cleanup_error:
            # Log cleanup error but don't fail the connection
            logger.warning(f"Failed to cleanup temporary tokens: {cleanup_error}")

    async def _validate_xero_tenant_not_connected_elsewhere(self, xero_tenant_id: str, client_id: str):
        """
        Validate that a Xero tenant is not already connected to a different client.
        Raises ValueError if the tenant is already connected elsewhere.
        """
        entity_ref = self.db.collection("ENTITIES").document(xero_tenant_id)
        entity_doc = await entity_ref.get()

        if entity_doc.exists:
            existing_client_id = entity_doc.to_dict().get("client_id")
            if existing_client_id and existing_client_id != client_id:
                # Get the organization name for a better error message
                entity_name = entity_doc.to_dict().get("entity_name", "Unknown Organization")
                raise ValueError(
                    f"Xero organization '{entity_name}' is already connected to another client. "
                    f"A Xero organization can only be connected to one client at a time. "
                    f"Please disconnect it from the other client first if you need to connect it here."
                )