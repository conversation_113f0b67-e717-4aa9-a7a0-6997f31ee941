import logging
import json
import traceback
import sys
from google.cloud import error_reporting

def setup_logging():
    """
    Set up logging configuration for the cloud function.
    
    Returns:
        Logger object
    """
    # Configure logging
    logger = logging.getLogger()
    
    # If the logger already has handlers, don't add another one
    if not logger.handlers:
        handler = logging.StreamHandler(sys.stdout)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    # Set log level from environment variable or default to INFO
    log_level = logging.INFO
    logger.setLevel(log_level)
    
    return logger

def handle_error(exception, message="An error occurred"):
    """
    Handle exceptions by logging them and reporting to Error Reporting.
    
    Args:
        exception: The exception that was raised
        message: A descriptive message about what was happening when the error occurred
        
    Returns:
        Dictionary with error details
    """
    logger = logging.getLogger()
    
    # Log the error
    logger.error(f"{message}: {str(exception)}")
    logger.error(traceback.format_exc())
    
    # Report the error to Error Reporting
    try:
        error_client = error_reporting.Client()
        error_client.report_exception()
    except Exception as e:
        logger.error(f"Failed to report error to Error Reporting: {str(e)}")
    
    # Return error response
    return {
        "status": "error",
        "message": message,
        "error": str(exception)
    }
