import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import '@testing-library/jest-dom';
import { SyncStatusDisplay, useSyncStatusPolling } from '@/components/ui/sync-status-display';

// Mock the API calls
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock sync status data
const mockSyncStatusSyncing = {
  is_syncing: true,
  current_step: 'accounts',
  progress_percentage: 45,
  estimated_remaining: '8 minutes',
  user_message: 'Syncing Chart of Accounts...',
  sync_duration_warning: 'Xero operations typically take 5-15 minutes'
};

const mockSyncStatusComplete = {
  is_syncing: false,
  current_step: 'completed',
  progress_percentage: 100,
  estimated_remaining: null,
  user_message: 'Sync completed successfully',
  last_sync_completed: '2025-05-30T10:30:00Z'
};

const mockSyncStatusError = {
  is_syncing: false,
  current_step: 'error',
  progress_percentage: 0,
  estimated_remaining: null,
  user_message: 'Sync failed due to connection error'
};

describe('SyncStatusDisplay Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders compact sync status correctly when syncing', () => {
    render(
      <SyncStatusDisplay
        entityId="test-entity"
        entityName="Test Entity"
        syncStatus={mockSyncStatusSyncing}
        compact={true}
      />
    );

    expect(screen.getByText('Syncing')).toBeInTheDocument();
    expect(screen.getByText('~8 minutes remaining')).toBeInTheDocument();
  });

  it('renders full sync status with progress bar when syncing', () => {
    render(
      <SyncStatusDisplay
        entityId="test-entity"
        entityName="Test Entity"
        syncStatus={mockSyncStatusSyncing}
        compact={false}
      />
    );

    expect(screen.getByText('Data Sync Status')).toBeInTheDocument();
    expect(screen.getByText('Syncing Chart of Accounts')).toBeInTheDocument();
    expect(screen.getByText('Syncing Chart of Accounts...')).toBeInTheDocument();
    expect(screen.getByText('Progress')).toBeInTheDocument();
    expect(screen.getByText('45%')).toBeInTheDocument();
    expect(screen.getByText('Estimated time remaining: 8 minutes')).toBeInTheDocument();
  });

  it('displays performance warning when syncing', () => {
    render(
      <SyncStatusDisplay
        entityId="test-entity"
        entityName="Test Entity"
        syncStatus={mockSyncStatusSyncing}
        compact={false}
      />
    );

    expect(screen.getByText('Please be patient')).toBeInTheDocument();
    expect(screen.getByText('Xero operations typically take 5-15 minutes')).toBeInTheDocument();
    expect(screen.getByText('You can safely close this page - sync will continue in the background.')).toBeInTheDocument();
  });

  it('renders completed status correctly', () => {
    render(
      <SyncStatusDisplay
        entityId="test-entity"
        entityName="Test Entity"
        syncStatus={mockSyncStatusComplete}
        compact={false}
      />
    );

    expect(screen.getByText('Complete')).toBeInTheDocument();
    expect(screen.getByText('Sync Complete')).toBeInTheDocument();
    expect(screen.getByText('Sync completed successfully')).toBeInTheDocument();
    expect(screen.getByText(/Last sync completed:/)).toBeInTheDocument();
  });

  it('renders error status correctly', () => {
    render(
      <SyncStatusDisplay
        entityId="test-entity"
        entityName="Test Entity"
        syncStatus={mockSyncStatusError}
        compact={false}
      />
    );

    expect(screen.getByText('Error')).toBeInTheDocument();
    expect(screen.getByText('Sync Error')).toBeInTheDocument();
    expect(screen.getByText('Sync failed due to connection error')).toBeInTheDocument();
  });

  it('calls onRefreshStatus when refresh button is clicked', () => {
    const mockRefresh = vi.fn();

    render(
      <SyncStatusDisplay
        entityId="test-entity"
        entityName="Test Entity"
        syncStatus={mockSyncStatusComplete}
        onRefreshStatus={mockRefresh}
        compact={false}
      />
    );

    const refreshButton = screen.getByRole('button');
    fireEvent.click(refreshButton);

    expect(mockRefresh).toHaveBeenCalledTimes(1);
  });

  it('calls onViewDetails when view details button is clicked', () => {
    const mockViewDetails = vi.fn();

    render(
      <SyncStatusDisplay
        entityId="test-entity"
        entityName="Test Entity"
        syncStatus={mockSyncStatusComplete}
        onViewDetails={mockViewDetails}
        compact={false}
      />
    );

    const viewDetailsButton = screen.getByText('View Details');
    fireEvent.click(viewDetailsButton);

    expect(mockViewDetails).toHaveBeenCalledTimes(1);
  });

  it('shows trigger sync button when not syncing', () => {
    render(
      <SyncStatusDisplay
        entityId="test-entity"
        entityName="Test Entity"
        syncStatus={mockSyncStatusComplete}
        onViewDetails={() => {}}
        compact={false}
      />
    );

    expect(screen.getByText('Trigger Sync')).toBeInTheDocument();
  });

  it('does not show trigger sync button when syncing', () => {
    render(
      <SyncStatusDisplay
        entityId="test-entity"
        entityName="Test Entity"
        syncStatus={mockSyncStatusSyncing}
        onViewDetails={() => {}}
        compact={false}
      />
    );

    expect(screen.queryByText('Trigger Sync')).not.toBeInTheDocument();
  });
});

describe('useSyncStatusPolling Hook', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('fetches sync status on mount', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ sync_status: mockSyncStatusSyncing })
    });

    const TestComponent = () => {
      const { syncStatus, isLoading, error } = useSyncStatusPolling('test-entity', true);
      return (
        <div>
          <div data-testid="loading">{isLoading ? 'loading' : 'not-loading'}</div>
          <div data-testid="error">{error || 'no-error'}</div>
          <div data-testid="syncing">{syncStatus?.is_syncing ? 'syncing' : 'not-syncing'}</div>
        </div>
      );
    };

    render(<TestComponent />);

    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith('/api/entities/test-entity/sync/status');
    });

    await waitFor(() => {
      expect(screen.getByTestId('syncing')).toHaveTextContent('syncing');
    });
  });

  it('polls every 5 seconds when syncing', async () => {
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => ({ sync_status: mockSyncStatusSyncing })
    });

    const TestComponent = () => {
      const { syncStatus } = useSyncStatusPolling('test-entity', true);
      return <div data-testid="syncing">{syncStatus?.is_syncing ? 'syncing' : 'not-syncing'}</div>;
    };

    render(<TestComponent />);

    // Initial call
    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledTimes(1);
    });

    // Advance timer by 5 seconds
    vi.advanceTimersByTime(5000);

    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledTimes(2);
    });
  });

  it('polls every 30 seconds when not syncing', async () => {
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => ({ sync_status: mockSyncStatusComplete })
    });

    const TestComponent = () => {
      const { syncStatus } = useSyncStatusPolling('test-entity', true);
      return <div data-testid="syncing">{syncStatus?.is_syncing ? 'syncing' : 'not-syncing'}</div>;
    };

    render(<TestComponent />);

    // Initial call
    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledTimes(1);
    });

    // Advance timer by 5 seconds (should not trigger new call)
    vi.advanceTimersByTime(5000);
    expect(mockFetch).toHaveBeenCalledTimes(1);

    // Advance timer by 30 seconds total
    vi.advanceTimersByTime(25000);

    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledTimes(2);
    });
  });

  it('handles fetch errors gracefully', async () => {
    mockFetch.mockRejectedValueOnce(new Error('Network error'));

    const TestComponent = () => {
      const { error } = useSyncStatusPolling('test-entity', true);
      return <div data-testid="error">{error || 'no-error'}</div>;
    };

    render(<TestComponent />);

    await waitFor(() => {
      expect(screen.getByTestId('error')).toHaveTextContent('Network error');
    });
  });

  it('does not fetch when disabled', () => {
    const TestComponent = () => {
      useSyncStatusPolling('test-entity', false);
      return <div>Test</div>;
    };

    render(<TestComponent />);

    expect(mockFetch).not.toHaveBeenCalled();
  });
});

// Integration test for the complete sync status workflow
describe('Sync Status Integration', () => {
  it('displays complete sync workflow from start to finish', async () => {
    // Start with syncing status
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ sync_status: mockSyncStatusSyncing })
    });

    const TestComponent = () => {
      const { syncStatus, refetch } = useSyncStatusPolling('test-entity', true);

      return (
        <div>
          <SyncStatusDisplay
            entityId="test-entity"
            entityName="Test Entity"
            syncStatus={syncStatus || mockSyncStatusSyncing}
            onRefreshStatus={refetch}
            compact={false}
          />
        </div>
      );
    };

    render(<TestComponent />);

    // Should show syncing state
    await waitFor(() => {
      expect(screen.getByText('Syncing')).toBeInTheDocument();
      expect(screen.getByText('Please be patient')).toBeInTheDocument();
    });

    // Simulate sync completion
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ sync_status: mockSyncStatusComplete })
    });

    // Click refresh to get updated status
    const refreshButton = screen.getByRole('button');
    fireEvent.click(refreshButton);

    // Should show completed state
    await waitFor(() => {
      expect(screen.getByText('Complete')).toBeInTheDocument();
      expect(screen.getByText('Trigger Sync')).toBeInTheDocument();
    });
  });
});
