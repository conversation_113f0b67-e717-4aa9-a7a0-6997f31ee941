import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from './card';
import { Badge } from './badge';
import { Progress } from './progress';
import { Alert, AlertDescription } from './alert';
import { Button } from './button';
import { 
  Loader2, 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  RefreshCw,
  Info,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';

export interface SyncStatus {
  is_syncing: boolean;
  current_step: string;
  progress_percentage: number;
  estimated_remaining?: string;
  user_message: string;
  last_sync_completed?: string;
  sync_duration_warning?: string;
}

export interface SyncStatusDisplayProps {
  entityId: string;
  entityName: string;
  syncStatus: SyncStatus;
  onRefreshStatus?: () => void;
  onViewDetails?: () => void;
  className?: string;
  compact?: boolean;
}

const SYNC_STEPS = [
  { key: 'idle', label: 'Ready', icon: CheckCircle },
  { key: 'connecting', label: 'Connecting to <PERSON><PERSON>', icon: RefreshCw },
  { key: 'accounts', label: 'Syncing Chart of Accounts', icon: Loader2 },
  { key: 'contacts', label: 'Syncing Contacts', icon: Loader2 },
  { key: 'transactions', label: 'Syncing Transactions', icon: Loader2 },
  { key: 'processing', label: 'Processing Data', icon: Loader2 },
  { key: 'completed', label: 'Sync Complete', icon: CheckCircle },
  { key: 'error', label: 'Sync Error', icon: AlertCircle }
];

export function SyncStatusDisplay({
  entityId,
  entityName,
  syncStatus,
  onRefreshStatus,
  onViewDetails,
  className,
  compact = false
}: SyncStatusDisplayProps) {
  const currentStep = SYNC_STEPS.find(step => step.key === syncStatus.current_step) || SYNC_STEPS[0];
  const IconComponent = currentStep.icon;

  const getStatusColor = () => {
    if (syncStatus.is_syncing) return 'blue';
    if (syncStatus.current_step === 'error') return 'red';
    if (syncStatus.current_step === 'completed') return 'green';
    return 'gray';
  };

  const getStatusText = () => {
    if (syncStatus.is_syncing) return 'Syncing';
    if (syncStatus.current_step === 'error') return 'Error';
    if (syncStatus.current_step === 'completed') return 'Complete';
    return 'Ready';
  };

  if (compact) {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <IconComponent 
          className={cn(
            "h-4 w-4",
            syncStatus.is_syncing && "animate-spin",
            getStatusColor() === 'blue' && "text-blue-500",
            getStatusColor() === 'green' && "text-green-500",
            getStatusColor() === 'red' && "text-red-500",
            getStatusColor() === 'gray' && "text-gray-500"
          )}
        />
        <Badge variant={getStatusColor() === 'blue' ? 'default' : 
                       getStatusColor() === 'green' ? 'secondary' :
                       getStatusColor() === 'red' ? 'destructive' : 'outline'}>
          {getStatusText()}
        </Badge>
        {syncStatus.is_syncing && (
          <span className="text-xs text-muted-foreground">
            {syncStatus.estimated_remaining && `~${syncStatus.estimated_remaining} remaining`}
          </span>
        )}
      </div>
    );
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <IconComponent 
              className={cn(
                "h-5 w-5",
                syncStatus.is_syncing && "animate-spin",
                getStatusColor() === 'blue' && "text-blue-500",
                getStatusColor() === 'green' && "text-green-500",
                getStatusColor() === 'red' && "text-red-500",
                getStatusColor() === 'gray' && "text-gray-500"
              )}
            />
            Data Sync Status
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant={getStatusColor() === 'blue' ? 'default' : 
                           getStatusColor() === 'green' ? 'secondary' :
                           getStatusColor() === 'red' ? 'destructive' : 'outline'}>
              {getStatusText()}
            </Badge>
            {onRefreshStatus && (
              <Button variant="ghost" size="sm" onClick={onRefreshStatus}>
                <RefreshCw className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Current Status Message */}
        <div className="space-y-2">
          <p className="text-sm font-medium">{currentStep.label}</p>
          <p className="text-sm text-muted-foreground">{syncStatus.user_message}</p>
        </div>

        {/* Progress Bar */}
        {syncStatus.is_syncing && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Progress</span>
              <span>{syncStatus.progress_percentage}%</span>
            </div>
            <Progress value={syncStatus.progress_percentage} className="h-2" />
            {syncStatus.estimated_remaining && (
              <p className="text-xs text-muted-foreground">
                Estimated time remaining: {syncStatus.estimated_remaining}
              </p>
            )}
          </div>
        )}

        {/* Performance Warning */}
        {syncStatus.sync_duration_warning && (
          <Alert>
            <Clock className="h-4 w-4" />
            <AlertDescription className="text-sm">
              <div className="space-y-1">
                <p className="font-medium">Please be patient</p>
                <p>{syncStatus.sync_duration_warning}</p>
                <p className="text-xs">You can safely close this page - sync will continue in the background.</p>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Last Sync Info */}
        {syncStatus.last_sync_completed && !syncStatus.is_syncing && (
          <div className="text-xs text-muted-foreground">
            Last sync completed: {new Date(syncStatus.last_sync_completed).toLocaleString()}
          </div>
        )}

        {/* Action Buttons */}
        {onViewDetails && (
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={onViewDetails}>
              <Info className="h-4 w-4 mr-1" />
              View Details
            </Button>
            {!syncStatus.is_syncing && (
              <Button variant="outline" size="sm">
                <Zap className="h-4 w-4 mr-1" />
                Trigger Sync
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Hook for managing sync status polling
export function useSyncStatusPolling(entityId: string, enabled: boolean = true) {
  const [syncStatus, setSyncStatus] = React.useState<SyncStatus | null>(null);
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  const fetchSyncStatus = React.useCallback(async () => {
    if (!entityId || !enabled) return;
    
    try {
      setIsLoading(true);
      setError(null);
      
      // Replace with actual API call
      const response = await fetch(`/api/entities/${entityId}/sync/status`);
      if (!response.ok) throw new Error('Failed to fetch sync status');
      
      const data = await response.json();
      setSyncStatus(data.sync_status);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  }, [entityId, enabled]);

  // Poll every 5 seconds when syncing, every 30 seconds when idle
  React.useEffect(() => {
    if (!enabled) return;

    fetchSyncStatus();
    
    const pollInterval = syncStatus?.is_syncing ? 5000 : 30000;
    const interval = setInterval(fetchSyncStatus, pollInterval);
    
    return () => clearInterval(interval);
  }, [fetchSyncStatus, enabled, syncStatus?.is_syncing]);

  return {
    syncStatus,
    isLoading,
    error,
    refetch: fetchSyncStatus
  };
}
