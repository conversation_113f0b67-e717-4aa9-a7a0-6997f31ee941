import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams, Link } from 'react-router-dom';
import { toast } from 'sonner';

// Add global styles to prevent scrolling
const GlobalStyles = () => {
  useEffect(() => {
    // Save original styles
    const originalStyle = window.getComputedStyle(document.body).overflow;

    // Apply no-scroll style
    document.body.style.overflow = 'hidden';
    document.documentElement.style.overflow = 'hidden';

    // Cleanup function to restore original styles
    return () => {
      document.body.style.overflow = originalStyle;
      document.documentElement.style.overflow = originalStyle;
    };
  }, []);

  return null;
};

// Import Shadcn UI components
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '../components/ui/card';
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "../components/ui/alert";

// Import Lucide icons
import {
  Loader2,
  Lock,
  AlertCircle,
  CheckCircle,
  Eye,
  EyeOff,
  KeyRound,
} from 'lucide-react';

// Import the DRCR logo
import drcrLogo from '../assets/logo.png';

export default function ResetPasswordPage() {
    const [searchParams] = useSearchParams();
    const navigate = useNavigate();
    const token = searchParams.get('token');

    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [isVerifying, setIsVerifying] = useState(true);
    const [tokenValid, setTokenValid] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [passwordError, setPasswordError] = useState<string | null>(null);
    const [confirmPasswordError, setConfirmPasswordError] = useState<string | null>(null);
    const [success, setSuccess] = useState(false);

    useEffect(() => {
        if (!token) {
            setError('No reset token provided');
            setIsVerifying(false);
            return;
        }

        // Verify token on component mount
        verifyToken();
    }, [token]);

    const verifyToken = async () => {
        try {
            const response = await fetch(`/auth/verify-reset-token/${token}`);
            const data = await response.json();
            
            if (data.valid) {
                setTokenValid(true);
            } else {
                setError(data.error || 'Invalid or expired reset token');
            }
        } catch (err) {
            setError('Failed to verify reset token');
        } finally {
            setIsVerifying(false);
        }
    };

    const validatePassword = (passwordToValidate: string) => {
        if (!passwordToValidate) {
            setPasswordError("Password is required.");
            return false;
        }
        if (passwordToValidate.length < 8) {
            setPasswordError("Password must be at least 8 characters long.");
            return false;
        }
        setPasswordError(null);
        return true;
    };

    const validateConfirmPassword = (confirmPasswordToValidate: string) => {
        if (!confirmPasswordToValidate) {
            setConfirmPasswordError("Please confirm your password.");
            return false;
        }
        if (confirmPasswordToValidate !== password) {
            setConfirmPasswordError("Passwords do not match.");
            return false;
        }
        setConfirmPasswordError(null);
        return true;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setError(null);

        const isPasswordValid = validatePassword(password);
        const isConfirmPasswordValid = validateConfirmPassword(confirmPassword);

        if (!isPasswordValid || !isConfirmPasswordValid) {
            return;
        }

        setIsLoading(true);
        try {
            const response = await fetch('/auth/reset-password', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ 
                    token,
                    new_password: password 
                }),
            });

            const data = await response.json();

            if (response.ok) {
                setSuccess(true);
                toast.success('Password reset successfully!');
                setTimeout(() => {
                    navigate('/login');
                }, 2000);
            } else {
                setError(data.detail?.message || data.detail || 'Failed to reset password');
                toast.error('Failed to reset password');
            }
        } catch (err) {
            setError('An error occurred. Please try again.');
            toast.error('An error occurred. Please try again.');
        } finally {
            setIsLoading(false);
        }
    };

    if (isVerifying) {
        return (
            <>
                <GlobalStyles />
                <div className="h-screen flex flex-col items-center justify-center bg-background p-0 m-0 overflow-hidden font-sans">
                    <Card className="w-full max-w-lg shadow-sm bg-card rounded-xl border">
                        <CardHeader className="text-center pt-4 pb-2">
                            <div className="w-32 h-auto mx-auto mb-1">
                                <img
                                    src={drcrLogo}
                                    alt="DRCR Logo"
                                    className="w-full h-auto"
                                />
                            </div>
                        </CardHeader>
                        <CardContent className="px-10 pb-3">
                            <div className="flex flex-col items-center space-y-4">
                                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                                <p className="text-base text-muted-foreground">Verifying reset token...</p>
                            </div>
                        </CardContent>
                        <CardFooter className="flex flex-col items-center text-xs text-muted-foreground pt-4 pb-6">
                            <p>&copy; {new Date().getFullYear()} DRCR Labs. All rights reserved.</p>
                        </CardFooter>
                    </Card>
                </div>
            </>
        );
    }

    if (!tokenValid && !success) {
        return (
            <>
                <GlobalStyles />
                <div className="h-screen flex flex-col items-center justify-center bg-background p-0 m-0 overflow-hidden font-sans">
                    <Card className="w-full max-w-lg shadow-sm bg-card rounded-xl border">
                        <CardHeader className="text-center pt-4 pb-2">
                            <div className="w-32 h-auto mx-auto mb-1">
                                <img
                                    src={drcrLogo}
                                    alt="DRCR Logo"
                                    className="w-full h-auto"
                                />
                            </div>
                        </CardHeader>
                        <CardContent className="px-10 pb-3">
                            <div className="flex flex-col items-center space-y-4">
                                <AlertCircle className="h-12 w-12 text-destructive" />
                                <div className="text-center">
                                    <h2 className="text-xl font-semibold text-foreground mb-2">Invalid Reset Link</h2>
                                    <p className="text-base text-muted-foreground mb-4">
                                        {error || 'This password reset link is invalid or has expired.'}
                                    </p>
                                    <Link to="/forgot-password">
                                        <Button variant="outline" className="w-full">
                                            Request New Reset Link
                                        </Button>
                                    </Link>
                                </div>
                            </div>
                        </CardContent>
                        <CardFooter className="flex flex-col items-center text-xs text-muted-foreground pt-4 pb-6">
                            <p>&copy; {new Date().getFullYear()} DRCR Labs. All rights reserved.</p>
                        </CardFooter>
                    </Card>
                </div>
            </>
        );
    }

    if (success) {
        return (
            <>
                <GlobalStyles />
                <div className="h-screen flex flex-col items-center justify-center bg-background p-0 m-0 overflow-hidden font-sans">
                    <Card className="w-full max-w-lg shadow-sm bg-card rounded-xl border">
                        <CardHeader className="text-center pt-4 pb-2">
                            <div className="w-32 h-auto mx-auto mb-1">
                                <img
                                    src={drcrLogo}
                                    alt="DRCR Logo"
                                    className="w-full h-auto"
                                />
                            </div>
                        </CardHeader>
                        <CardContent className="px-10 pb-3">
                            <div className="flex flex-col items-center space-y-4">
                                <CheckCircle className="h-12 w-12 text-green-500" />
                                <div className="text-center">
                                    <h2 className="text-xl font-semibold text-foreground mb-2">Password Reset Successful</h2>
                                    <p className="text-base text-muted-foreground mb-4">
                                        Your password has been successfully reset. You will be redirected to the login page shortly.
                                    </p>
                                    <Link to="/login">
                                        <Button className="w-full">
                                            Go to Login
                                        </Button>
                                    </Link>
                                </div>
                            </div>
                        </CardContent>
                        <CardFooter className="flex flex-col items-center text-xs text-muted-foreground pt-4 pb-6">
                            <p>&copy; {new Date().getFullYear()} DRCR Labs. All rights reserved.</p>
                        </CardFooter>
                    </Card>
                </div>
            </>
        );
    }

    return (
        <>
            <GlobalStyles />
            <div className="h-screen flex flex-col items-center justify-center bg-background p-0 m-0 overflow-hidden font-sans">
                <Card className="w-full max-w-lg shadow-sm bg-card rounded-xl border">
                    <CardHeader className="text-center pt-4 pb-2">
                        <div className="w-32 h-auto mx-auto mb-1">
                            <img
                                src={drcrLogo}
                                alt="DRCR Logo"
                                className="w-full h-auto"
                            />
                        </div>
                        <CardTitle className="text-xl font-semibold text-foreground">Reset Your Password</CardTitle>
                        <CardDescription className="text-sm text-muted-foreground">
                            Enter your new password below
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="px-10 pb-3">
                        <form onSubmit={handleSubmit} className="flex flex-col space-y-4">
                            <div className={`h-${error ? 'auto' : '0'} mb-${error ? '4' : '0'} transition-all duration-200`}>
                                {error && (
                                    <Alert variant="destructive" className="p-3 text-sm">
                                        <AlertCircle className="h-4 w-4" />
                                        <AlertTitle className="text-sm font-medium">Reset Failed</AlertTitle>
                                        <AlertDescription className="text-xs">{error}</AlertDescription>
                                    </Alert>
                                )}
                            </div>
                            
                            <div className="space-y-2">
                                <Label htmlFor="password" className="text-base font-medium text-foreground block mb-1">
                                    New Password
                                </Label>
                                <div className="relative">
                                    <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                    <Input
                                        id="password"
                                        type={showPassword ? "text" : "password"}
                                        placeholder="••••••••"
                                        value={password}
                                        onChange={(e) => { 
                                            setPassword(e.target.value); 
                                            if (passwordError) validatePassword(e.target.value);
                                            if (confirmPassword && confirmPasswordError) validateConfirmPassword(confirmPassword);
                                        }}
                                        onBlur={() => validatePassword(password)}
                                        className={`w-full pl-10 pr-10 h-10 text-base ${passwordError ? 'border-destructive focus:ring-destructive' : 'border-border focus:ring-primary'}`}
                                        autoComplete="new-password"
                                    />
                                    <button
                                        type="button"
                                        onClick={() => setShowPassword(!showPassword)}
                                        className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                                    >
                                        {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                                    </button>
                                </div>
                                <div className="h-5 mt-1">
                                    {passwordError && <p className="text-xs text-destructive m-0">{passwordError}</p>}
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="confirmPassword" className="text-base font-medium text-foreground block mb-1">
                                    Confirm New Password
                                </Label>
                                <div className="relative">
                                    <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                    <Input
                                        id="confirmPassword"
                                        type={showConfirmPassword ? "text" : "password"}
                                        placeholder="••••••••"
                                        value={confirmPassword}
                                        onChange={(e) => { 
                                            setConfirmPassword(e.target.value); 
                                            if (confirmPasswordError) validateConfirmPassword(e.target.value);
                                        }}
                                        onBlur={() => validateConfirmPassword(confirmPassword)}
                                        className={`w-full pl-10 pr-10 h-10 text-base ${confirmPasswordError ? 'border-destructive focus:ring-destructive' : 'border-border focus:ring-primary'}`}
                                        autoComplete="new-password"
                                    />
                                    <button
                                        type="button"
                                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                        className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                                    >
                                        {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                                    </button>
                                </div>
                                <div className="h-5 mt-1">
                                    {confirmPasswordError && <p className="text-xs text-destructive m-0">{confirmPasswordError}</p>}
                                </div>
                            </div>

                            <Button
                                type="submit"
                                className="w-full h-10 text-base font-medium flex items-center justify-center gap-2 rounded-md disabled:opacity-70 disabled:cursor-not-allowed transition-colors"
                                disabled={isLoading}
                            >
                                {isLoading ? (
                                    <Loader2 className="h-5 w-5 animate-spin" />
                                ) : (
                                    <KeyRound className="h-5 w-5" />
                                )}
                                {isLoading ? 'Resetting Password...' : 'Reset Password'}
                            </Button>
                            
                            {/* Back to Login Link */}
                            <div className="text-center pt-2">
                                <Link 
                                    to="/login" 
                                    className="text-sm text-muted-foreground hover:text-primary transition-colors underline"
                                >
                                    Back to Login
                                </Link>
                            </div>
                        </form>
                    </CardContent>
                    <CardFooter className="flex flex-col items-center text-xs text-muted-foreground pt-4 pb-6">
                        <p>&copy; {new Date().getFullYear()} DRCR Labs. All rights reserved.</p>
                    </CardFooter>
                </Card>
            </div>
        </>
    );
} 