import React, { useState } from 'react';

// --- Shadcn/UI & Lucide Imports ---
import { But<PERSON> } from '@/components/ui/button';
import {
    DraggableDialog,
    DraggableDialogContent,
    DraggableDialogDescription,
    DraggableDialogFooter,
    DraggableDialogHeader,
    DraggableDialogTitle,
    DraggableDialogClose,
} from '@/components/ui/draggable-dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import {
    Loader2,
    X,
    Check,
    AlertCircle,
    Info,
} from 'lucide-react';

// --- Type Definitions ---
type MonthlyBreakdownItem = { 
    status: 'proposed' | 'posted' | 'posting_error' | 'matched_manual' | 'skipped' | 'posting'; 
    amount: number; 
    journalId?: string; 
    error?: string; 
};

type ScheduleSummary = {
    status: 'proposed' | 'partially_posted' | 'fully_posted' | 'skipped' | 'error_posting';
    originalAmount: number;
    amortizationStartDate: string;
    amortizationEndDate: string;
    numberOfPeriods: number;
    amortizationAccountCode: string;
    expenseAccountCode: string | null;
};

type LineItem = {
    lineItemId: string;
    description: string;
    lineAmount: number;
    isAmortized: boolean;
};

type TransactionDetails = {
    transactionId: string;
    reference: string;
    counterpartyName: string;
    hasAttachment: boolean;
    attachmentId?: string;
    lineItems: LineItem[];
    currencyCode: string;
};

// --- Component Props ---
interface SideBySideReviewProps {
    isOpen: boolean;
    onClose: () => void;
    transactionId: string | null;
    transactionData: TransactionDetails | null;
    scheduleData: ScheduleSummary | null;
    attachmentUrl: string | null;
    isLoading: boolean;
    error: string | null;
}

// --- Side-by-Side Review Component ---
export function SideBySideReview({
    isOpen,
    onClose,
    transactionId,
    transactionData,
    scheduleData,
    attachmentUrl,
    isLoading,
    error
}: SideBySideReviewProps) {

    // Format dates for display
    const formatDate = (dateString: string | undefined | null) => {
        if (!dateString) return 'N/A';
        try {
            return new Date(dateString).toLocaleDateString();
        } catch (e) {
            return 'Invalid Date';
        }
    };

    return (
        <DraggableDialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
            <DraggableDialogContent className="flex flex-col p-0 gap-0">
                <DraggableDialogHeader className="p-4 border-b">
                    <DraggableDialogTitle>Side-by-Side Review</DraggableDialogTitle>
                    {transactionData && (
                        <DraggableDialogDescription>
                            {`Reviewing ${transactionData.reference || transactionData.transactionId} from ${transactionData.counterpartyName}`}
                        </DraggableDialogDescription>
                    )}
                </DraggableDialogHeader>

                {/* Main Content Area */}
                <div className="flex-grow grid grid-cols-1 md:grid-cols-2 gap-0 overflow-y-auto">

                    {/* Loading State */}
                    {isLoading && (
                        <div className="col-span-1 md:col-span-2 p-6 flex flex-col items-center justify-center space-y-4">
                            <Loader2 className="h-12 w-12 animate-spin text-blue-600" />
                            <p className="text-muted-foreground">Loading review data...</p>
                        </div>
                    )}

                    {/* Error State */}
                    {!isLoading && error && (
                        <div className="col-span-1 md:col-span-2 p-4">
                            <Alert variant="destructive">
                                <AlertCircle className="h-4 w-4" />
                                <AlertTitle>Error Loading Data</AlertTitle>
                                <AlertDescription>{error}</AlertDescription>
                            </Alert>
                        </div>
                    )}

                    {/* Data Display State */}
                    {!isLoading && !error && transactionData && (
                        <>
                            {/* Left Column: Attachment Preview */}
                            <div className="border-r flex flex-col">
                                <h3 className="text-base font-semibold p-3 border-b bg-gray-50 flex-shrink-0">Source Document</h3>
                                <div className="flex-grow p-1 bg-gray-200">
                                    {attachmentUrl ? (
                                        <iframe
                                            src={attachmentUrl}
                                            title="Attachment Preview"
                                            className="w-full h-full border-0 min-h-[400px] md:min-h-[600px]"
                                        />
                                    ) : (
                                        <div className="flex items-center justify-center h-full text-gray-500 min-h-[400px] md:min-h-[600px]">
                                            {transactionData.hasAttachment ? (
                                                <div className="text-center space-y-2">
                                                    <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                                                    <span>Loading attachment...</span>
                                                </div>
                                            ): (
                                                <div className="text-center space-y-2">
                                                    <Info className="h-6 w-6 mx-auto" />
                                                    <span>No attachment available.</span>
                                                </div>
                                            )}
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* Right Column: Line Items & Schedule Summary */}
                            <ScrollArea className="h-full">
                                <div className="p-3 space-y-4">
                                    {/* Included vs Excluded Lines */}
                                    <div>
                                        <h4 className="font-medium mb-2 text-sm">Original Line Items:</h4>
                                        <div className="border rounded-md">
                                            <Table>
                                                <TableHeader>
                                                    <TableRow>
                                                        <TableHead>Description</TableHead>
                                                        <TableHead className="text-right">Amount</TableHead>
                                                        <TableHead className="text-center w-[100px]">Amortized?</TableHead>
                                                    </TableRow>
                                                </TableHeader>
                                                <TableBody>
                                                    {transactionData.lineItems.length > 0 ? (
                                                        transactionData.lineItems.map((line) => (
                                                            <TableRow key={line.lineItemId}>
                                                                <TableCell className="align-top">{line.description || '(No description)'}</TableCell>
                                                                <TableCell className="text-right align-top">{line.lineAmount.toFixed(2)}</TableCell>
                                                                <TableCell className="text-center align-top">
                                                                    {line.isAmortized ? (
                                                                        <Check className="h-4 w-4 text-green-600 inline-block" />
                                                                    ) : (
                                                                        <X className="h-4 w-4 text-muted-foreground inline-block" />
                                                                    )}
                                                                </TableCell>
                                                            </TableRow>
                                                        ))
                                                    ) : (
                                                        <TableRow>
                                                            <TableCell colSpan={3} className="text-center text-muted-foreground">No line items found.</TableCell>
                                                        </TableRow>
                                                    )}
                                                </TableBody>
                                            </Table>
                                        </div>
                                    </div>

                                    {/* Schedule Summary */}
                                    {scheduleData ? (
                                        <div>
                                            <h4 className="font-medium mb-2 text-sm">Proposed Schedule Summary:</h4>
                                            <div className="text-sm space-y-1 bg-gray-50 p-3 rounded border">
                                                <p><span className="font-medium w-32 inline-block">Status:</span>
                                                    <Badge variant={scheduleData.status === 'proposed' ? 'outline' : 'secondary'} className={scheduleData.status === 'proposed' ? 'text-yellow-600 border-yellow-300' : ''}>
                                                        {scheduleData.status}
                                                    </Badge>
                                                </p>
                                                <p><span className="font-medium w-32 inline-block">Amount:</span> {scheduleData.originalAmount.toFixed(2)} {transactionData.currencyCode}</p>
                                                <p><span className="font-medium w-32 inline-block">Period:</span> {formatDate(scheduleData.amortizationStartDate)} - {formatDate(scheduleData.amortizationEndDate)} ({scheduleData.numberOfPeriods} months)</p>
                                                <p><span className="font-medium w-32 inline-block">Prepayment Acct:</span> {scheduleData.amortizationAccountCode}</p>
                                                <p><span className="font-medium w-32 inline-block">Expense Acct:</span> {scheduleData.expenseAccountCode || <Badge variant="destructive">Needs Selection!</Badge>}</p>
                                            </div>
                                        </div>
                                    ) : (
                                         <Alert variant="default" className="mt-4">
                                            <Info className="h-4 w-4" />
                                            <AlertTitle>No Schedule</AlertTitle>
                                            <AlertDescription>
                                                No amortization schedule was proposed for this transaction based on current rules.
                                            </AlertDescription>
                                        </Alert>
                                    )}
                                </div>
                            </ScrollArea>
                        </>
                    )}
                </div>

                {/* Footer with Close Button */}
                <DraggableDialogFooter className="p-4 border-t">
                    <DraggableDialogClose asChild>
                        <Button variant="outline" onClick={onClose}>Close</Button>
                    </DraggableDialogClose>
                </DraggableDialogFooter>
            </DraggableDialogContent>
        </DraggableDialog>
    );
} 