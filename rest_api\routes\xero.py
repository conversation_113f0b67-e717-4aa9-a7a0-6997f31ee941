from fastapi import APIRouter, Depends, HTTPException, status, Path, Query, Request, BackgroundTasks, Body
from fastapi.responses import RedirectResponse
from typing import Dict, Any, Optional
import logging
from urllib.parse import urlparse
import time
from datetime import datetime

from ..core.firebase_auth import get_current_user, get_firm_user_with_client_access, AuthUser
from ..dependencies import get_db
from ..services.xero_service import XeroService

logger = logging.getLogger(__name__)
router = APIRouter(tags=["Xero Integration"])


def get_xero_service(db = Depends(get_db)) -> XeroService:
    """Dependency to get xero service instance"""
    return XeroService(db)


@router.get("/connect/initiate/{client_id}")
async def initiate_xero_connection(
    client_id: str = Path(...),
    current_user: AuthUser = Depends(get_current_user),
    xero_service: XeroService = Depends(get_xero_service)
):
    """Initiate Xero OAuth connection for a client"""
    try:
        # Check client access manually
        await get_firm_user_with_client_access(client_id, current_user)

        result = await xero_service.initiate_connection(client_id, current_user)
        return result
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to initiate Xero connection for client {client_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to initiate Xero connection"
        )


@router.get("/{client_id}/xero/configure")
async def get_xero_configuration(
    client_id: str = Path(...),
    entities: Optional[str] = Query(None, description="Comma-separated list of entity IDs"),
    current_user: AuthUser = Depends(get_current_user),
    xero_service: XeroService = Depends(get_xero_service)
):
    """Get Xero configuration page data for a client"""
    try:
        # Check client access manually
        await get_firm_user_with_client_access(client_id, current_user)
        
        # Get client entities and their Xero connection status
        result = await xero_service.get_client_xero_configuration(client_id, current_user, entities)
        return result
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to get Xero configuration for client {client_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get Xero configuration"
        )


@router.get("/callback")
async def xero_oauth_callback(
    code: str = Query(...),
    state: str = Query(...),
    request: Request = None,
    background_tasks: BackgroundTasks = None,
    xero_service: XeroService = Depends(get_xero_service)
):
    """Handle Xero OAuth callback - smart reconnection with organization selection for new connections only"""
    try:
        # Use the smart callback logic
        client_id, entities_processed, needs_selection = await xero_service.handle_smart_oauth_callback(code, state)

        # Get the frontend URL from the request origin or referer
        frontend_url = request.headers.get("origin")
        logger.info(f"🔍 OAuth callback - origin header: {frontend_url}")
        if not frontend_url:
            referer = request.headers.get("referer", "")
            logger.info(f"🔍 OAuth callback - referer header: {referer}")
            if referer:
                # Extract base URL from referer (e.g., "http://localhost:5174/dashboard" -> "http://localhost:5174")
                parsed = urlparse(referer)
                frontend_url = f"{parsed.scheme}://{parsed.netloc}"
                logger.info(f"🔍 OAuth callback - parsed frontend_url from referer: {frontend_url}")
        
        # Final fallback
        if not frontend_url:
            frontend_url = "http://localhost:5173"
            logger.info(f"🔍 OAuth callback - using fallback frontend_url: {frontend_url}")
        
        if frontend_url.endswith("/"):
            frontend_url = frontend_url[:-1]
        
        # Always redirect to organization selector for now
        # This ensures we have a proper frontend route to handle the redirect
        redirect_url = f"{frontend_url}/clients/{client_id}/xero/select-organization"
        logger.info(f"🔍 OAuth callback - final redirect_url: {redirect_url}")

        return RedirectResponse(url=redirect_url)

    except ValueError as e:
        # Handle validation errors
        frontend_url = request.headers.get("origin")
        if not frontend_url:
            referer = request.headers.get("referer", "")
            if referer:
                parsed = urlparse(referer)
                frontend_url = f"{parsed.scheme}://{parsed.netloc}"
        if not frontend_url:
            frontend_url = "http://localhost:5173"
        
        error_url = f"{frontend_url}/xero/error?message={str(e)}"
        return RedirectResponse(url=error_url)
    except Exception as e:
        logger.error(f"OAuth callback error: {str(e)}")
        frontend_url = request.headers.get("origin")
        if not frontend_url:
            referer = request.headers.get("referer", "")
            if referer:
                parsed = urlparse(referer)
                frontend_url = f"{parsed.scheme}://{parsed.netloc}"
        if not frontend_url:
            frontend_url = "http://localhost:5173"
        
        error_url = f"{frontend_url}/xero/error?message=Connection failed"
        return RedirectResponse(url=error_url)


@router.get("/entities/{entity_id}/accounts")
async def get_xero_accounts(
    entity_id: str = Path(...),
    current_user: AuthUser = Depends(get_current_user),
    xero_service: XeroService = Depends(get_xero_service)
):
    """Get chart of accounts from Xero for an entity"""
    try:
        accounts = await xero_service.get_xero_accounts(entity_id, current_user)
        return {"accounts": accounts}
    except ValueError as e:
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=str(e)
            )
        elif "access" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=str(e)
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )
    except Exception as e:
        logger.error(f"Failed to get Xero accounts for entity {entity_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve accounts from Xero"
        )


@router.put("/entities/{entity_id}/settings")
async def update_entity_settings(
    entity_id: str = Path(...),
    settings_data: Dict[str, Any] = Body(...),
    current_user: AuthUser = Depends(get_current_user),
    xero_service: XeroService = Depends(get_xero_service)
):
    """Update entity settings"""
    try:
        updated_settings = await xero_service.update_entity_settings(
            entity_id, settings_data, current_user
        )
        return {"settings": updated_settings}
    except ValueError as e:
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=str(e)
            )
        elif "access" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=str(e)
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )
    except Exception as e:
        logger.error(f"Failed to update entity settings for {entity_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update entity settings"
        )


@router.post("/entities/{entity_id}/revoke")
async def revoke_xero_connection(
    entity_id: str = Path(...),
    current_user: AuthUser = Depends(get_current_user),
    xero_service: XeroService = Depends(get_xero_service)
):
    """Revoke Xero connection for an entity"""
    try:
        result = await xero_service.revoke_connection(entity_id, current_user)
        return result
    except ValueError as e:
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=str(e)
            )
        elif "access" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=str(e)
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )
    except RuntimeError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to revoke Xero connection for entity {entity_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to revoke Xero connection"
        )


@router.get("/callback/organizations")
async def get_available_xero_organizations(
    code: str = Query(...),
    state: str = Query(...),
    request: Request = None,
    xero_service: XeroService = Depends(get_xero_service)
):
    """Get available Xero organizations for user selection"""
    try:
        client_id, organizations = await xero_service.get_available_xero_organizations(code, state)

        # Redirect to the organization selection page
        frontend_url = request.headers.get("origin")
        if not frontend_url:
            referer = request.headers.get("referer", "")
            if referer:
                # Extract base URL from referer (e.g., "http://localhost:5174/dashboard" -> "http://localhost:5174")
                parsed = urlparse(referer)
                frontend_url = f"{parsed.scheme}://{parsed.netloc}"
        
        # Final fallback
        if not frontend_url:
            frontend_url = "http://localhost:5173"
        
        if frontend_url.endswith("/"):
            frontend_url = frontend_url[:-1]
        
        # For now, we'll redirect to a selection page with the organizations data
        # In a real implementation, you might want to store this in a temporary session
        # and redirect to a selection UI
        redirect_url = f"{frontend_url}/clients/{client_id}/xero/select-organization"

        return RedirectResponse(url=redirect_url)

    except ValueError as e:
        # Handle validation errors
        frontend_url = request.headers.get("origin")
        if not frontend_url:
            referer = request.headers.get("referer", "")
            if referer:
                parsed = urlparse(referer)
                frontend_url = f"{parsed.scheme}://{parsed.netloc}"
        if not frontend_url:
            frontend_url = "http://localhost:5173"
        
        error_url = f"{frontend_url}/xero/error?message={str(e)}"
        return RedirectResponse(url=error_url)
    except Exception as e:
        logger.error(f"OAuth organization selection error: {str(e)}")
        frontend_url = request.headers.get("origin")
        if not frontend_url:
            referer = request.headers.get("referer", "")
            if referer:
                parsed = urlparse(referer)
                frontend_url = f"{parsed.scheme}://{parsed.netloc}"
        if not frontend_url:
            frontend_url = "http://localhost:5173"
        
        error_url = f"{frontend_url}/xero/error?message=Organization selection failed"
        return RedirectResponse(url=error_url)


@router.get("/clients/{client_id}/xero/available-organizations")
async def get_client_available_organizations(
    client_id: str = Path(...),
    current_user: AuthUser = Depends(get_current_user),
    xero_service: XeroService = Depends(get_xero_service)
):
    logger.info("THIS IS THE VERY LATEST CODE - CHECKING FOR OAUTH SESSION")
    """Get available Xero organizations for a client (from temporary storage)"""
    try:
        # Check client access manually
        await get_firm_user_with_client_access(client_id, current_user)
        
        # Get temporary OAuth data
        temp_tokens_ref = xero_service.db.collection("TEMP_OAUTH_TOKENS").document(f"{client_id}_{current_user.uid}")
        
        logger.info(f"[PERF_DEBUG] Attempting to get temp_tokens_doc for {client_id}_{current_user.uid}")
        get_start_time = time.time()
        temp_tokens_doc = await temp_tokens_ref.get()
        get_end_time = time.time()
        logger.info(f"[PERF_DEBUG] temp_tokens_ref.get() took {get_end_time - get_start_time:.4f} seconds.")
        
        if not temp_tokens_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No pending OAuth session found. Please restart the connection process."
            )
        
        temp_data = temp_tokens_doc.to_dict()
        
        # Check if the session has expired
        expires_at = temp_data.get("expires_at") # Should be a UTC-aware datetime from Firestore
        logger.info(f"[AUTH_DEBUG] Retrieved expires_at from Firestore: {expires_at} (type: {type(expires_at)})")

        if expires_at:
            # expires_at is a datetime object from Firestore (UTC-aware).
            # .timestamp() gives its Unix timestamp in seconds.
            expires_timestamp = expires_at.timestamp()
            logger.info(f"[AUTH_DEBUG] Calculated expires_timestamp: {expires_timestamp}")
            
            # time.time() gives current Unix timestamp in seconds (UTC).
            current_timestamp = time.time()
            logger.info(f"[AUTH_DEBUG] Calculated current_timestamp: {current_timestamp}")
            
            # Log the difference for clarity
            time_difference_seconds = expires_timestamp - current_timestamp
            logger.info(f"[AUTH_DEBUG] Time difference (expires_timestamp - current_timestamp): {time_difference_seconds} seconds")

            if current_timestamp > expires_timestamp:
                logger.warning(f"[AUTH_DEBUG] OAuth session EXPIRED. Current ({current_timestamp}) > Expires ({expires_timestamp})")
                # Session has expired, clean it up
                await temp_tokens_ref.delete()
                raise HTTPException(
                    status_code=status.HTTP_410_GONE,
                    detail="OAuth session has expired. Please restart the connection process."
                )
            else:
                logger.info(f"[AUTH_DEBUG] OAuth session VALID. Current ({current_timestamp}) <= Expires ({expires_timestamp})")
        else:
            logger.warning("[AUTH_DEBUG] expires_at field not found in session document.")
        
        # Use the stored new_connections data instead of making another API call to Xero
        stored_connections = temp_data.get("new_connections", [])
        
        # Get all tenant IDs at once for batch checking (only relevant if stored_connections is not empty)
        tenant_ids = [conn.get("tenant_id") for conn in stored_connections if conn and conn.get("tenant_id")]
        logger.info(f"[PERF_DEBUG] Number of tenant_ids to check in ENTITIES collection: {len(tenant_ids)}")
        
        # Temporarily bypass the Firestore query for performance testing
        connected_entities = {}
        # if tenant_ids: # Only query if there are potential new connections to check
        #     entities_query = xero_service.db.collection("ENTITIES").where("client_id", "==", client_id).where("status", "==", "active")
        #     # Filter further by the tenant_ids we have, to be more efficient
        #     # Firestore's `in` operator has a limit of 30 items in an array per query.
        #     # If tenant_ids can exceed this, batching will be needed.
        #     # For now, assuming tenant_ids will be reasonably small for a single OAuth callback.
        #     if tenant_ids: # Ensure tenant_ids is not empty before using 'in' filter
        #          entities_query = entities_query.where("entity_id", "in", tenant_ids)
        #     entities_docs = entities_query.stream()
            
        #     async for entity_doc in entities_docs:
        #         entity_data = entity_doc.to_dict()
        #         entity_id = entity_doc.id
        #         connected_entities[entity_id] = True
        
        # Format organizations for selection using the stored data
        available_organizations = []
        for connection in stored_connections: # This loop won't run if stored_connections is empty
            xero_tenant_id = connection.get("tenant_id")
            xero_tenant_name = connection.get("tenant_name")
            
            if not xero_tenant_id:
                continue
            
            # is_already_connected should be false here if stored_connections is correctly populated
            # by handle_smart_oauth_callback to only include NEW, UNCONNECTED orgs for this client.
            is_already_connected = connected_entities.get(xero_tenant_id, False) 
            
            available_organizations.append({
                "tenant_id": xero_tenant_id,
                "tenant_name": xero_tenant_name or "Unnamed Organization",
                "is_already_connected": is_already_connected, # This should ideally always be false
                "connection_type": connection.get("connection_type", "ORGANISATION")
            })
        
        reconnections_processed = temp_data.get("reconnections_processed", 0)
        
        return {
            "client_id": client_id,
            "organizations": available_organizations, # This will be an empty list if no new orgs
            "reconnections_processed": reconnections_processed,
            "message": f"Automatically reconnected {reconnections_processed} organization(s). Please select from the remaining new organizations below." if reconnections_processed > 0 and available_organizations else (
                f"Successfully reconnected {reconnections_processed} organization(s). No new organizations to select." if reconnections_processed > 0 else (
                    "No new organizations available to connect." if not available_organizations else None
                )
            )
        }
        
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to get available organizations for client {client_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get available organizations"
        )


@router.post("/clients/{client_id}/xero/connect-organization")
async def connect_selected_organization(
    client_id: str = Path(...),
    request_data: Dict[str, str] = Body(...),
    current_user: AuthUser = Depends(get_current_user),
    xero_service: XeroService = Depends(get_xero_service)
):
    """Connect to a selected Xero organization"""
    try:
        # Check client access manually
        await get_firm_user_with_client_access(client_id, current_user)
        
        selected_tenant_id = request_data.get("tenant_id")
        if not selected_tenant_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="tenant_id is required"
            )
        
        result = await xero_service.connect_selected_xero_organization(
            client_id, selected_tenant_id, current_user
        )
        
        return result
        
    except ValueError as e:
        if "expired" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_410_GONE,
                detail=str(e)
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )
    except Exception as e:
        logger.error(f"Failed to connect selected organization for client {client_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to connect to selected organization"
        ) 