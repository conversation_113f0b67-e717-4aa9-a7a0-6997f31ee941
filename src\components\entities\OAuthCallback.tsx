import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';

import { 
  CheckCircle, 
  XCircle, 
  Loader2, 
  AlertCircle, 
  ArrowLeft,
  RefreshCw,
  ExternalLink
} from 'lucide-react';

import { EntitiesService } from '@/services/entities.service';
import type { OAuthCallbackData, CompleteConnectionResponse } from '@/types/entity.types';

interface OAuthCallbackProps {
  entityId?: string;
  onSuccess: (entityId: string, response: CompleteConnectionResponse) => void;
  onError: (error: string) => void;
  onCancel: () => void;
}

type CallbackState = 'processing' | 'success' | 'error' | 'cancelled';

export function OAuthCallback({ entityId, onSuccess, onError, onCancel }: OAuthCallbackProps) {
  // State
  const [state, setState] = useState<CallbackState>('processing');
  const [progress, setProgress] = useState(0);
  const [message, setMessage] = useState('Processing OAuth callback...');
  const [error, setError] = useState<string | null>(null);
  const [response, setResponse] = useState<CompleteConnectionResponse | null>(null);

  // Hooks
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  // Process OAuth callback
  useEffect(() => {
    const processCallback = async () => {
      try {
        // Extract callback parameters
        const callbackData = extractCallbackData(searchParams);
        
        // Validate callback data
        if (!callbackData) {
          setState('error');
          setError('Invalid OAuth callback parameters');
          return;
        }

        // Check for OAuth errors
        if (callbackData.error) {
          setState('cancelled');
          setMessage(getErrorMessage(callbackData.error, callbackData.error_description));
          return;
        }

        // Validate required parameters
        if (!callbackData.code || !callbackData.state) {
          setState('error');
          setError('Missing required OAuth parameters (code or state)');
          return;
        }

        // Validate entity ID
        if (!entityId) {
          setState('error');
          setError('Entity ID not provided');
          return;
        }

        // Update progress
        setProgress(25);
        setMessage('Validating authorization code...');

        // Complete OAuth connection
        setProgress(50);
        setMessage('Completing connection setup...');

        const connectionResponse = await EntitiesService.completeOAuthConnection(entityId, callbackData);

        setProgress(75);
        setMessage('Finalizing connection...');

        // Simulate final processing
        await new Promise(resolve => setTimeout(resolve, 1000));

        setProgress(100);
        setMessage('Connection completed successfully!');
        setResponse(connectionResponse);
        setState('success');

        // Call success handler after a brief delay
        setTimeout(() => {
          onSuccess(entityId, connectionResponse);
        }, 1500);

      } catch (err) {
        console.error('OAuth callback error:', err);
        setState('error');
        setError(err instanceof Error ? err.message : 'Failed to complete OAuth connection');
      }
    };

    processCallback();
  }, [searchParams, entityId, onSuccess]);

  // Extract callback data from URL parameters
  const extractCallbackData = (params: URLSearchParams): OAuthCallbackData | null => {
    const code = params.get('code');
    const state = params.get('state');
    const scope = params.get('scope');
    const error = params.get('error');
    const errorDescription = params.get('error_description');

    // Return null if no relevant parameters found
    if (!code && !error) {
      return null;
    }

    return {
      code: code || '',
      state: state || '',
      scope: scope || undefined,
      error: error || undefined,
      error_description: errorDescription || undefined,
    };
  };

  // Get user-friendly error message
  const getErrorMessage = (error: string, description?: string): string => {
    const errorMessages: Record<string, string> = {
      'access_denied': 'You cancelled the authorization process',
      'invalid_request': 'Invalid authorization request',
      'unauthorized_client': 'Application not authorized',
      'unsupported_response_type': 'Unsupported response type',
      'invalid_scope': 'Invalid authorization scope',
      'server_error': 'Authorization server error',
      'temporarily_unavailable': 'Authorization server temporarily unavailable',
    };

    const userMessage = errorMessages[error] || 'Authorization failed';
    return description ? `${userMessage}: ${description}` : userMessage;
  };

  // Event handlers
  const handleRetry = () => {
    window.location.reload();
  };

  const handleGoBack = () => {
    onCancel();
  };

  const handleViewEntity = () => {
    if (entityId && response) {
      onSuccess(entityId, response);
    }
  };

  // Render different states
  const renderProcessing = () => (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
          <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
        </div>
        <CardTitle>Connecting Your Account</CardTitle>
        <CardDescription>
          Please wait while we complete the connection setup
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Progress</span>
            <span>{progress}%</span>
          </div>
          <Progress value={progress} className="w-full" />
        </div>
        <p className="text-sm text-gray-600 text-center">{message}</p>
      </CardContent>
    </Card>
  );

  const renderSuccess = () => (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
          <CheckCircle className="h-6 w-6 text-green-600" />
        </div>
        <CardTitle className="text-green-900">Connection Successful!</CardTitle>
        <CardDescription>
          Your accounting software has been connected successfully
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {response && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Status:</span>
              <Badge variant="default">
                <CheckCircle className="h-3 w-3 mr-1" />
                {response.connection_status}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Entity:</span>
              <span className="text-sm font-medium">{response.entity_id}</span>
            </div>
          </div>
        )}
        <div className="flex gap-2">
          <Button onClick={handleViewEntity} className="flex-1">
            <ExternalLink className="h-4 w-4 mr-2" />
            View Entity
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  const renderError = () => (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
          <XCircle className="h-6 w-6 text-red-600" />
        </div>
        <CardTitle className="text-red-900">Connection Failed</CardTitle>
        <CardDescription>
          We couldn't complete the connection to your accounting software
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleGoBack} className="flex-1">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Go Back
          </Button>
          <Button onClick={handleRetry} className="flex-1">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  const renderCancelled = () => (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-yellow-100">
          <AlertCircle className="h-6 w-6 text-yellow-600" />
        </div>
        <CardTitle className="text-yellow-900">Connection Cancelled</CardTitle>
        <CardDescription>
          The authorization process was cancelled
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-sm text-gray-600 text-center">{message}</p>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleGoBack} className="flex-1">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Go Back
          </Button>
          <Button onClick={handleRetry} className="flex-1">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {state === 'processing' && renderProcessing()}
        {state === 'success' && renderSuccess()}
        {state === 'error' && renderError()}
        {state === 'cancelled' && renderCancelled()}
      </div>
    </div>
  );
}

// Custom hook to detect OAuth callback
export function useOAuthCallback() {
  const [searchParams] = useSearchParams();
  
  const isOAuthCallback = React.useMemo(() => {
    const hasCode = searchParams.has('code');
    const hasState = searchParams.has('state');
    const hasError = searchParams.has('error');
    
    return hasCode || hasError || (hasState && (hasCode || hasError));
  }, [searchParams]);

  return { isOAuthCallback };
} 