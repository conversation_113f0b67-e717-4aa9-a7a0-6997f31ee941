import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../store/auth.store';
import { AppSidebar } from '../components/layout/AppSidebar';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '../components/ui/breadcrumb';
import { Separator } from '../components/ui/separator';
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from '../components/ui/sidebar';
import { FirmClientsOverviewDashboard } from '../features/dashboard/components/FirmClientsOverviewDashboard';

export function DashboardPage() {
  const navigate = useNavigate();
  const { user } = useAuthStore();

  // Don't render dashboard if user is not loaded or doesn't have firm info
  if (!user || !user.firmId) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset className="flex-1 overflow-hidden">
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbPage>DRCR</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </header>
        <div className="flex-1 overflow-auto">
          <FirmClientsOverviewDashboard
            firmId={user.firmId}
            firmName="DRCR Accounting"
            isAdmin={user.role === 'firm_admin'}
          />
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
