# 🚀 Quick Deploy Reference - DRCR Frontend

## **One-Time Setup** (5 minutes)

### 1. Get Firebase Token
```bash
npm install -g firebase-tools
firebase login:ci
# Copy the token
```

### 2. Add to GitLab Variables
- Go to: **Project Settings** → **CI/CD** → **Variables**
- Add: `FIREBASE_TOKEN` = `your-token-here` (Protected ✅, Masked ✅)

### 3. Push `.gitlab-ci.yml` to your repo
```bash
git add .gitlab-ci.yml GITLAB_CICD_SETUP.md
git commit -m "Add GitLab CI/CD pipeline"
git push
```

## **Daily Workflow**

### 🔧 **Development**
```bash
# 1. Create feature branch
git checkout -b feature/my-awesome-feature

# 2. Make changes to optimized components
# Edit src/features/dashboard/components/...

# 3. Push and create MR
git push origin feature/my-awesome-feature
# Create merge request in GitLab UI

# 4. Deploy preview (manual trigger in GitLab)
# Preview URL: https://drcr-d660a--preview-[MR-ID].web.app
```

### 🧪 **Staging**
```bash
# 1. Merge to develop
git checkout develop
git merge feature/my-awesome-feature
git push origin develop

# 2. Deploy staging (manual trigger in GitLab)
# Staging URL: https://drcr-d660a.web.app
```

### 🚀 **Production**
```bash
# 1. Merge to main
git checkout main
git merge develop
git push origin main

# 2. Deploy production (manual trigger in GitLab)
# Production URL: https://drcr-d660a.web.app
```

## **Pipeline Status**

| Branch | Build | Test | Deploy | URL |
|--------|-------|------|--------|-----|
| `feature/*` | ✅ Auto | ✅ Auto | 🔘 Manual (Preview) | `preview-[MR-ID].web.app` |
| `develop` | ✅ Auto | ✅ Auto | 🔘 Manual (Staging) | `drcr-d660a.web.app` |
| `main` | ✅ Auto | ✅ Auto | 🔘 Manual (Production) | `drcr-d660a.web.app` |

## **Quick Commands**

### Local Testing
```bash
# Build optimized bundle
npm run build

# Run linting
npm run lint

# Test deployment script
.\deploy.ps1
```

### GitLab Pipeline
```bash
# View pipelines
# GitLab → CI/CD → Pipelines

# Download build artifacts
# Click pipeline → Download artifacts

# Trigger manual deployment
# Click pipeline → Deploy stage → Play button
```

## **Bundle Analysis**

Your optimized build will show:
```
📊 Bundle analysis:
  react-vendor-[hash].js: ~326 KB
  firebase-[hash].js: ~164 KB  
  index-[hash].js: ~124 KB
  data-[hash].js: ~35 KB
```

## **Troubleshooting**

| Issue | Solution |
|-------|----------|
| Build fails | Check `npm run build` locally |
| Deploy fails | Verify `FIREBASE_TOKEN` in GitLab variables |
| Tests fail | Run `npm run lint` locally |
| Slow builds | Check if caching is working |

## **Performance Monitoring**

After deployment, check:
- ✅ Bundle sizes in pipeline logs
- ✅ Loading speed at deployed URL
- ✅ No console errors
- ✅ All routes working (SPA routing)

---

**🎯 Result:** Your optimized DRCR frontend automatically deployed with proper code splitting and performance optimizations! 