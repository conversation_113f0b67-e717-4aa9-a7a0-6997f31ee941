import os
import logging
from google.cloud import firestore
from fastapi import HTT<PERSON>Ex<PERSON>, Request, Depends
from typing import Optional, AsyncGenerator

logger = logging.getLogger(__name__)

async def get_firestore_db(request: Request) -> AsyncGenerator[firestore.AsyncClient, None]:
    """Firestore database dependency"""
    db_client_local_var_id_at_creation = str(id(None)) # For logging in case of issues
    
    gcp_project_id = os.getenv("GCP_PROJECT_ID")
    if not gcp_project_id:
        logger.error("GCP_PROJECT_ID is not set. Firestore client cannot be initialized.")
        raise HTTPException(status_code=500, detail="Firestore configuration error: GCP_PROJECT_ID missing.")
    
    local_db_client: Optional[firestore.AsyncClient] = None

    try:
        local_db_client = firestore.AsyncClient(project=gcp_project_id)
        db_client_local_var_id_at_creation = str(id(local_db_client))
        
        request.state.firestore_client = local_db_client
        
        logger.info(f"get_firestore_db (request {id(request)}): Firestore client initialized. ID: {db_client_local_var_id_at_creation}")
        yield local_db_client
    
    except Exception as e:
        logger.error(f"get_firestore_db (request {id(request)}): Failed during Firestore client operation: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Could not connect to database: {str(e)}")
    
    finally:
        client_from_state_for_closing: Optional[firestore.AsyncClient] = None
        client_id_for_logs = "Unknown_or_None"

        if hasattr(request.state, "firestore_client"):
            client_from_state_for_closing = request.state.firestore_client 
            if client_from_state_for_closing is not None:
                client_id_for_logs = str(id(client_from_state_for_closing))

            if client_from_state_for_closing is not None: 
                try:                    
                    close_method_return_value = client_from_state_for_closing.close()

                    if close_method_return_value is not None:
                        await close_method_return_value
                        logger.info(f"get_firestore_db (request {id(request)}): Firestore client (ID: {client_id_for_logs}) awaited close() successfully.")
                    else:
                        logger.warning(f"get_firestore_db (request {id(request)}): Firestore AsyncClient .close() method returned None. Not awaiting.")
                    
                except Exception as e_close:
                    logger.error(f"get_firestore_db (request {id(request)}): Error during close operation for client (ID: {client_id_for_logs}): {e_close}", exc_info=True)
                finally:
                    if hasattr(request.state, "firestore_client"): 
                        delattr(request.state, "firestore_client")

def get_db():
    """Simplified DB getter - this is the dependency to use in route handlers"""
    return get_firestore_db