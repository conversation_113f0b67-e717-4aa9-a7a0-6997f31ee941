# GitLab CI/CD Setup Guide for DRCR Frontend

## 🚀 **Overview**

This guide sets up automated deployment for your optimized DRCR frontend using GitLab CI/CD and Firebase Hosting.

## 📋 **Prerequisites**

### 1. **Firebase Setup**
- Firebase project: `drcr-d660a` (already configured)
- Firebase CLI access token (see setup below)

### 2. **GitLab Project Setup**
- GitLab repository for your frontend code
- GitLab CI/CD enabled

## 🔧 **Setup Steps**

### **Step 1: Get Firebase CI Token**

Run this locally to get your Firebase token:

```bash
# Install Firebase CLI (if not already installed)
npm install -g firebase-tools

# Login and get CI token
firebase login:ci
```

Copy the token that's generated - you'll need it for GitLab.

### **Step 2: Configure GitLab CI/CD Variables**

In your GitLab project:

1. Go to **Settings** → **CI/CD** → **Variables**
2. Add these variables:

| Variable Name | Value | Protected | Masked |
|---------------|-------|-----------|---------|
| `FIREBASE_TOKEN` | Your Firebase CI token | ✅ | ✅ |

### **Step 3: Branch Strategy**

The pipeline is configured for this branch strategy:

- **`main`** → Production deployments (manual trigger)
- **`develop`** → Staging deployments (manual trigger)  
- **Merge Requests** → Preview deployments (manual trigger)

## 🔄 **Pipeline Stages**

### **1. Install Dependencies**
- Caches `node_modules` for faster builds
- Uses `npm ci` for consistent installs
- Runs on: `main`, `develop`, merge requests

### **2. Test & Lint**
- Runs ESLint checks
- Ready for Vitest tests (commented out until tests are written)
- Generates coverage reports
- Runs on: `main`, `develop`, merge requests

### **3. Build Production**
- Creates optimized production bundle
- Shows bundle analysis (file sizes)
- Artifacts stored for 1 week
- Runs on: `main`, `develop`, merge requests

### **4. Deploy**

#### **Production Deployment** (`main` branch)
- **Trigger:** Manual
- **URL:** https://drcr-d660a.web.app
- **When:** Push to `main` branch

#### **Staging Deployment** (`develop` branch)
- **Trigger:** Manual  
- **URL:** https://drcr-d660a.web.app (staging channel)
- **When:** Push to `develop` branch

#### **Preview Deployment** (Merge Requests)
- **Trigger:** Manual
- **URL:** https://drcr-d660a--preview-[MR-ID].web.app
- **When:** Open merge request
- **Cleanup:** Automatic when MR is closed

## 🎯 **Usage Workflow**

### **For Development:**
1. Create feature branch from `develop`
2. Make changes to your optimized components
3. Push and create merge request
4. **Manual trigger** → Preview deployment created
5. Review changes on preview URL
6. Merge to `develop` when ready

### **For Staging:**
1. Merge feature to `develop` branch
2. Pipeline runs automatically (build + test)
3. **Manual trigger** → Deploy to staging
4. Test on staging environment

### **For Production:**
1. Merge `develop` to `main` branch
2. Pipeline runs automatically (build + test)
3. **Manual trigger** → Deploy to production
4. Live on https://drcr-d660a.web.app

## 📊 **Pipeline Benefits**

### **Performance Optimizations:**
- ✅ Caching for faster builds (~2-3 min vs 5-7 min)
- ✅ Parallel job execution
- ✅ Artifact reuse between stages
- ✅ Alpine Linux images (smaller, faster)

### **Quality Assurance:**
- ✅ Automated linting on every commit
- ✅ Bundle size analysis
- ✅ Preview deployments for testing
- ✅ Manual deployment gates

### **Deployment Features:**
- ✅ Multi-environment support
- ✅ Preview URLs for merge requests
- ✅ Automatic cleanup of preview deployments
- ✅ Environment tracking in GitLab

## 🔍 **Monitoring & Debugging**

### **View Pipeline Status:**
- GitLab Project → **CI/CD** → **Pipelines**
- Click on pipeline to see job details
- Download artifacts (build files, coverage reports)

### **Common Issues:**

#### **Build Fails:**
```bash
# Check the build job logs
# Usually TypeScript or linting errors
npm run build  # Test locally first
```

#### **Deployment Fails:**
```bash
# Check Firebase token is valid
firebase login:ci  # Get new token
# Update FIREBASE_TOKEN variable in GitLab
```

#### **Tests Fail:**
```bash
# When tests are enabled
npm run test:run  # Test locally first
npm run lint      # Check linting
```

## 🚀 **Advanced Configuration**

### **Enable Automatic Deployments:**
Change `when: manual` to `when: on_success` in `.gitlab-ci.yml`

### **Add More Environments:**
```yaml
deploy_uat:
  stage: deploy
  # ... similar to staging but different project/channel
```

### **Add Slack/Teams Notifications:**
```yaml
notify_deployment:
  stage: deploy
  script:
    - curl -X POST -H 'Content-type: application/json' --data '{"text":"🚀 DRCR Frontend deployed!"}' $SLACK_WEBHOOK
```

## 📈 **Expected Results**

After setup, you'll have:

- ✅ **Automated builds** on every commit
- ✅ **Quality gates** (linting, tests)
- ✅ **Preview deployments** for code review
- ✅ **Staging environment** for testing
- ✅ **Production deployments** with manual approval
- ✅ **Bundle optimization** showcased in every build

Your optimized React app will be automatically built and deployed with proper code splitting and performance optimizations! 🎉

## 🆘 **Support**

If you encounter issues:
1. Check GitLab pipeline logs
2. Verify Firebase token is valid
3. Test build locally: `npm run build`
4. Check Firebase project permissions 