import os
import logging
from google.cloud import firestore
from fastapi import HTTPEx<PERSON>, Request, Depends
from typing import Optional, AsyncGenerator

# For Firebase Auth
from firebase_admin import auth, credentials
import firebase_admin

logger = logging.getLogger(__name__)

# Global database client for connection pooling
_db_client: Optional[firestore.AsyncClient] = None

def get_global_db_client() -> firestore.AsyncClient:
    """Get or create a global database client for connection pooling"""
    global _db_client
    
    if _db_client is None:
        gcp_project_id = os.getenv("GCP_PROJECT_ID") or os.getenv("GOOGLE_CLOUD_PROJECT")
        if not gcp_project_id:
            logger.error("GCP_PROJECT_ID is not set. Firestore client cannot be initialized.")
            raise HTTPException(status_code=500, detail="Firestore configuration error: GCP_PROJECT_ID missing.")
        
        try:
            _db_client = firestore.AsyncClient(project=gcp_project_id)
            logger.info(f"Global Firestore client initialized for project: {gcp_project_id}")
        except Exception as e:
            logger.error(f"Failed to initialize global Firestore client: {e}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"Could not connect to database: {str(e)}")
    
    return _db_client

async def get_firestore_db(request: Request) -> AsyncGenerator[firestore.AsyncClient, None]:
    """Fast database dependency using global client"""
    try:
        # Use the global client instead of creating new ones
        db_client = get_global_db_client()
        yield db_client
    except Exception as e:
        logger.error(f"Database connection error: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Database connection failed: {str(e)}")

# Alias for backward compatibility
get_db = get_firestore_db
