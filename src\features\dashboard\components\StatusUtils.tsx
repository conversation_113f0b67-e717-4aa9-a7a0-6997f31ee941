import React from 'react';
import { Badge } from '@/components/ui/badge';
import { 
  <PERSON>Circle, 
  Bell, 
  AlertTriangle, 
  Unlink, 
  Loader2 
} from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import type { ClientSummary, EntitySummary, StatusInfo } from '../types';

export const getClientStatusInfo = (status: ClientSummary['overall_status']): StatusInfo => {
  switch (status) {
    case 'ok': 
      return { 
        badgeClass: 'bg-green-100 text-green-700 border border-green-200', 
        icon: <CheckCircle className="h-3 w-3" />, 
        text: 'OK' 
      };
    case 'action_needed': 
      return { 
        badgeClass: 'bg-yellow-50 text-yellow-800 border border-yellow-300', 
        icon: <Bell className="h-3 w-3" />, 
        text: 'Action Needed' 
      };
    case 'error': 
      return { 
        badgeClass: 'bg-red-100 text-red-700 border border-red-200', 
        icon: <AlertTriangle className="h-3 w-3" />, 
        text: 'Error' 
      };
    default: 
      return { 
        badgeClass: 'bg-gray-100 text-gray-700 border border-gray-200', 
        icon: null, 
        text: 'Unknown' 
      };
  }
};

export const getEntityStatusInfo = (entity: EntitySummary): StatusInfo => {
  const status = entity.connection_status;
  const tooltip = entity.error_message;
  
  switch (status) {
    case 'active': 
      return { 
        badgeClass: 'bg-green-100 text-green-700 border border-green-200', 
        icon: <CheckCircle className="h-3 w-3" />, 
        text: 'Active', 
        tooltip: null 
      };
    case 'error': 
      return { 
        badgeClass: 'bg-red-100 text-red-700 border border-red-200', 
        icon: <AlertTriangle className="h-3 w-3" />, 
        text: 'Error', 
        tooltip: tooltip 
      };
    case 'disconnected': 
      return { 
        badgeClass: 'bg-gray-100 text-gray-500 border border-gray-200', 
        icon: <Unlink className="h-3 w-3" />, 
        text: 'Disconnected', 
        tooltip: tooltip 
      };
    case 'syncing': 
      return { 
        badgeClass: 'bg-blue-100 text-blue-700 border border-blue-200', 
        icon: <Loader2 className="h-3 w-3 animate-spin" />, 
        text: 'Syncing', 
        tooltip: null 
      };
    default: 
      return { 
        badgeClass: 'bg-gray-100 text-gray-700 border border-gray-200', 
        icon: null, 
        text: 'Unknown', 
        tooltip: null 
      };
  }
};

interface StatusBadgeProps {
  statusInfo: StatusInfo;
  tooltipContent?: React.ReactNode;
}

export function StatusBadge({ statusInfo, tooltipContent }: StatusBadgeProps) {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <span>
          <Badge variant="outline" className={statusInfo.badgeClass}>
            {statusInfo.icon}
            <span className="ml-1">{statusInfo.text}</span>
          </Badge>
        </span>
      </TooltipTrigger>
      {(statusInfo.tooltip || tooltipContent) && (
        <TooltipContent>
          <p>{statusInfo.tooltip || tooltipContent}</p>
        </TooltipContent>
      )}
    </Tooltip>
  );
}

interface ClientDetailsProps {
  client: ClientSummary;
}

export function ClientDetails({ client }: ClientDetailsProps) {
  const details: React.ReactNode[] = [];
  
  // Tooltip for Pending items
  if ((client.pending_items_count || 0) > 0) {
    details.push(
      <TooltipProvider key="pending-tp" delayDuration={100}>
        <Tooltip>
          <TooltipTrigger asChild>
            <span className="text-yellow-700 flex items-center text-xs mr-2 cursor-default">
              <Bell className="h-3 w-3 mr-1" /> {client.pending_items_count} pending
            </span>
          </TooltipTrigger>
          <TooltipContent>
            <p>{client.pending_items_count} prepayment schedule(s) require review across all entities.</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }
  
  // Tooltip for Error count
  if ((client.error_count || 0) > 0) {
    const errorEntities = client.entities
      .filter(e => e.connection_status === 'error' || e.connection_status === 'disconnected')
      .map(e => e.entity_name)
      .join(', ');
    const errorTooltip = `${client.error_count} ${client.error_count === 1 ? 'entity' : 'entities'} with connection issues: ${errorEntities || 'Details unavailable'}.`;
    
    details.push(
      <TooltipProvider key="error-tp" delayDuration={100}>
        <Tooltip>
          <TooltipTrigger asChild>
            <span className="text-red-600 flex items-center text-xs mr-2 cursor-default">
              <AlertTriangle className="h-3 w-3 mr-1" /> {client.error_count} errors
            </span>
          </TooltipTrigger>
          <TooltipContent>
            <p>{errorTooltip}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }
  
  if (details.length === 0 && client.overall_status === 'ok') {
    details.push(
      <span key="ok" className="text-gray-500 flex items-center text-xs mr-2"> - </span>
    );
  }
  
  return <div className="flex flex-wrap items-center gap-1">{details}</div>;
} 