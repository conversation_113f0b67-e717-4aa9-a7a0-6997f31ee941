#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create required Firestore indexes for DRCR prepayment testing.
This will create the composite indexes needed for the queries in our application.
"""

import os
import sys
from dotenv import load_dotenv
load_dotenv()

# Add project root to path
project_root = os.path.abspath(os.path.dirname(__file__))
sys.path.insert(0, project_root)

from google.cloud import firestore

def main():
    project_id = os.getenv('GCP_PROJECT_ID', 'drcr-d660a')
    print(f'🔧 Creating Firestore indexes for project: {project_id}')
    
    # Note: The Python client doesn't directly support creating indexes
    # We'll provide the gcloud commands instead
    
    print('\n📋 Required Firestore Indexes')
    print('=' * 50)
    
    indexes = [
        {
            'collection': 'TRANSACTIONS',
            'fields': 'entityId,dateUtc desc',
            'description': 'For querying transactions by entity and date'
        },
        {
            'collection': 'AMORTIZATION_SCHEDULES', 
            'fields': 'entityId,createdAt desc',
            'description': 'For querying schedules by entity and creation date'
        },
        {
            'collection': 'PROPOSED_JOURNALS',
            'fields': 'entityId,createdAt desc', 
            'description': 'For querying journals by entity and creation date'
        },
        {
            'collection': 'AUDIT_LOG',
            'fields': 'entityId,timestamp desc',
            'description': 'For querying audit logs by entity and timestamp'
        }
    ]
    
    print('\n🚀 Run these gcloud commands to create the indexes:')
    print('-' * 60)
    
    for i, index in enumerate(indexes, 1):
        print(f'\n{i}. {index["description"]}:')
        print(f'gcloud firestore indexes composite create \\')
        print(f'  --collection-group={index["collection"]} \\')
        print(f'  --field-config=field-path=entityId,order=ascending \\')
        
        # Parse the second field
        field_parts = index['fields'].split(',')[1].strip().split()
        field_name = field_parts[0]
        field_order = 'descending' if 'desc' in field_parts else 'ascending'
        
        print(f'  --field-config=field-path={field_name},order={field_order} \\')
        print(f'  --project={project_id}')
    
    print('\n' + '=' * 60)
    print('📝 Alternative: Use Firebase Console URLs')
    print('=' * 60)
    
    console_urls = [
        'https://console.firebase.google.com/v1/r/project/drcr-d660a/firestore/indexes?create_composite=Ck9wcm9qZWN0cy9kcmNyLWQ2NjBhL2RhdGFiYXNlcy8oZGVmYXVsdCkvY29sbGVjdGlvbkdyb3Vwcy9UUkFOU0FDVElPTlMvaW5kZXhlcy9fEAEaDAoIZW50aXR5SWQQAR oLCgdkYXRlVXRjEAIaDAoIX19uYW1lX18QAg',
        'https://console.firebase.google.com/v1/r/project/drcr-d660a/firestore/indexes?create_composite=Cllwcm9qZWN0cy9kcmNyLWQ2NjBhL2RhdGFiYXNlcy8oZGVmYXVsdCkvY29sbGVjdGlvbkdyb3Vwcy9BTU9SVElaQVRJT05fU0NIRURVTEVTL2luZGV4ZXMv XxABGgwKCGVudGl0eUlkEAEaDQoJY3JlYXRlZEF0EAIaDAoIX19uYW1lX18QAg',
        'https://console.firebase.google.com/v1/r/project/drcr-d660a/firestore/indexes?create_composite=ClRwcm9qZWN0cy9kcmNyLWQ2NjBhL2RhdGFiYXNlcy8oZGVmYXVsdCkvY29sbGVjdGlvbkdyb3Vwcy9QUk9QT1NFRF9KT1VSTkFMUy9pbmRleGVzL18QARoMCghlb nRpdHlJZBABGg0KCWNyZWF0ZWRBdBACGgwKCF9fbmFtZV9fEAI',
        'https://console.firebase.google.com/v1/r/project/drcr-d660a/firestore/indexes?create_composite=Ckxwcm9qZWN0cy9kcmNyLWQ2NjBhL2RhdGFiYXNlcy8oZGVmYXVsdCkvY29sbGVjdGlvbkdyb3Vwcy9BVURJVF9MT0cvaW5kZXhlcy9fEAEaDAoIZW50aXR5SWQQARoNCgl0 aW1lc3RhbXAQAhoMCghfX25hbWVfXxAC'
    ]
    
    for i, (index, url) in enumerate(zip(indexes, console_urls), 1):
        print(f'\n{i}. {index["collection"]}: {url}')
    
    print('\n💡 Tips:')
    print('• Indexes take a few minutes to build')
    print('• You can check status in Firebase Console > Firestore > Indexes')
    print('• Once built, re-run: python check_prepayment_data.py')
    
    print('\n🎯 After creating indexes, you can run:')
    print('   python sync_invoices_for_prepayment_testing.py')

if __name__ == "__main__":
    main() 