import { api } from '@/lib/api';
import type { ClientSummary } from '@/lib/api';

export interface FirmDashboardFilters {
  page?: number;
  limit?: number;
  clientFilter?: string;
  statusFilter?: string;
}

export interface FirmDashboardResponse {
  clients: ClientSummary[];
  pagination: {
    current_page: number;
    page_size: number;
    total_items: number;
    total_pages: number;
  };
}

export class DashboardService {
  /**
   * Fetch firm dashboard data with clients summary
   */
  static async getFirmDashboard(filters?: FirmDashboardFilters): Promise<FirmDashboardResponse> {
    try {
      const apiFilters = {
        page: filters?.page,
        limit: filters?.limit,
        client_filter: filters?.clientFilter,
        status_filter: filters?.statusFilter,
      };

      const response = await api.getClientsSummary(apiFilters);

      // Return the response directly since the types now match
      return {
        clients: response.clients,
        pagination: response.pagination,
      };
    } catch (error) {
      console.error('Error fetching firm dashboard:', error);
      throw new Error('Failed to fetch firm dashboard data');
    }
  }

  /**
   * Get client dashboard data
   */
  static async getClientDashboard(clientId: string, entityId?: string) {
    try {
      return await api.getClientDashboard(clientId, entityId);
    } catch (error) {
      console.error('Error fetching client dashboard:', error);
      throw new Error('Failed to fetch client dashboard data');
    }
  }
}
