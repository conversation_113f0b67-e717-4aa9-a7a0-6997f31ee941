from pydantic_settings import BaseSettings
import os

class Settings(BaseSettings):
    APP_NAME: str = "DRCR FastAPI App"
    DEBUG_MODE: bool = False
    
    # For JWT authentication in routes/auth.py
    SECRET_KEY: str = os.getenv("SECRET_KEY", "a-very-secret-key-that-should-be-in-env-or-secrets-manager")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # Example of explicitly defined settings that might come from your .env
    GCP_PROJECT_ID: str | None = None 
    XERO_CLIENT_ID: str | None = None
    XERO_CLIENT_SECRET: str | None = None
    XERO_REDIRECT_URI_FASTAPI: str | None = None # if used by FastAPI app directly
    XERO_SCOPES: str | None = None

    # You can continue to add other critical settings explicitly here
    # For any other variables in your .env that you don't explicitly define here,
    # the `extra = 'ignore'` setting below will prevent Pydantic from erroring.

    class Config:
        env_file = ".env"
        env_file_encoding = 'utf-8'
        extra = 'ignore' # Allow and ignore extra fields from .env

settings = Settings()
