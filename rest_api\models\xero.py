from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime

class XeroAuthRequest(BaseModel):
    """Request model for Xero authentication."""
    redirect_uri: Optional[str] = None

class XeroAuthResponse(BaseModel):
    """Response model for Xero authentication."""
    auth_url: str

class XeroTenant(BaseModel):
    """Model for a Xero tenant (organization)."""
    id: str
    name: str
    tenant_type: str
    created_date_utc: Optional[datetime] = None
    class Config:
        from_attributes = True

class XeroTokens(BaseModel):
    """Model for Xero OAuth tokens."""
    access_token: str
    refresh_token: str
    id_token: Optional[str] = None
    expires_at: datetime
    class Config:
        from_attributes = True
