import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  ChevronDown,
  ChevronRight,
  ExternalLink,
  CircleSlash,
  PlusCircle,
  Unlink,
  Wrench,
  Link as LinkIcon,
  Settings,
  Loader2,
} from 'lucide-react';

import { getClientStatusInfo, getEntityStatusInfo, StatusBadge, ClientDetails } from './StatusUtils';
import type { DashboardState, DashboardActions } from '../types';
import { XeroOperationStatus } from '@/components/ui/xero-operation-status';
import { SyncStatusDisplay } from '@/components/ui/sync-status-display';

interface ClientsTableProps extends DashboardState, DashboardActions {}

export function ClientsTable({
  clientSummaries,
  isLoading,
  error,
  expandedClients,
  handleClientClick,
  handleDisconnectEntity,
  handleFixConnection,
  handleConnectNewEntity,
  handleEntitySettings,
  toggleClientExpansion,
  handleClientSettings,
  operatingEntityId,
  connectState,
  disconnectState,
}: ClientsTableProps) {
  return (
    <div className="border rounded-lg overflow-auto bg-white shadow-sm flex-grow">
      <Table>
        <TableHeader className="sticky top-0 bg-gray-50 z-10">
          <TableRow className="border-b">
            <TableHead className="w-[40%] py-4 px-6 font-semibold">Client / Group</TableHead>
            <TableHead className="w-[150px] py-4 px-4 font-semibold">Overall Status</TableHead>
            <TableHead className="py-4 px-4 font-semibold">Details</TableHead>
            <TableHead className="text-right w-[150px] py-4 px-6 font-semibold">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {isLoading && (
            // Loading Skeleton Rows
            [...Array(5)].map((_, i) => (
              <TableRow key={`skel-${i}`} className="border-b">
                <TableCell className="py-4 px-6"><Skeleton className="h-5 w-3/4" /></TableCell>
                <TableCell className="py-4 px-4"><Skeleton className="h-5 w-20" /></TableCell>
                <TableCell className="py-4 px-4"><Skeleton className="h-5 w-1/2" /></TableCell>
                <TableCell className="text-right py-4 px-6"><Skeleton className="h-8 w-24 ml-auto" /></TableCell>
              </TableRow>
            ))
          )}
          {!isLoading && !error && clientSummaries.length === 0 && (
            <TableRow>
              <TableCell colSpan={4} className="h-32 text-center text-muted-foreground py-8">
                <CircleSlash className="mx-auto h-12 w-12 mb-4 text-gray-400"/>
                <p className="text-lg font-medium">No clients found matching criteria.</p>
              </TableCell>
            </TableRow>
          )}
          {!isLoading && !error && clientSummaries.map((client) => {
            const statusInfo = getClientStatusInfo(client.overall_status);
            const isExpanded = expandedClients[client.client_id];
            return (
              <React.Fragment key={client.client_id}>
                {/* Client Row */}
                <TableRow
                  className="hover:bg-muted/50 border-b border-gray-200 cursor-pointer transition-colors"
                  onClick={() => toggleClientExpansion(client.client_id)}
                >
                  <TableCell className="font-medium py-4 px-6">
                    <span className="inline-flex items-center">
                      {isExpanded ?
                        <ChevronDown className="h-4 w-4 mr-3 text-muted-foreground" /> :
                        <ChevronRight className="h-4 w-4 mr-3 text-muted-foreground" />
                      }
                      <span className="text-base">{client.name}</span>
                    </span>
                  </TableCell>
                  <TableCell className="py-4 px-4">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <span>
                          <StatusBadge
                            statusInfo={statusInfo}
                            tooltipContent={
                              client.overall_status === 'action_needed' ? 'Client has items requiring review.' :
                              client.overall_status === 'error' ? 'Client has connection or sync errors.' :
                              client.overall_status === 'ok' ? 'Client status is OK.' : undefined
                            }
                          />
                        </span>
                      </TooltipTrigger>
                    </Tooltip>
                  </TableCell>
                  <TableCell className="py-4 px-4">
                    <ClientDetails client={client} />
                  </TableCell>
                  <TableCell className="text-right py-4 px-6">
                    <div className="flex gap-2 justify-end">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleClientClick(client.client_id);
                        }}
                      >
                        View Client <ExternalLink className="ml-2 h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleClientSettings(client.client_id);
                        }}
                        title="Client Settings"
                      >
                        <Settings className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>

                {/* Entity Sub-Rows */}
                {isExpanded && client.entities.map((entity) => {
                  const entityStatusInfo = getEntityStatusInfo(entity);
                  const isOperating = operatingEntityId === entity.entity_id;
                  const showDisconnectStatus = isOperating && disconnectState.status !== 'idle';
                  const showConnectStatus = isOperating && connectState.status !== 'idle';

                  return (
                    <React.Fragment key={entity.entity_id}>
                      <TableRow className="bg-gray-50 hover:bg-gray-100 border-b border-gray-100">
                        <TableCell className="pl-16 pr-6 py-3 font-normal text-sm flex items-center">
                          <span className="text-gray-700">{entity.entity_name} ({entity.type})</span>
                        </TableCell>
                        <TableCell className="py-3 px-4">
                          <div className="flex items-center gap-2">
                            <StatusBadge statusInfo={entityStatusInfo} />
                            {entity.sync_status && (
                              <SyncStatusDisplay
                                entityId={entity.entity_id}
                                entityName={entity.entity_name}
                                syncStatus={entity.sync_status}
                                compact={true}
                                className="ml-2"
                              />
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="text-xs text-muted-foreground py-3 px-4">
                          <div className="space-y-1">
                            <div>
                              {entity.last_sync ? `Last Sync: ${new Date(entity.last_sync).toLocaleString()}` : 'Never synced'}
                            </div>
                            {entity.sync_status?.is_syncing && (
                              <div className="text-blue-600 font-medium">
                                {entity.sync_status.user_message}
                                {entity.sync_status.estimated_remaining && (
                                  <span className="text-xs text-muted-foreground ml-1">
                                    (~{entity.sync_status.estimated_remaining} remaining)
                                  </span>
                                )}
                              </div>
                            )}
                            {entity.sync_status?.sync_duration_warning && entity.sync_status.is_syncing && (
                              <div className="text-xs text-amber-600">
                                ⚠️ {entity.sync_status.sync_duration_warning}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="text-right space-x-1 py-3 px-6">
                          {entity.connection_status === 'error' && (
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleFixConnection(client.client_id, entity.entity_id);
                              }}
                              disabled={operatingEntityId === entity.entity_id && connectState.isLoading}
                            >
                              {operatingEntityId === entity.entity_id && connectState.isLoading ? (
                                <>
                                  <Loader2 className="mr-1 h-3 w-3 animate-spin" /> Fixing...
                                </>
                              ) : (
                                <>
                                  <Wrench className="mr-1 h-3 w-3" /> Fix
                                </>
                              )}
                            </Button>
                          )}
                          {entity.connection_status === 'active' && entity.type !== 'manual' && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDisconnectEntity(client.client_id, entity.entity_id);
                              }}
                              disabled={operatingEntityId === entity.entity_id && disconnectState.isLoading}
                            >
                              {operatingEntityId === entity.entity_id && disconnectState.isLoading ? (
                                <>
                                  <Loader2 className="mr-1 h-3 w-3 animate-spin" /> Disconnecting...
                                </>
                              ) : (
                                <>
                                  <Unlink className="mr-1 h-3 w-3" /> Disconnect
                                </>
                              )}
                            </Button>
                          )}
                          {entity.connection_status === 'disconnected' && (
                            <Button
                              variant="default"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleFixConnection(client.client_id, entity.entity_id);
                              }}
                              disabled={operatingEntityId === entity.entity_id && connectState.isLoading}
                            >
                              {operatingEntityId === entity.entity_id && connectState.isLoading ? (
                                <>
                                  <Loader2 className="mr-1 h-3 w-3 animate-spin" /> Reconnecting...
                                </>
                              ) : (
                                <>
                                  <LinkIcon className="mr-1 h-3 w-3" /> Reconnect
                                </>
                              )}
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEntitySettings(client.client_id, entity.entity_id);
                            }}
                          >
                            <Settings className="h-3 w-3" />
                          </Button>
                        </TableCell>
                      </TableRow>

                      {/* Inline Operation Status */}
                      {(showDisconnectStatus || showConnectStatus) && (
                        <TableRow className="bg-blue-50/50 border-b border-blue-200">
                          <TableCell colSpan={4} className="pl-16 pr-6 py-4">
                            {showDisconnectStatus && (
                              <XeroOperationStatus
                                operation="disconnect"
                                status={disconnectState.status}
                                variant="compact"
                                showProgress={true}
                                showElapsedTime={true}
                                timeoutSeconds={30}
                                elapsedSeconds={disconnectState.elapsedSeconds}
                                size="sm"
                                className="w-full"
                              />
                            )}
                            {showConnectStatus && (
                              <XeroOperationStatus
                                operation="reconnect"
                                status={connectState.status}
                                variant="compact"
                                showProgress={true}
                                showElapsedTime={true}
                                timeoutSeconds={45}
                                elapsedSeconds={connectState.elapsedSeconds}
                                size="sm"
                                className="w-full"
                              />
                            )}
                          </TableCell>
                        </TableRow>
                      )}
                    </React.Fragment>
                  );
                })}

                {/* Add Entity Row */}
                {isExpanded && (
                  <TableRow className="bg-gray-50 hover:bg-gray-100 border-b border-gray-100">
                    <TableCell colSpan={3} className="pl-16 pr-6 py-3">
                      <Button
                        variant="link"
                        size="sm"
                        className="text-blue-600 p-0 h-auto font-medium"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleConnectNewEntity(client.client_id);
                        }}
                      >
                        <PlusCircle className="mr-2 h-4 w-4"/> Connect New Entity...
                      </Button>
                    </TableCell>
                    <TableCell className="py-3 px-6"></TableCell>
                  </TableRow>
                )}
              </React.Fragment>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}