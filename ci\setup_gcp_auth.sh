#!/bin/sh
# Script for setting up GCP authentication

echo "Running setup_gcp_auth.sh"
echo "Current directory: $(pwd)"

echo "Checking GCP_SERVICE_KEY variable..."
if [ -z "$GCP_SERVICE_KEY" ]; then
  echo "GCP_SERVICE_KEY is empty or not set"
  exit 1
else
  echo "GCP_SERVICE_KEY is set (not showing value for security)"
fi

echo "Decoding service account key..."
echo $GCP_SERVICE_KEY | base64 -d > gcp-key.json
if [ ! -s gcp-key.json ]; then
  echo "Error: gcp-key.json is empty after decoding"
  exit 1
fi

echo "Activating service account..."
gcloud auth activate-service-account --key-file gcp-key.json
gcloud config set project drcr-d660a

# Add these lines to configure Docker with GCP
echo "Configuring Docker with GCP..."
gcloud auth configure-docker gcr.io,us-docker.pkg.dev

echo "GCP authentication setup completed"
