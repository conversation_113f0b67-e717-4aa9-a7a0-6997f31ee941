#!/usr/bin/env python3
"""
Deployment script for the scheduled sync infrastructure.

This script:
1. Creates zip files for the Cloud Functions
2. Applies Terraform configuration for Cloud Scheduler and functions
3. Verifies the deployment
"""

import os
import sys
import subprocess
import zipfile
import shutil
from pathlib import Path

def run_command(command, check=True, cwd=None):
    """Run a shell command and return the result."""
    print(f"Running: {command}")
    result = subprocess.run(
        command, 
        shell=True, 
        capture_output=True, 
        text=True, 
        check=check,
        cwd=cwd
    )
    
    if result.stdout:
        print(result.stdout)
    if result.stderr:
        print(result.stderr, file=sys.stderr)
    
    return result

def create_function_zip(function_dir, zip_path):
    """Create a zip file for a Cloud Function."""
    print(f"Creating zip file: {zip_path}")
    
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(function_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arcname = os.path.relpath(file_path, function_dir)
                zipf.write(file_path, arcname)
    
    print(f"Created {zip_path} ({os.path.getsize(zip_path)} bytes)")

def main():
    """Main deployment function."""
    # Get the project root directory
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    
    print("🚀 Deploying Scheduled Sync Infrastructure")
    print("=" * 50)
    
    # Step 1: Create Cloud Function zip files
    print("\n📦 Creating Cloud Function zip files...")
    
    # Create scheduled sync processor zip
    scheduled_sync_dir = project_root / "cloud_functions" / "scheduled_sync_processor"
    scheduled_sync_zip = project_root / "cloud_functions" / "scheduled_sync_processor.zip"
    
    if scheduled_sync_dir.exists():
        create_function_zip(scheduled_sync_dir, scheduled_sync_zip)
    else:
        print(f"❌ Directory not found: {scheduled_sync_dir}")
        return 1
    
    # Create xero sync consumer zip if it doesn't exist
    xero_sync_dir = project_root / "cloud_functions" / "xero_sync_consumer"
    xero_sync_zip = project_root / "cloud_functions" / "xero_sync_consumer.zip"
    
    if xero_sync_dir.exists() and not xero_sync_zip.exists():
        create_function_zip(xero_sync_dir, xero_sync_zip)
    
    # Step 2: Initialize and apply Terraform
    print("\n🏗️  Applying Terraform configuration...")
    
    terraform_dir = project_root / "terraform"
    
    # Initialize Terraform if needed
    if not (terraform_dir / ".terraform").exists():
        print("Initializing Terraform...")
        result = run_command("terraform init", cwd=terraform_dir)
        if result.returncode != 0:
            print("❌ Terraform init failed")
            return 1
    
    # Plan the deployment
    print("Planning Terraform deployment...")
    result = run_command("terraform plan", cwd=terraform_dir, check=False)
    if result.returncode != 0:
        print("❌ Terraform plan failed")
        return 1
    
    # Ask for confirmation
    response = input("\n🤔 Do you want to apply these changes? (y/N): ")
    if response.lower() != 'y':
        print("Deployment cancelled.")
        return 0
    
    # Apply the configuration
    print("Applying Terraform configuration...")
    result = run_command("terraform apply -auto-approve", cwd=terraform_dir)
    if result.returncode != 0:
        print("❌ Terraform apply failed")
        return 1
    
    # Step 3: Verify deployment
    print("\n✅ Verifying deployment...")
    
    # Check if Cloud Scheduler jobs are created
    print("Checking Cloud Scheduler jobs...")
    result = run_command("gcloud scheduler jobs list --format='table(name,schedule,state)'", check=False)
    
    # Check if Pub/Sub topics are created
    print("Checking Pub/Sub topics...")
    result = run_command("gcloud pubsub topics list --format='table(name)'", check=False)
    
    # Check if Cloud Functions are deployed
    print("Checking Cloud Functions...")
    result = run_command("gcloud functions list --format='table(name,status,trigger)'", check=False)
    
    print("\n🎉 Scheduled sync infrastructure deployment complete!")
    print("\nNext steps:")
    print("1. Verify that entities have sync_frequency configured")
    print("2. Monitor Cloud Function logs for scheduled sync execution")
    print("3. Check Firestore for sync timestamp updates")
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 