# Smart Password Reset Implementation

## Overview

This document describes the implementation of a smart password reset system for the DRCR application that automatically detects the frontend URL and generates appropriate reset links for different environments (localhost, staging, production).

## Features

### 🎯 Smart URL Detection
- **Automatic Environment Detection**: Detects whether the request comes from localhost, staging, or production
- **Dynamic Redirect Links**: Generates reset links that redirect users back to the correct frontend URL
- **Multi-Environment Support**: Works seamlessly across development, staging, and production environments

### 🔒 Security Features
- **Cryptographically Secure Tokens**: Uses `secrets.token_urlsafe(32)` for token generation
- **SHA-256 Token Hashing**: Tokens are hashed before storage in Firestore
- **24-Hour Expiration**: Reset tokens automatically expire after 24 hours
- **Rate Limiting**: Maximum 3 reset requests per hour per email address
- **Single-Use Tokens**: Tokens are invalidated after successful password reset
- **Security Logging**: All requests are logged with IP addresses and user agents

### 📧 Email Integration
- **SendGrid Integration**: Professional email delivery with high deliverability
- **Custom HTML Templates**: Beautiful, responsive email templates
- **Template Fallback**: Falls back to custom HTML if SendGrid templates aren't configured

## Architecture

### Backend Components

#### 1. API Endpoints (`rest_api/routes/auth.py`)

```python
# Password reset request
POST /api/auth/forgot-password
{
    "email": "<EMAIL>"
}

# Password reset execution  
POST /api/auth/reset-password
{
    "token": "secure_token_here",
    "new_password": "new_secure_password"
}

# Token verification
GET /api/auth/verify-reset-token/{token}
```

#### 2. Smart URL Detection (`detect_frontend_url()`)

The system detects the appropriate frontend URL using this priority order:

1. **Origin Header** (most reliable for CORS requests)
2. **Referer Header** (for form submissions)
3. **Host Header** with protocol detection:
   - `http://` for localhost/192.168.x.x (development)
   - `https://` for all other domains (production)
4. **Environment Variable Fallback** (`FRONTEND_BASE_URL`)

#### 3. Password Reset Service (`rest_api/services/password_reset_service.py`)

```python
class PasswordResetService:
    async def request_password_reset(
        self, 
        email: str, 
        client_ip: str, 
        user_agent: str,
        frontend_url: str = None  # Smart URL detection
    ) -> bool:
```

#### 4. Email Service (`rest_api/services/email_service.py`)

```python
class EmailService:
    async def send_password_reset_email(
        self,
        to_email: str,
        user_name: str,
        reset_link: str,  # Contains smart redirect URL
        expiry_time: str = "24 hours"
    ) -> bool:
```

### Frontend Components

#### 1. Forgot Password Page (`src/pages/ForgotPasswordPage.tsx`)
- Clean, responsive UI matching the login page design
- Email validation with real-time feedback
- Success state with clear instructions
- Error handling with user-friendly messages

#### 2. Reset Password Page (`src/pages/ResetPasswordPage.tsx`)
- Token verification on page load
- Password strength validation
- Password confirmation matching
- Show/hide password toggles
- Success state with automatic redirect

#### 3. Updated Login Page (`src/pages/ShadcnLoginPage.tsx`)
- Added "Forgot your password?" link
- Seamless integration with existing design

### Database Schema

#### Firestore Collection: `password_reset_tokens`

```javascript
{
  "user_id": "firebase_user_uid",
  "email": "<EMAIL>",
  "token": "sha256_hashed_token",
  "expires_at": "2024-01-15T10:30:00Z",
  "used": false,
  "created_at": "2024-01-14T10:30:00Z",
  "used_at": null,
  "ip_address": "*************",
  "user_agent": "Mozilla/5.0...",
  "frontend_url": "http://localhost:3000"  // Smart detected URL
}
```

## Smart URL Detection Examples

### Development Environment
```
Request from: http://localhost:3000
Generated link: http://localhost:3000/reset-password?token=abc123
```

### Local Network Development
```
Request from: http://*************:3000
Generated link: http://*************:3000/reset-password?token=abc123
```

### Staging Environment
```
Request from: https://staging.drcrlabs.com
Generated link: https://staging.drcrlabs.com/reset-password?token=abc123
```

### Production Environment
```
Request from: https://app.drcrlabs.com
Generated link: https://app.drcrlabs.com/reset-password?token=abc123
```

## Configuration

### Environment Variables

```bash
# SendGrid Configuration
SENDGRID_API_KEY=SG.your_api_key_here
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=DRCR Labs
SENDGRID_TEMPLATE_ID_PASSWORD_RESET=d-template_id  # Optional

# Frontend Configuration
FRONTEND_BASE_URL=https://app.drcrlabs.com  # Fallback URL
PASSWORD_RESET_URL_PATH=/reset-password     # Reset page path

# Firebase/Firestore Configuration
GCP_PROJECT_ID=your_project_id
```

### Dependencies

#### Backend (`rest_api/requirements.txt`)
```
sendgrid==6.10.0
email-validator==2.1.0
```

#### Frontend (`package.json`)
```json
{
  "dependencies": {
    "react-router-dom": "^6.x.x",
    "sonner": "^1.x.x"
  }
}
```

## Testing

### Backend Testing

```bash
# Test smart password reset functionality
python test_smart_password_reset.py
```

### Manual Testing Scenarios

1. **Localhost Development**:
   - Start frontend on `http://localhost:3000`
   - Request password reset
   - Verify email contains `localhost:3000` link

2. **Production Testing**:
   - Deploy to production domain
   - Request password reset
   - Verify email contains production domain link

## Security Considerations

### Rate Limiting
- Maximum 3 requests per hour per email address
- Prevents abuse and spam
- Graceful handling without revealing rate limits to attackers

### Token Security
- 32-byte cryptographically secure tokens
- SHA-256 hashing before database storage
- 24-hour automatic expiration
- Single-use invalidation

### Privacy Protection
- Always returns success message (doesn't reveal if email exists)
- Logs security events for monitoring
- No sensitive data in URLs or logs

## Error Handling

### Backend Error Responses
```json
{
  "detail": "An error occurred while processing your request. Please try again later."
}
```

### Frontend Error States
- Invalid/expired token handling
- Network error recovery
- User-friendly error messages
- Fallback to request new reset link

## Deployment Checklist

### Backend Deployment
- [ ] Set `SENDGRID_API_KEY` environment variable
- [ ] Configure `FRONTEND_BASE_URL` for production
- [ ] Verify Firebase/Firestore credentials
- [ ] Test email delivery in production

### Frontend Deployment
- [ ] Verify `/forgot-password` route is accessible
- [ ] Verify `/reset-password` route is accessible
- [ ] Test password reset flow end-to-end
- [ ] Verify email links redirect correctly

## Monitoring and Logging

### Key Metrics to Monitor
- Password reset request volume
- Email delivery success rate
- Token usage and expiration rates
- Failed reset attempts

### Log Events
- Password reset requests (with IP and user agent)
- Email delivery status
- Token verification attempts
- Successful password resets

## Future Enhancements

### Potential Improvements
1. **Custom Email Templates**: Design branded SendGrid templates
2. **Multi-Language Support**: Localized email content
3. **Advanced Rate Limiting**: IP-based and user-based limits
4. **Analytics Dashboard**: Reset request metrics and trends
5. **SMS Backup**: Alternative reset method via SMS

## Troubleshooting

### Common Issues

#### Email Not Received
1. Check spam/junk folder
2. Verify SendGrid API key is valid
3. Check SendGrid delivery logs
4. Verify sender email is not blacklisted

#### Invalid Token Errors
1. Check token expiration (24 hours)
2. Verify token hasn't been used already
3. Check for URL encoding issues
4. Verify database connectivity

#### Wrong Redirect URL
1. Check request headers (Origin, Referer, Host)
2. Verify `FRONTEND_BASE_URL` environment variable
3. Test with different browsers/environments
4. Check CORS configuration

## Conclusion

The smart password reset implementation provides a seamless, secure, and user-friendly experience across all deployment environments. The automatic URL detection ensures users are always redirected to the correct frontend, whether they're developing locally, testing on staging, or using the production application.

The system is production-ready with comprehensive security measures, proper error handling, and extensive logging for monitoring and debugging. 