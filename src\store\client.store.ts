import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { ClientsService } from '@/services/clients.service';
import type {
  ClientSummaryEnhanced,
  ClientResponse,
  ClientCreate,
  ClientUpdate,
  ClientFilters,
  ClientEnums,
  ClientWizardStep1,
  ClientWizardStep2,
  ClientWizardStep3,
} from '@/types/client.types';

interface ClientState {
  // Data
  clients: ClientSummaryEnhanced[];
  currentClient: ClientResponse | null;
  clientEnums: ClientEnums | null;
  
  // Pagination
  pagination: {
    current_page: number;
    page_size: number;
    total_items: number;
    total_pages: number;
  };
  
  // Filters
  filters: ClientFilters;
  
  // Loading states
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  isLoadingEnums: boolean;
  
  // Error states
  error: string | null;
  
  // Actions
  fetchClients: (filters?: ClientFilters) => Promise<void>;
  fetchClient: (clientId: string) => Promise<void>;
  createClient: (clientData: ClientCreate) => Promise<string>;
  createClientFromWizard: (
    step1: ClientWizardStep1,
    step2: ClientWizardStep2,
    step3: ClientWizardStep3
  ) => Promise<string>;
  updateClient: (clientId: string, clientData: ClientUpdate) => Promise<void>;
  deleteClient: (clientId: string) => Promise<void>;
  fetchClientEnums: () => Promise<void>;
  
  // Filter actions
  setFilters: (filters: Partial<ClientFilters>) => void;
  resetFilters: () => void;
  
  // Utility actions
  clearError: () => void;
  clearCurrentClient: () => void;
  setCurrentClient: (client: ClientResponse | null) => void;
}

const initialFilters: ClientFilters = {
  page: 1,
  limit: 20,
  name_filter: '',
  status_filter: '',
  client_type_filter: '',
};

const initialPagination = {
  current_page: 1,
  page_size: 20,
  total_items: 0,
  total_pages: 0,
};

export const useClientStore = create<ClientState>()(
  devtools(
    (set, get) => ({
      // Initial state
      clients: [],
      currentClient: null,
      clientEnums: null,
      pagination: initialPagination,
      filters: initialFilters,
      isLoading: false,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
      isLoadingEnums: false,
      error: null,

      // Fetch clients with filtering and pagination
      fetchClients: async (filters?: ClientFilters) => {
        set({ isLoading: true, error: null });
        
        try {
          const currentFilters = filters || get().filters;
          const response = await ClientsService.getClientsEnhanced(currentFilters);
          
          set({
            clients: response.clients,
            pagination: response.pagination,
            filters: currentFilters,
            isLoading: false,
          });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to fetch clients',
            isLoading: false,
          });
        }
      },

      // Fetch single client details
      fetchClient: async (clientId: string) => {
        set({ isLoading: true, error: null });
        
        try {
          const client = await ClientsService.getClientDetails(clientId);
          set({
            currentClient: client,
            isLoading: false,
          });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to fetch client',
            isLoading: false,
          });
        }
      },

      // Create new client
      createClient: async (clientData: ClientCreate): Promise<string> => {
        set({ isCreating: true, error: null });
        
        try {
          const response = await ClientsService.createClientEnhanced(clientData);
          
          // Refresh clients list
          await get().fetchClients();
          
          set({ isCreating: false });
          return response.client_id;
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to create client',
            isCreating: false,
          });
          throw error;
        }
      },

      // Create client from wizard
      createClientFromWizard: async (
        step1: ClientWizardStep1,
        step2: ClientWizardStep2,
        step3: ClientWizardStep3
      ): Promise<string> => {
        set({ isCreating: true, error: null });
        
        try {
          const response = await ClientsService.createClientFromWizard(step1, step2, step3);
          
          // Refresh clients list
          await get().fetchClients();
          
          set({ isCreating: false });
          return response.client_id;
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to create client',
            isCreating: false,
          });
          throw error;
        }
      },

      // Update client
      updateClient: async (clientId: string, clientData: ClientUpdate) => {
        set({ isUpdating: true, error: null });
        
        try {
          await ClientsService.updateClientEnhanced(clientId, clientData);
          
          // Refresh current client if it's the one being updated
          if (get().currentClient?.client_id === clientId) {
            await get().fetchClient(clientId);
          }
          
          // Refresh clients list
          await get().fetchClients();
          
          set({ isUpdating: false });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to update client',
            isUpdating: false,
          });
          throw error;
        }
      },

      // Delete client
      deleteClient: async (clientId: string) => {
        set({ isDeleting: true, error: null });
        
        try {
          await ClientsService.deleteClient(clientId);
          
          // Clear current client if it's the one being deleted
          if (get().currentClient?.client_id === clientId) {
            set({ currentClient: null });
          }
          
          // Refresh clients list
          await get().fetchClients();
          
          set({ isDeleting: false });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to delete client',
            isDeleting: false,
          });
          throw error;
        }
      },

      // Fetch client enums for forms
      fetchClientEnums: async () => {
        set({ isLoadingEnums: true, error: null });
        
        try {
          const enums = await ClientsService.getClientEnums();
          set({
            clientEnums: enums,
            isLoadingEnums: false,
          });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to fetch client options',
            isLoadingEnums: false,
          });
        }
      },

      // Filter management
      setFilters: (newFilters: Partial<ClientFilters>) => {
        const currentFilters = get().filters;
        const updatedFilters = { ...currentFilters, ...newFilters };
        
        // Reset to page 1 when filters change (except when changing page)
        if (!newFilters.page) {
          updatedFilters.page = 1;
        }
        
        set({ filters: updatedFilters });
        
        // Auto-fetch with new filters
        get().fetchClients(updatedFilters);
      },

      resetFilters: () => {
        set({ filters: initialFilters });
        get().fetchClients(initialFilters);
      },

      // Utility actions
      clearError: () => set({ error: null }),
      
      clearCurrentClient: () => set({ currentClient: null }),
      
      setCurrentClient: (client: ClientResponse | null) => set({ currentClient: client }),
    }),
    {
      name: 'client-store',
    }
  )
);

// Selectors for easier access to computed values
export const useClientSelectors = () => {
  const store = useClientStore();
  
  return {
    // Basic selectors
    clients: store.clients,
    currentClient: store.currentClient,
    pagination: store.pagination,
    filters: store.filters,
    
    // Loading states
    isLoading: store.isLoading,
    isCreating: store.isCreating,
    isUpdating: store.isUpdating,
    isDeleting: store.isDeleting,
    isLoadingEnums: store.isLoadingEnums,
    
    // Computed values
    hasClients: store.clients.length > 0,
    totalClients: store.pagination.total_items,
    hasNextPage: store.pagination.current_page < store.pagination.total_pages,
    hasPrevPage: store.pagination.current_page > 1,
    
    // Filter states
    hasActiveFilters: Boolean(
      store.filters.name_filter ||
      (store.filters.status_filter && store.filters.status_filter !== '') ||
      (store.filters.client_type_filter && store.filters.client_type_filter !== '')
    ),
    
    // Error state
    error: store.error,
    hasError: Boolean(store.error),
  };
}; 