# DRCR Backend Agent Guide

## Build/Test Commands
- Install dependencies: `pip install -r requirements.txt`
- Run local tests: `python -m cloud_functions.xero_sync_consumer.main`
- Run direct test: `python direct_test.py`
- Run single test: `python -m unittest test_file.TestClass.test_method`

## MCP Servers
- Puppeteer: Web automation for testing UI interactions with Xero
  - Tools: puppeteer_navigate, puppeteer_screenshot, puppeteer_click, puppeteer_fill
- Context7: Library documentation lookup
  - Usage: First use `resolve-library-id` to get the Context7-compatible ID
  - Then use `get-library-docs` with that ID and optional topic parameter

## Code Style
- Imports order: standard library, third-party, local/relative imports
- Use type hints throughout (from typing import Dict, List, Optional, Any)
- Use exceptions for error handling with specific exception types
- Variable naming: snake_case for variables/functions, PascalCase for classes
- Comprehensive error logging using self.logger
- Async/await for I/O operations
- Use f-strings for string formatting
- Error handling includes detailed logs with exception information
- Custom exceptions should subclass Exception with informative messages

## Project Structure
- `drcr_shared_logic/` - Core business logic and shared components
- `cloud_functions/` - Cloud function implementations
- `rest_api/` - FastAPI-based REST API
- `terraform/` - Infrastructure as code