import React, { useCallback } from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle as AlertCircleIcon } from 'lucide-react';
import { TooltipProvider } from '@/components/ui/tooltip';
import { XeroOperationStatus } from '@/components/ui/xero-operation-status';
import { ConnectNewEntityModal } from '@/components/entities/ConnectNewEntityModal';
import { EntitySettingsManagement } from '@/components/entities/EntitySettingsManagement';
import { DraggableDialog, DraggableDialogContent, DraggableDialogHeader, DraggableDialogTitle, DraggableDialogFooter, DraggableDialogDescription } from '@/components/ui/draggable-dialog';
import { CreateClientModal } from '@/components/clients';
import { EditClientModal } from '@/components/clients/EditClientModal';
import { EntitiesService } from '@/services/entities.service';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

import { useDashboardState } from '../hooks/useDashboardState';
import { DashboardFiltersComponent } from './DashboardFilters';
import { ClientsTable } from './ClientsTable';
import { DashboardPagination } from './DashboardPagination';
import type { FirmDashboardProps } from '../types';

export function FirmClientsOverviewDashboard({ firmId, firmName, isAdmin = true }: FirmDashboardProps) {
  const dashboardState = useDashboardState(firmId);

  // State for entity settings dialog actions
  const [entitySaveHandler, setEntitySaveHandler] = React.useState<(() => Promise<void>) | null>(null);
  const [canSaveEntity, setCanSaveEntity] = React.useState(false);
  const [isSavingEntity, setIsSavingEntity] = React.useState(false);

  const {
    // State
    error,
    selectedClientId,
    selectedEntityId,
    connectModalOpen,
    settingsModalOpen,
    createClientModalOpen,
    clientSettingsModalOpen,

    // Actions
    handleAddNewClient,
    handleInitiateConnection,

    // Utilities
    filters,
    setFilters,
    setConnectModalOpen,
    setSettingsModalOpen,
    setCreateClientModalOpen,
    setClientSettingsModalOpen,
    refreshData
  } = dashboardState;

  // Handle entity settings save action callback
  const handleEntitySaveAction = React.useCallback((saveHandler: () => Promise<void>, canSave: boolean, isSaving: boolean) => {
    setEntitySaveHandler(() => saveHandler);
    setCanSaveEntity(canSave);
    setIsSavingEntity(isSaving);
  }, []);

  // Async handlers for settings modal - wrap in useCallback to prevent infinite loops
  const handleFetchSettings = useCallback(async (entityId: string) => {
    try {
      const entity = await EntitiesService.getEntity(entityId);
      const settings = entity.settings || {};

      return {
        entityId: entity.entity_id,
        entityName: entity.entity_name,
        prepaymentAssetAccountCodes: (settings as any)['prepayment_asset_account_codes'] || [],
        defaultExpenseAccountCode: (settings as any)['default_expense_account_code'] || null,
        defaultAmortizationMonths: (settings as any)['default_amortization_months'] || 12,
        autoSyncEnabled: (settings as any)['auto_sync_enabled'] ?? true,
        syncFrequency: (settings as any)['sync_frequency'] || 'daily',
        syncSpendMoney: (settings as any)['sync_spend_money'] ?? true,
        excludedPnlAccountCodes: (settings as any)['excluded_pnl_account_codes'] || [],
        transactionSyncStartDate: (settings as any)['transaction_sync_start_date'] || '',
        syncInvoices: (settings as any)['sync_invoices'] ?? true,
        syncBills: (settings as any)['sync_bills'] ?? true,
        syncPayments: (settings as any)['sync_payments'] ?? true,
        syncBankTransactions: (settings as any)['sync_bank_transactions'] ?? true,
        syncJournalEntries: (settings as any)['sync_journal_entries'] ?? true,
        autoPostProposedJournals: (settings as any)['auto_post_proposed_journals'] ?? false,
        baseCurrencyCode: (settings as any)['base_currency_code'] || 'USD',
      };
    } catch (error) {
      console.error('Error fetching settings:', error);
      throw error;
    }
  }, []);

  const handleFetchChartOfAccounts = useCallback(async (entityId: string) => {
    try {
      const chartOfAccounts = await EntitiesService.getChartOfAccounts(entityId);
      return chartOfAccounts;
    } catch (error) {
      console.error('Error fetching chart of accounts:', error);
      throw error;
    }
  }, []);

  const handleSaveSettings = useCallback(async (entityId: string, settings: any) => {
    try {
      // Convert camelCase to snake_case for backend API
      const backendSettings: any = {
        prepayment_asset_account_codes: settings.prepaymentAssetAccountCodes,
        default_expense_account_code: settings.defaultExpenseAccountCode,
        default_amortization_months: settings.defaultAmortizationMonths,
        auto_sync_enabled: settings.autoSyncEnabled,
        sync_frequency: settings.syncFrequency,
        sync_spend_money: settings.syncSpendMoney,
        excluded_pnl_account_codes: settings.excludedPnlAccountCodes,
        sync_invoices: settings.syncInvoices,
        sync_bills: settings.syncBills,
        sync_payments: settings.syncPayments,
        sync_bank_transactions: settings.syncBankTransactions,
        sync_journal_entries: settings.syncJournalEntries,
        auto_post_proposed_journals: settings.autoPostProposedJournals,
        base_currency_code: settings.baseCurrencyCode,
      };

      // Only include transaction_sync_start_date if it's not empty
      if (settings.transactionSyncStartDate && settings.transactionSyncStartDate.trim() !== '') {
        backendSettings.transaction_sync_start_date = settings.transactionSyncStartDate;
      }

      await EntitiesService.updateEntitySettings(entityId, backendSettings);
      await refreshData();
    } catch (error) {
      console.error('Error saving settings:', error);
      throw error;
    }
  }, [refreshData]);

  const handleClientCreated = useCallback(async (clientId: string) => {
    // Refresh the dashboard data to show the new client
    await refreshData();
    // Optionally show a success message or navigate to the client
    console.log('Client created successfully:', clientId);
  }, [refreshData]);

  return (
    <TooltipProvider delayDuration={100}>
      <div className="p-6 space-y-6 h-full flex flex-col">
        {/* Filter Controls */}
        <DashboardFiltersComponent
          filters={filters}
          onFiltersChange={setFilters}
          onAddNewClient={handleAddNewClient}
          isAdmin={isAdmin}
        />

        {/* Error Display */}
        {error && (
          <Alert variant="destructive" className="flex-shrink-0">
            <AlertCircleIcon className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Client Table */}
        <ClientsTable {...dashboardState} />

        {/* Pagination Controls */}
        <DashboardPagination
          pagination={dashboardState.pagination}
          isLoading={dashboardState.isLoading}
          onPageChange={(page) => setFilters({ page })}
        />

        {/* Modals */}
        <CreateClientModal
          isOpen={createClientModalOpen}
          onClose={() => setCreateClientModalOpen(false)}
          onSuccess={handleClientCreated}
        />

        <EditClientModal
          isOpen={clientSettingsModalOpen}
          onClose={() => setClientSettingsModalOpen(false)}
          clientId={selectedClientId || ''}
          onSuccess={refreshData}
        />

        <ConnectNewEntityModal
          isOpen={connectModalOpen}
          onClose={() => setConnectModalOpen(false)}
          clientId={selectedClientId || ''}
          onInitiateConnection={handleInitiateConnection}
        />

        {selectedEntityId && (
          <DraggableDialog open={settingsModalOpen} onOpenChange={setSettingsModalOpen}>
            <DraggableDialogContent className="flex flex-col p-0 gap-0">
              <DraggableDialogHeader className="p-6 border-b flex-shrink-0">
                <DraggableDialogTitle>Entity Settings</DraggableDialogTitle>
                <DraggableDialogDescription>
                  Configure prepayment and expense accounts for this entity.
                </DraggableDialogDescription>
              </DraggableDialogHeader>
              <div className="flex-1 overflow-y-auto p-6">
                <EntitySettingsManagement
                  entityId={selectedEntityId}
                  fetchSettings={handleFetchSettings}
                  fetchChartOfAccounts={handleFetchChartOfAccounts}
                  saveSettings={handleSaveSettings}
                  hideFooter={true}
                  onSaveAction={handleEntitySaveAction}
                />
              </div>
              <DraggableDialogFooter className="p-6 border-t flex-shrink-0">
                <Button variant="outline" onClick={() => setSettingsModalOpen(false)} disabled={isSavingEntity}>
                  Cancel
                </Button>
                <Button
                  onClick={entitySaveHandler || (() => {})}
                  disabled={isSavingEntity || !canSaveEntity || !entitySaveHandler}
                >
                  {isSavingEntity && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Save Settings
                </Button>
              </DraggableDialogFooter>
            </DraggableDialogContent>
          </DraggableDialog>
        )}
      </div>
    </TooltipProvider>
  );
}

