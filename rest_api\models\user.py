from pydantic import BaseModel, EmailStr, Field
from typing import Optional

class Token(BaseModel):
    """Token model for authentication."""
    access_token: str
    token_type: str

class TokenData(BaseModel):
    """Token data model."""
    username: Optional[str] = None

class UserBase(BaseModel):
    email: EmailStr = Field(..., example="<EMAIL>")
    username: str = Field(..., min_length=3, max_length=50, example="john_doe")
    full_name: Optional[str] = Field(None, example="John Doe")
    # Add other base fields

class UserCreate(UserBase):
    password: str = Field(..., min_length=8, example="strongpassword123")

class UserUpdate(BaseModel):
    email: Optional[EmailStr] = None
    username: Optional[str] = Field(None, min_length=3, max_length=50)
    full_name: Optional[str] = None
    password: Optional[str] = Field(None, min_length=8)
    is_active: Optional[bool] = None
    is_superuser: Optional[bool] = None

class UserInDBBase(UserBase):
    id: int = Field(..., example=1)
    is_active: bool = Field(True, example=True)
    is_superuser: bool = Field(False, example=False)

    # If this model is created from an ORM object:
    class Config:
        from_attributes = True

class User(UserInDBBase):
    # Any additional fields for the User model returned to the client
    pass

class UserInDB(UserInDBBase):
    hashed_password: str
