from pydantic import BaseModel, EmailStr, Field
from typing import Optional

class ForgotPasswordRequest(BaseModel):
    """Request model for forgot password endpoint"""
    email: EmailStr = Field(..., description="User's email address")

class ForgotPasswordResponse(BaseModel):
    """Response model for forgot password endpoint"""
    message: str = Field(..., description="Success or error message")
    success: bool = Field(..., description="Whether the request was successful")

class ResetPasswordRequest(BaseModel):
    """Request model for reset password endpoint"""
    token: str = Field(..., description="Password reset token", min_length=1)
    new_password: str = Field(..., description="New password", min_length=8)

class ResetPasswordResponse(BaseModel):
    """Response model for reset password endpoint"""
    message: str = Field(..., description="Success or error message")

class VerifyTokenResponse(BaseModel):
    """Response model for token verification endpoint"""
    valid: bool = Field(..., description="Whether the token is valid")
    email: Optional[str] = Field(None, description="Email associated with token (if valid)")
    expires_at: Optional[str] = Field(None, description="Token expiration time (if valid)")
    error: Optional[str] = Field(None, description="Error message (if invalid)")

class ErrorResponse(BaseModel):
    """Standard error response model"""
    error: str = Field(..., description="Error code")
    message: str = Field(..., description="Human-readable error message") 