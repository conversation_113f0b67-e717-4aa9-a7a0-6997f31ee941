import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
    DraggableDialog,
    DraggableDialogContent,
    DraggableDialogDescription,
    DraggableDialogFooter,
    DraggableDialogHeader,
    DraggableDialogTitle,
    DraggableDialogClose,
} from '@/components/ui/draggable-dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

export default function DraggableDialogDemo() {
    const [isSmallDialogOpen, setIsSmallDialogOpen] = useState(false);
    const [isLargeDialogOpen, setIsLargeDialogOpen] = useState(false);
    const [isFormDialogOpen, setIsFormDialogOpen] = useState(false);

    return (
        <div className="min-h-screen bg-gray-50 p-8">
            <div className="max-w-4xl mx-auto space-y-8">
                <div className="text-center">
                    <h1 className="text-3xl font-bold text-gray-900">Draggable & Resizable Dialog Demo</h1>
                    <p className="text-gray-600 mt-2">Test the new draggable and resizable modal functionality. All dialogs now start maximized (full screen) by default for better usability.</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {/* Small Dialog Demo */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Basic Dialog</CardTitle>
                            <CardDescription>
                                A dialog with basic content that starts maximized
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Button onClick={() => setIsSmallDialogOpen(true)}>
                                Open Basic Dialog
                            </Button>
                        </CardContent>
                    </Card>

                    {/* Large Dialog Demo */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Content Dialog</CardTitle>
                            <CardDescription>
                                A dialog with more content and features
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Button onClick={() => setIsLargeDialogOpen(true)}>
                                Open Content Dialog
                            </Button>
                        </CardContent>
                    </Card>

                    {/* Form Dialog Demo */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Form Dialog</CardTitle>
                            <CardDescription>
                                A dialog with form elements and scrolling
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Button onClick={() => setIsFormDialogOpen(true)}>
                                Open Form Dialog
                            </Button>
                        </CardContent>
                    </Card>
                </div>

                {/* Instructions */}
                <Card>
                    <CardHeader>
                        <CardTitle>How to Use</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                        <div className="bg-blue-50 p-4 rounded-lg">
                          <h3 className="font-semibold text-blue-900 mb-2">How to Use:</h3>
                          <p><strong>Default State:</strong> All dialogs start maximized (full screen) for maximum usability</p>
                          <p><strong>Minimize:</strong> Click the minimize button in the header to switch to windowed mode</p>
                          <p><strong>Maximize:</strong> Click the maximize button to return to full screen</p>
                          <p><strong>Drag:</strong> In windowed mode, click and drag the header bar to move the dialog</p>
                          <p><strong>Resize:</strong> In windowed mode, drag the edges or corners to resize the dialog</p>
                          <p><strong>Scrolling:</strong> Content automatically scrolls when needed</p>
                        </div>
                    </CardContent>
                </Card>

                {/* Basic Dialog */}
                <DraggableDialog open={isSmallDialogOpen} onOpenChange={setIsSmallDialogOpen}>
                    <DraggableDialogContent>
                        <DraggableDialogHeader>
                            <DraggableDialogTitle>Basic Dialog</DraggableDialogTitle>
                            <DraggableDialogDescription>
                                This dialog starts maximized and can be minimized to windowed mode.
                            </DraggableDialogDescription>
                        </DraggableDialogHeader>
                        
                        <div className="space-y-4">
                            <p>This dialog starts in full screen mode by default for better usability.</p>
                            <p>Click the minimize button in the header to switch to windowed mode, then you can drag and resize it!</p>
                            <p>The maximize button will return it to full screen.</p>
                        </div>

                        <DraggableDialogFooter>
                            <DraggableDialogClose asChild>
                                <Button variant="outline">Close</Button>
                            </DraggableDialogClose>
                        </DraggableDialogFooter>
                    </DraggableDialogContent>
                </DraggableDialog>

                {/* Content Dialog */}
                <DraggableDialog open={isLargeDialogOpen} onOpenChange={setIsLargeDialogOpen}>
                    <DraggableDialogContent>
                        <DraggableDialogHeader>
                            <DraggableDialogTitle>Content Dialog</DraggableDialogTitle>
                            <DraggableDialogDescription>
                                This dialog demonstrates content handling and scrolling.
                            </DraggableDialogDescription>
                        </DraggableDialogHeader>
                        
                        <div className="space-y-4">
                            <h3 className="text-lg font-semibold">Features</h3>
                            <ul className="list-disc list-inside space-y-2">
                                <li>Starts maximized (full screen) by default</li>
                                <li>Fully draggable in windowed mode by clicking and dragging the header</li>
                                <li>Resizable in windowed mode by dragging edges and corners</li>
                                <li>Maximize/minimize functionality with smooth transitions</li>
                                <li>Respects minimum and maximum size constraints</li>
                                <li>Content scrolls automatically when needed</li>
                            </ul>
                            
                            <h3 className="text-lg font-semibold">Content Area</h3>
                            <p>This content area scrolls when the dialog is resized to be smaller than the content.</p>
                            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
                            <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
                            <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
                            <p>Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
                        </div>

                        <DraggableDialogFooter>
                            <DraggableDialogClose asChild>
                                <Button variant="outline">Cancel</Button>
                            </DraggableDialogClose>
                            <Button>Save Changes</Button>
                        </DraggableDialogFooter>
                    </DraggableDialogContent>
                </DraggableDialog>

                {/* Form Dialog */}
                <DraggableDialog open={isFormDialogOpen} onOpenChange={setIsFormDialogOpen}>
                    <DraggableDialogContent>
                        <DraggableDialogHeader>
                            <DraggableDialogTitle>Form Dialog</DraggableDialogTitle>
                            <DraggableDialogDescription>
                                A dialog containing form elements that demonstrates scrolling behavior.
                            </DraggableDialogDescription>
                        </DraggableDialogHeader>
                        
                        <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="firstName">First Name</Label>
                                    <Input id="firstName" placeholder="Enter first name" />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="lastName">Last Name</Label>
                                    <Input id="lastName" placeholder="Enter last name" />
                                </div>
                            </div>
                            
                            <div className="space-y-2">
                                <Label htmlFor="email">Email</Label>
                                <Input id="email" type="email" placeholder="Enter email address" />
                            </div>
                            
                            <div className="space-y-2">
                                <Label htmlFor="message">Message</Label>
                                <Textarea 
                                    id="message" 
                                    placeholder="Enter your message here..."
                                    rows={4}
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="company">Company</Label>
                                <Input id="company" placeholder="Enter company name" />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="phone">Phone</Label>
                                <Input id="phone" placeholder="Enter phone number" />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="address">Address</Label>
                                <Textarea 
                                    id="address" 
                                    placeholder="Enter full address..."
                                    rows={3}
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="notes">Additional Notes</Label>
                                <Textarea 
                                    id="notes" 
                                    placeholder="Any additional information..."
                                    rows={4}
                                />
                            </div>
                        </div>

                        <DraggableDialogFooter>
                            <DraggableDialogClose asChild>
                                <Button variant="outline">Cancel</Button>
                            </DraggableDialogClose>
                            <Button>Submit</Button>
                        </DraggableDialogFooter>
                    </DraggableDialogContent>
                </DraggableDialog>
            </div>
        </div>
    );
} 