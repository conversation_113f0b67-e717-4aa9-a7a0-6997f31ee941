import os
import secrets
import hashlib
import logging
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any, Tu<PERSON>
from firebase_admin import auth as firebase_auth
from firebase_admin.exceptions import FirebaseError
from google.cloud.firestore import SERVER_TIMESTAMP
from email_validator import validate_email, EmailNotValidError
from google.cloud.firestore import FieldFilter

logger = logging.getLogger(__name__)

class PasswordResetService:
    """Service for handling password reset functionality"""
    
    def __init__(self, db):
        self.db = db
        self.frontend_base_url = os.getenv("FRONTEND_BASE_URL", "https://app.drcrlabs.com")
        self.reset_url_path = os.getenv("PASSWORD_RESET_URL_PATH", "/reset-password")
        self.token_expiry_hours = 24
        self.rate_limit_requests = 3  # Max requests per hour per email
        
        # Import and create email service instance
        from .email_service import EmailService
        self.email_service = EmailService()
    
    def _generate_secure_token(self) -> str:
        """Generate a cryptographically secure token"""
        return secrets.token_urlsafe(32)
    
    def _hash_token(self, token: str) -> str:
        """Hash a token using SHA-256"""
        return hashlib.sha256(token.encode()).hexdigest()
    
    def _get_reset_link(self, token: str, frontend_url: str = None) -> str:
        """Generate the complete password reset URL"""
        base_url = frontend_url or self.frontend_base_url
        return f"{base_url}{self.reset_url_path}?token={token}"
    
    async def _check_rate_limit(self, email: str, ip_address: str) -> bool:
        """
        Check if the email/IP is within rate limits
        
        Args:
            email: Email address to check
            ip_address: IP address to check
            
        Returns:
            bool: True if within rate limits, False if exceeded
        """
        try:
            # Check requests in the last hour
            one_hour_ago = datetime.now(timezone.utc) - timedelta(hours=1)
            
            # Query for recent requests from this email
            email_requests = self.db.collection("password_reset_tokens")\
                .where(filter=FieldFilter("email", "==", email))\
                .where(filter=FieldFilter("created_at", ">=", one_hour_ago))\
                .stream()
            
            # Count the requests using async iteration
            email_count = 0
            async for doc in email_requests:
                email_count += 1
            
            if email_count >= self.rate_limit_requests:
                logger.warning(f"Rate limit exceeded for email {email}: {email_count} requests in last hour")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking rate limit for {email}: {e}")
            # Allow the request if we can't check rate limits
            return True
    
    async def _invalidate_existing_tokens(self, user_id: str) -> None:
        """Invalidate all existing tokens for a user"""
        try:
            # Get all active tokens for the user
            tokens_ref = self.db.collection("password_reset_tokens")\
                .where(filter=FieldFilter("user_id", "==", user_id))\
                .where(filter=FieldFilter("used", "==", False))
            
            tokens = tokens_ref.stream()
            
            # Mark all tokens as used using async iteration
            async for token_doc in tokens:
                await token_doc.reference.update({
                    "used": True,
                    "used_at": SERVER_TIMESTAMP
                })
                
            logger.info(f"Invalidated existing tokens for user {user_id}")
            
        except Exception as e:
            logger.error(f"Error invalidating tokens for user {user_id}: {e}")
    
    async def request_password_reset(
        self, 
        email: str, 
        client_ip: str, 
        user_agent: str,
        frontend_url: str = None
    ) -> bool:
        """
        Request a password reset for the given email
        
        Args:
            email: User's email address
            client_ip: Request IP address
            user_agent: Request user agent
            frontend_url: Frontend URL to use for reset link (auto-detected from request)
            
        Returns:
            bool: True if successful (always returns True for security)
        """
        try:
            # Validate email format
            try:
                validated_email = validate_email(email)
                email = validated_email.email
            except EmailNotValidError:
                logger.warning(f"Invalid email format: {email}")
                return True  # Don't reveal invalid email for security
            
            # Check rate limits
            if not await self._check_rate_limit(email, client_ip):
                logger.warning(f"Rate limit exceeded for {email} from {client_ip}")
                return True  # Don't reveal rate limiting for security
            
            # Check if user exists in Firebase
            try:
                firebase_user = firebase_auth.get_user_by_email(email)
                user_id = firebase_user.uid
                display_name = firebase_user.display_name or email.split("@")[0]
            except FirebaseError:
                # Don't reveal if email exists - return success anyway
                logger.info(f"Password reset requested for non-existent email: {email}")
                return True
            
            # Invalidate existing tokens for this user
            await self._invalidate_existing_tokens(user_id)
            
            # Generate new token
            token = self._generate_secure_token()
            token_hash = self._hash_token(token)
            
            # Calculate expiry time
            expires_at = datetime.now(timezone.utc) + timedelta(hours=self.token_expiry_hours)
            
            # Store token in Firestore
            token_data = {
                "user_id": user_id,
                "email": email,
                "token": token_hash,
                "expires_at": expires_at,
                "used": False,
                "created_at": SERVER_TIMESTAMP,
                "ip_address": client_ip,
                "user_agent": user_agent,
                "frontend_url": frontend_url or self.frontend_base_url
            }
            
            await self.db.collection("password_reset_tokens").add(token_data)
            
            # Generate reset link with smart URL detection
            reset_link = self._get_reset_link(token, frontend_url)
            
            # Send email
            email_sent = await self.email_service.send_password_reset_email(
                to_email=email,
                user_name=display_name,
                reset_link=reset_link,
                expiry_time="24 hours"
            )
            
            if email_sent:
                logger.info(f"Password reset email sent successfully to {email} with link to {frontend_url or self.frontend_base_url}")
                return True
            else:
                logger.error(f"Failed to send password reset email to {email}")
                return True  # Still return True for security
                
        except Exception as e:
            logger.error(f"Error requesting password reset for {email}: {e}")
            return True  # Still return True for security
    
    async def verify_reset_token(self, token: str) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """
        Verify if a reset token is valid
        
        Args:
            token: The reset token to verify
            
        Returns:
            Tuple[bool, Optional[Dict]]: (is_valid, token_data)
        """
        try:
            token_hash = self._hash_token(token)
            
            # Find the token in Firestore
            tokens_ref = self.db.collection("password_reset_tokens")\
                .where(filter=FieldFilter("token", "==", token_hash))\
                .where(filter=FieldFilter("used", "==", False))\
                .limit(1)
            
            tokens = tokens_ref.stream()
            
            # Get the first token using async iteration
            token_doc = None
            async for doc in tokens:
                token_doc = doc
                break
            
            if not token_doc:
                return False, None
            
            token_data = token_doc.to_dict()
            
            # Check if token has expired
            expires_at = token_data.get("expires_at")
            if expires_at and datetime.now(timezone.utc) > expires_at:
                return False, None
            
            return True, {
                "email": token_data.get("email"),
                "user_id": token_data.get("user_id"),
                "expires_at": expires_at.isoformat() if expires_at else None,
                "doc_id": token_doc.id
            }
            
        except Exception as e:
            logger.error(f"Error verifying reset token: {e}")
            return False, None
    
    async def reset_password(self, token: str, new_password: str) -> Tuple[bool, str]:
        """
        Reset user's password using a valid token
        
        Args:
            token: The reset token
            new_password: The new password
            
        Returns:
            Tuple[bool, str]: (success, message)
        """
        try:
            # Validate password
            if len(new_password) < 8:
                return False, "Password must be at least 8 characters long."
            
            # Verify token
            is_valid, token_data = await self.verify_reset_token(token)
            
            if not is_valid or not token_data:
                return False, "Invalid or expired reset token."
            
            user_id = token_data["user_id"]
            doc_id = token_data["doc_id"]
            
            # Update password in Firebase
            try:
                firebase_auth.update_user(user_id, password=new_password)
            except FirebaseError as e:
                logger.error(f"Failed to update password for user {user_id}: {e}")
                return False, "Failed to update password. Please try again."
            
            # Mark token as used
            token_ref = self.db.collection("password_reset_tokens").document(doc_id)
            await token_ref.update({
                "used": True,
                "used_at": SERVER_TIMESTAMP
            })
            
            logger.info(f"Password reset successfully for user {user_id}")
            return True, "Password has been reset successfully."
            
        except Exception as e:
            logger.error(f"Error resetting password: {e}")
            return False, "An error occurred. Please try again later." 