import os
import sys
import uvicorn
from dotenv import load_dotenv

# Add the project root to the Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Load environment variables
load_dotenv()

# Set Google Cloud Project ID if not set
if not os.getenv("GOOGLE_CLOUD_PROJECT") and not os.getenv("GCP_PROJECT_ID"):
    os.environ["GOOGLE_CLOUD_PROJECT"] = "drcr-d660a"  # Set your actual project ID
    os.environ["GCP_PROJECT_ID"] = "drcr-d660a"

# Print environment for debugging
print("Python version:", sys.version)
print("Current working directory:", os.getcwd())
print("FIREBASE_CREDENTIALS_PATH:", os.getenv("FIREBASE_CREDENTIALS_PATH"))
print("GOOGLE_CLOUD_PROJECT:", os.getenv("GOOGLE_CLOUD_PROJECT"))

# Check if the credentials file exists
cred_path = os.getenv("FIREBASE_CREDENTIALS_PATH")
if os.path.exists(cred_path):
    print(f"Credentials file exists at {cred_path}")
else:
    print(f"Credentials file does not exist at {cred_path}")
    sys.exit(1)

# Run the server
if __name__ == "__main__":
    # Force port 8081 to avoid conflicts
    host = "0.0.0.0"
    port = 8081
    
    # Check if we're in production mode
    is_production = os.getenv("ENVIRONMENT") == "production"
    
    print(f"Starting server on {host}:{port}")
    print(f"Auto-reload: {not is_production}")
    
    if is_production:
        # Production mode - no reload, better performance
        uvicorn.run("rest_api.main:app", host=host, port=port, reload=False, workers=1)
    else:
        # Development mode - but you can disable reload by setting DISABLE_RELOAD=true
        disable_reload = os.getenv("DISABLE_RELOAD", "false").lower() == "true"
        uvicorn.run("rest_api.main:app", host=host, port=port, reload=not disable_reload)
