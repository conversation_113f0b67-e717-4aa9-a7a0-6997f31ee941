// Entity Management Types

export enum EntityType {
  XERO = "xero",
  QBO = "qbo",
  MANUAL = "manual"
}

export enum EntityStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
  ERROR = "error",
  SYNCING = "syncing",
  PENDING = "pending",
  DISCONNECTED = "disconnected"
}

export enum ConnectionStatus {
  CONNECTED = "connected",
  DISCONNECTED = "disconnected",
  ERROR = "error",
  PENDING = "pending",
  EXPIRED = "expired",
  SYNCING = "syncing"
}

export interface EntityConnectionDetails {
  status: ConnectionStatus;
  last_checked?: string;
  last_sync?: string;
  error_message?: string;
  oauth_expires_at?: string;
  tenant_id?: string;
  organization_id?: string;
}

export interface EntitySettings {
  entity_id: string;
  entity_name: string;
  prepayment_asset_account_codes: string[];
  excluded_pnl_account_codes?: string[];
  default_amortization_months?: number;
  auto_sync_enabled?: boolean;
  sync_frequency?: 'hourly' | 'daily' | 'weekly' | 'manual';
  sync_spend_money?: boolean;
  // Transaction sync settings
  transaction_sync_start_date?: string;
  // Individual sync data type toggles
  sync_invoices?: boolean;
  sync_bills?: boolean;
  sync_payments?: boolean;
  sync_bank_transactions?: boolean;
  sync_journal_entries?: boolean;
  // Additional settings
  auto_post_proposed_journals?: boolean;
  base_currency_code?: string;
  initial_sync_completed?: boolean;
  last_full_sync_date?: string;
  notification_preferences?: {
    sync_errors: boolean;
    connection_issues: boolean;
    data_anomalies: boolean;
  };
}

export interface EntityHealthMetrics {
  connection_uptime: number; // percentage
  last_successful_sync?: string;
  sync_error_count: number;
  data_quality_score: number; // 0-100
  pending_items_count: number;
  error_items_count: number;
}

export interface EntityCreate {
  client_id: string;
  entity_name: string;
  type: EntityType;
  description?: string;
  settings?: Partial<EntitySettings>;
}

export interface EntityUpdate {
  entity_name?: string;
  description?: string;
  status?: EntityStatus;
  settings?: Partial<EntitySettings>;
}

export interface EntityResponse {
  entity_id: string;
  client_id: string;
  entity_name: string;
  type: EntityType;
  status: EntityStatus;
  description?: string;
  connection_details: EntityConnectionDetails;
  settings?: EntitySettings;
  health_metrics?: EntityHealthMetrics;
  created_at?: string;
  updated_at?: string;
}

export interface EntitySummary {
  entity_id: string;
  entity_name: string;
  type: EntityType;
  status: EntityStatus;
  connection_status: ConnectionStatus;
  last_sync?: string;
  error_message?: string;
  pending_items_count?: number;
  error_count?: number;
  health_score?: number;
}

export interface EntityListResponse {
  entities: EntitySummary[];
  pagination?: {
    current_page: number;
    page_size: number;
    total_items: number;
    total_pages: number;
  };
}

// Wizard Step Types for Entity Creation
export interface EntityWizardStep1 {
  entity_name: string;
  type: EntityType;
  description?: string;
}

export interface EntityWizardStep2 {
  connection_method: 'oauth' | 'manual';
  oauth_provider?: 'xero' | 'qbo';
  manual_config?: {
    api_key?: string;
    secret?: string;
    endpoint?: string;
  };
}

export interface EntityWizardStep3 {
  settings: Partial<EntitySettings>;
  auto_sync_enabled: boolean;
  sync_frequency: 'hourly' | 'daily' | 'weekly' | 'manual';
}

// OAuth Flow Types
export interface OAuthInitiateResponse {
  authorization_url: string;
  state: string;
  code_verifier?: string;
}

export interface OAuthCallbackData {
  code: string;
  state: string;
  scope?: string;
  error?: string;
  error_description?: string;
}

export interface OAuthTokenResponse {
  access_token: string;
  refresh_token?: string;
  expires_in: number;
  token_type: string;
  scope?: string;
}

// Connection Management Types
export interface ConnectionTestResult {
  success: boolean;
  message: string;
  details?: {
    organization_name?: string;
    organization_id?: string;
    permissions?: string[];
    api_version?: string;
  };
  error_details?: {
    error_code?: string;
    error_message?: string;
    suggested_action?: string;
  };
}

export interface SyncStatus {
  is_syncing: boolean;
  sync_started_at?: string;
  sync_progress?: number; // 0-100
  current_operation?: string;
  estimated_completion?: string;
}

// Dashboard Types
export interface EntityDashboardData {
  entity: EntityResponse;
  health_metrics: EntityHealthMetrics;
  sync_status: SyncStatus;
  recent_activity: ActivityItem[];
  data_summary: {
    accounts_count: number;
    transactions_count: number;
    invoices_count: number;
    last_updated: string;
  };
}

export interface ActivityItem {
  id: string;
  type: 'sync' | 'error' | 'connection' | 'settings' | 'data';
  title: string;
  description: string;
  timestamp: string;
  status: 'success' | 'error' | 'warning' | 'info';
  details?: Record<string, any>;
}

// Filter and Search Types
export interface EntityFilters {
  page?: number;
  limit?: number;
  client_id?: string;
  type?: EntityType;
  status?: EntityStatus;
  connection_status?: ConnectionStatus;
  search?: string;
  sort_by?: 'name' | 'created_at' | 'last_sync' | 'health_score';
  sort_order?: 'asc' | 'desc';
}

// Enum Options for UI
export interface EnumOption {
  value: string;
  label: string;
  description?: string;
  icon?: string;
}

export interface EntityEnums {
  entity_types: EnumOption[];
  entity_statuses: EnumOption[];
  connection_statuses: EnumOption[];
  sync_frequencies: EnumOption[];
  currencies: Array<{ value: string; label: string; symbol: string; }>;
  amortization_periods: Array<{ value: number; label: string; }>;
  sync_data_types: Array<{ value: string; label: string; description: string; }>;
}

// API Response Types
export interface CreateEntityResponse {
  message: string;
  entity_id: string;
  entity_name: string;
}

export interface UpdateEntityResponse {
  message: string;
}

export interface DeleteEntityResponse {
  message: string;
}

export interface InitiateConnectionResponse {
  authorization_url: string;
  state: string;
}

export interface CompleteConnectionResponse {
  message: string;
  entity_id: string;
  connection_status: ConnectionStatus;
} 