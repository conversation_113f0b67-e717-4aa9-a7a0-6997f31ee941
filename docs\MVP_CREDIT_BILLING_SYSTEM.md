# MVP Prepaid Credit Billing System - Developer Specification

## Business Logic

### Pricing Model
- **Standard Package**: $10 purchases 1,000 credits
- **Usage Rate**: 1 credit deducted per page processed
- **Free Trial**: New users receive 500 credits automatically
- **Processing Rule**: Block document processing when insufficient credits

### Credit Management
- Credits are managed at the **firm level** (per `FIRM_USERS` collection)
- Credits never expire
- Real-time balance tracking and updates
- Atomic credit deduction after successful document processing

## Database Schema Changes

### Users Collection Updates
Add fields to existing `FIRM_USERS` collection:
```javascript
{
  // Existing fields...
  credits_remaining: number,        // default: 500 for new users
  credits_total_purchased: number,  // default: 0
  created_at: timestamp,
  updated_at: timestamp
}
```

### New Collection: `credit_transactions`
```javascript
{
  transaction_id: string,           // Auto-generated document ID
  user_id: string,                  // Reference to FIRM_USERS document
  firm_id: string,                  // Reference to FIRMS document
  type: "purchase" | "usage",       // Transaction type
  amount: number,                   // Positive for purchase, negative for usage
  pages_processed: number,          // Usage transactions only
  document_name: string,            // Usage transactions only
  timestamp: Firestore.timestamp,   // Transaction timestamp
  remaining_balance: number,        // Balance after this transaction
  stripe_payment_id: string,        // Purchase transactions only
  metadata: {                       // Additional context
    processing_session_id: string,  // Link to document processing session
    entity_id: string,              // Entity where processing occurred
    client_id: string               // Client context
  }
}
```

## API Endpoints Required

### 1. GET /api/credits/balance
- **Auth**: Required (Firebase JWT)
- **Purpose**: Get user's current credit balance
- **Response**: 
```json
{
  "credits_remaining": 750,
  "credits_total_purchased": 1000,
  "last_updated": "2024-12-15T10:30:00Z"
}
```

### 2. POST /api/credits/purchase
- **Auth**: Required (Firebase JWT)
- **Purpose**: Initiate credit purchase via Stripe
- **Input**: 
```json
{
  "package_type": "standard",  // Only "standard" for MVP
  "success_url": "https://app.drcrlabs.com/billing?success=true",
  "cancel_url": "https://app.drcrlabs.com/billing?canceled=true"
}
```
- **Response**: 
```json
{
  "checkout_session_url": "https://checkout.stripe.com/pay/...",
  "session_id": "cs_test_..."
}
```

### 3. POST /api/webhooks/stripe
- **Auth**: Stripe signature verification
- **Purpose**: Handle successful payments
- **Action**: Add 1,000 credits to user account, log transaction
- **Idempotency**: Handle duplicate webhooks gracefully

### 4. GET /api/credits/usage
- **Auth**: Required (Firebase JWT)
- **Purpose**: Get recent credit transactions
- **Parameters**: 
  - `limit` (default: 10, max: 50)
  - `type` (optional: "purchase" | "usage")
- **Response**: Array of recent transactions with pagination

## Document Processing Integration

### Pre-Processing Requirements
1. **Page Counting**: Count pages in uploaded document (PDF/images)
2. **Credit Check**: Verify `user.credits_remaining >= page_count`
3. **Block Processing**: If insufficient credits, return error and block processing
4. **Continue Pipeline**: If sufficient credits, proceed with existing document processing

### Post-Processing Requirements
1. **Atomic Deduction**: Only deduct credits AFTER successful processing
2. **Transaction Logging**: Use Firestore transaction to update credits and log usage
3. **Update Records**: Update both `credits_remaining` and create `credit_transaction` record

### Error Handling
**Insufficient Credits Error Message**:
```json
{
  "error": "insufficient_credits",
  "message": "Insufficient credits. Need {required} credits, have {available} credits. Purchase more to continue.",
  "required_credits": 5,
  "available_credits": 2,
  "purchase_url": "/billing"
}
```

## Frontend Changes

### Header Component Updates
- Display current credit balance prominently
- Show warning indicator when credits < 50
- Link to billing page from credit display

### Enhanced Billing Page
Replace current preview billing page with functional credit management:

#### Components Required:
1. **Credit Balance Display**
   - Large, prominent credit number
   - Usage statistics (pages processed, credits purchased)
   - Low credit warning (when < 50 credits)

2. **Purchase Section**
   - "Buy 1,000 Credits - $10" button
   - Package details and benefits
   - Secure Stripe Checkout integration

3. **Usage History Table**
   - Last 10 transactions by default
   - Columns: Date/Time, Document Name (or "Credit Purchase"), Credits Used/Added, Remaining Balance
   - Pagination for full history

### Upload/Processing Components
- **Pre-upload Credit Check**: Verify credits before allowing document upload
- **Insufficient Credits UI**: Display error message with link to billing page
- **Processing Block**: Disable processing UI when credits too low

## Stripe Integration

### Required Setup
1. **Product Configuration**:
   - Create product: "DRCR Credits"
   - Create price: $10.00 USD (one-time payment)
   - Set product metadata: `credits=1000`

2. **Webhook Configuration**:
   - Configure webhook endpoint for `checkout.session.completed`
   - Use Stripe Checkout (hosted payment page)
   - Set up proper webhook signature verification

### Webhook Requirements
- Verify Stripe signature for security
- Extract `user_id` from checkout session metadata
- Add 1,000 credits to user account atomically
- Create purchase transaction record
- Handle idempotency for duplicate webhooks

## Security Requirements

### Credit Deduction Security
- Use Firestore transactions to prevent race conditions
- Verify credit balance immediately before deduction
- Log all credit changes with full audit trail
- Validate page count server-side (never trust client)

### Payment Processing Security
- Validate all Stripe webhooks with signature verification
- Store Stripe payment IDs for reconciliation
- Handle payment failures gracefully
- Use HTTPS for all payment-related endpoints

## Error Handling & Edge Cases

### Insufficient Credits
- Block processing before it starts
- Provide clear error message with current balance
- Direct user to billing page with purchase options

### Payment Failures
- Handle Stripe webhook failures with retry logic
- Retry failed credit additions with exponential backoff
- Log all payment processing errors for debugging

### Concurrent Processing
- Handle multiple simultaneous document uploads
- Prevent credit balance race conditions
- Ensure atomic credit deduction per processing session

### System Failures
- Handle payment success but webhook failure scenarios
- Implement credit reconciliation processes
- Monitor for credit balance inconsistencies

## Testing Requirements

### Functional Tests
- New user receives 500 free credits automatically
- Credit deduction works correctly per page processed
- Processing blocked when insufficient credits
- Stripe payment adds correct credits (1,000 for $10)
- Usage history displays accurately with proper pagination

### Integration Tests
- End-to-end payment flow from purchase to credit addition
- Webhook processing reliability under various conditions
- Concurrent document processing scenarios
- Credit balance consistency across multiple operations

### Security Tests
- Webhook signature verification
- Credit deduction race condition prevention
- Payment fraud prevention measures

## Performance Considerations

### Database Optimization
- Index `credit_transactions` by `user_id` and `timestamp`
- Limit usage history queries to prevent performance issues
- Use efficient Firestore transactions for credit operations
- Implement proper pagination for transaction history

### Real-time Updates
- Update credit balance in UI after processing completion
- Refresh balance after successful payment
- Handle loading states during credit operations
- Implement optimistic UI updates where appropriate

## Deployment Strategy

### Environment Setup
- Configure Stripe test/production keys securely
- Set up webhook endpoints with proper SSL certificates
- Configure CORS for Stripe integration
- Set up monitoring for webhook delivery

### Rollout Plan
1. Deploy behind feature flag for controlled testing
2. Test with internal users and small user group
3. Monitor credit transaction accuracy and webhook reliability
4. Gradual rollout to full user base with monitoring

### Monitoring & Alerting
- Track credit deduction accuracy and consistency
- Monitor Stripe webhook success rates (target: >99%)
- Alert on payment processing failures
- Log all credit-related operations for debugging and audit

## Success Criteria

### Technical Metrics
- Users can purchase credits without technical issues
- Credit deduction is accurate and atomic (100% consistency)
- No document processing occurs without sufficient credits
- Payment webhook reliability > 99%
- Credit balance displays correctly in real-time

### Business Metrics
- Successful payment conversion rate
- Average credits purchased per user
- Credit usage patterns and efficiency
- User satisfaction with billing transparency

### Performance Metrics
- Credit check response time < 100ms
- Payment processing completion time < 30 seconds
- Webhook processing time < 5 seconds
- UI responsiveness during credit operations

## Implementation Priority

### Phase 1: Core Credit System (Week 1)
- Database schema updates
- Credit management API endpoints
- Basic credit checking middleware

### Phase 2: Stripe Integration (Week 2)
- Stripe Checkout setup
- Webhook handling implementation
- Payment processing and credit addition

### Phase 3: Frontend Integration (Week 3)
- Enhanced billing page
- Credit display in header
- Document upload credit checking

### Phase 4: Testing & Polish (Week 4)
- Comprehensive testing suite
- Error handling refinement
- Performance optimization
- Production deployment

This specification provides a complete roadmap for implementing the MVP prepaid credit billing system while maintaining security, performance, and user experience standards.
