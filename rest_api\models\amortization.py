from pydantic import BaseModel, Field
from typing import List, Dict, Any
from datetime import date as date_type

class MonthlyTotal(BaseModel):
    """Model for a monthly total in the amortization report."""
    month: date_type = Field(..., description="Month (first day of the month)")
    amount: float = Field(..., description="Total amount for the month")
    status: str = Field(..., description="Status of the entries for this month")

class AmortizationTransaction(BaseModel):
    """Model for a transaction in the amortization report."""
    transaction_id: str = Field(..., description="Transaction ID")
    document_number: str = Field(..., description="Document number (e.g., invoice number)")
    date: date_type = Field(..., description="Transaction date")
    contact_name: str = Field(..., description="Contact name")
    total_amount: float = Field(..., description="Total transaction amount")
    remaining_amount: float = Field(..., description="Remaining amount to be amortized")
    status: str = Field(..., description="Transaction status")
    monthly_amounts: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="Monthly amortization amounts"
    )

class AmortizationReportResponse(BaseModel):
    """Response model for the amortization report endpoint."""
    monthly_totals: List[MonthlyTotal] = Field(
        default_factory=list,
        description="Monthly totals for all transactions"
    )
    transactions: List[AmortizationTransaction] = Field(
        default_factory=list,
        description="List of transactions with amortization details"
    )

    model_config = {
        "json_schema_extra": {
            "example": {
                "monthly_totals": [
                    {
                        "month": "2023-05-01",
                        "amount": 5000.00,
                        "status": "POSTED"
                    },
                    {
                        "month": "2023-06-01",
                        "amount": 5000.00,
                        "status": "PENDING"
                    }
                ],
                "transactions": [
                    {
                        "transaction_id": "transaction_123",
                        "document_number": "INV-001",
                        "date": "2023-05-15",
                        "contact_name": "Acme Corp",
                        "total_amount": 12000.00,
                        "remaining_amount": 10000.00,
                        "status": "ACTIVE",
                        "monthly_amounts": [
                            {
                                "month": "2023-05-01",
                                "amount": 1000.00,
                                "status": "POSTED"
                            },
                            {
                                "month": "2023-06-01",
                                "amount": 1000.00,
                                "status": "PENDING"
                            }
                        ]
                    }
                ]
            }
        }
    }
