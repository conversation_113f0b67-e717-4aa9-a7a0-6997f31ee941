# API Request Patterns

This document outlines standard patterns for making requests and interpreting responses from the DRCR API, particularly when dealing with collections of resources. These patterns cover pagination, filtering, and sorting to help you efficiently retrieve the data you need.

## Standard Response Structure for Collections

When you request a list of resources (e.g., `GET /api/v1/clients`), the API typically returns a JSON object that includes pagination details and an array of the resource items.

**Conceptual Example Response:**

```json
{
  "items": [
    {
      "client_id": "client_abc",
      "name": "Client Alpha",
      // ... other client fields ...
    },
    {
      "client_id": "client_xyz",
      "name": "Client Omega",
      // ... other client fields ...
    }
  ],
  "total_items": 50,
  "limit": 10,
  "offset": 0,
  "next_offset": 10, // Optional: direct pointer to the next offset
  "previous_offset": null // Optional: direct pointer to the previous offset
}
```

*   **`items`** (Array): An array of the resource objects for the current page.
*   **`total_items`** (Integer): The total number of items available across all pages for the given query.
*   **`limit`** (Integer): The maximum number of items requested per page.
*   **`offset`** (Integer): The starting point (0-indexed) of the items returned in this page.
*   **`next_offset`** (Integer | Null, Optional): The offset to use to request the next page of results. `null` if this is the last page.
*   **`previous_offset`** (Integer | Null, Optional): The offset to use to request the previous page of results. `null` if this is the first page.

*(Note: The exact pagination fields like `next_offset`/`previous_offset` or cursor-based fields might vary slightly based on specific endpoint implementations or if cursor-based pagination is used. Always check the OpenAPI/Swagger documentation for the precise response structure of each endpoint.)*

## Pagination

Pagination is used to break down large sets of data into smaller, more manageable chunks (pages). The DRCR API typically uses limit/offset based pagination.

*   **`limit`** (Query Parameter):
    *   Specifies the maximum number of items to return in a single response.
    *   Example: `GET /api/v1/transactions?limit=25`
    *   If not provided, a default limit (e.g., 10 or 20) will be applied.
    *   There might be a maximum allowed limit to prevent abuse.
*   **`offset`** (Query Parameter):
    *   Specifies the number of items to skip before starting to collect the result set.
    *   Example: `GET /api/v1/transactions?limit=25&offset=50` (retrieves items 51-75)
    *   If not provided, defaults to `0` (the beginning of the list).

To fetch subsequent pages, increment the `offset` by the `limit` value used in the previous request.

**Example: Fetching all transactions in pages of 10**
1.  `GET /api/v1/transactions?limit=10&offset=0` (gets items 1-10)
2.  `GET /api/v1/transactions?limit=10&offset=10` (gets items 11-20)
3.  `GET /api/v1/transactions?limit=10&offset=20` (gets items 21-30)
    ...and so on, until the `items` array in the response is empty or has fewer items than the `limit`.

## Filtering

Filtering allows you to retrieve a subset of resources based on specific criteria. Filters are generally applied using query parameters. The exact filterable fields and their parameter names will vary depending on the resource and will be detailed in the OpenAPI/Swagger documentation for each endpoint.

**Conceptual Examples:**

*   **Filter transactions by status**:
    `GET /api/v1/transactions?status=PAID`
*   **Filter clients by firm ID**:
    `GET /api/v1/clients?firm_id=some_firm_uuid`
*   **Filter transactions by date range (exact parameter names may vary)**:
    `GET /api/v1/transactions?date_issued_after=2023-01-01T00:00:00Z&date_issued_before=2023-01-31T23:59:59Z`
*   **Filter by multiple criteria**:
    `GET /api/v1/transactions?status=AUTHORISED&type=ACCPAY_INVOICE`

Refer to the specific endpoint documentation for available filter parameters.

## Sorting

Sorting allows you to order the returned list of resources based on one or more fields. Sorting is typically controlled by a `sort_by` query parameter.

*   **`sort_by`** (Query Parameter):
    *   Specifies the field to sort by.
    *   To indicate descending order, the field name is often prefixed with a hyphen (`-`). Ascending order is the default if no prefix is used.
    *   Example (sort transactions by issue date, newest first):
        `GET /api/v1/transactions?sort_by=-date_issued`
    *   Example (sort clients by name, alphabetically):
        `GET /api/v1/clients?sort_by=name`
*   **Multiple Sort Fields** (Support may vary):
    *   Some endpoints might allow sorting by multiple fields by providing a comma-separated list.
    *   Example: `GET /api/v1/transactions?sort_by=-status,date_issued` (Sort by status descending, then by issue date ascending)

The available sortable fields will be specified in the OpenAPI/Swagger documentation for each relevant endpoint.

---

By utilizing these request patterns—pagination, filtering, and sorting—you can tailor your API requests to retrieve precisely the data you need in an efficient manner. Always consult the specific endpoint documentation in OpenAPI/Swagger for the exact parameters and supported features. 