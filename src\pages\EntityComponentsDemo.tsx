import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ConnectNewEntityModal, EntitySettingsManagement } from '@/components/entities';
import type { Account, EntitySettingsData } from '@/components/entities';

export default function EntityComponentsDemo() {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isConnecting, setIsConnecting] = useState(false);
    const [connectionError, setConnectionError] = useState<string | null>(null);

    // Mock functions for ConnectNewEntityModal
    const handleInitiateConnection = (clientId: string, provider: 'xero' | 'qbo') => {
        setIsConnecting(true);
        setConnectionError(null);
        console.log(`Initiating connection for client ${clientId} with ${provider}...`);
        
        // Simulate API call
        setTimeout(() => {
            const success = Math.random() > 0.3;
            if (!success) {
                setConnectionError(`Failed to initiate ${provider} connection. Please try again.`);
            } else {
                setIsModalOpen(false);
                alert(`Successfully initiated ${provider} connection for client ${clientId}`);
            }
            setIsConnecting(false);
        }, 1500);
    };

    // Mock functions for EntitySettingsManagement
    const fetchSettingsMock = async (entityId: string): Promise<EntitySettingsData> => {
        console.log(`Fetching settings for ${entityId}...`);
        await new Promise(res => setTimeout(res, 500));
        return {
            entityId: entityId,
            entityName: "Xero - ABC Inc.",
            prepaymentAssetAccountCodes: ["620", "625"],
            defaultExpenseAccountCode: "750"
        };
    };

    const fetchChartOfAccountsMock = async (entityId: string): Promise<Account[]> => {
        console.log(`Fetching accounts for ${entityId}...`);
        await new Promise(res => setTimeout(res, 500));
        return [
            { code: '100', name: 'Cash', type: 'ASSET'},
            { code: '620', name: 'Prepaid Expenses - Software', type: 'ASSET'},
            { code: '621', name: 'Prepaid Expenses - Rent', type: 'ASSET'},
            { code: '625', name: 'Prepaid Insurance', type: 'ASSET'},
            { code: '1500', name: 'Equipment', type: 'ASSET'},
            { code: '750', name: 'Software Subscriptions', type: 'EXPENSE'},
            { code: '755', name: 'Cloud Services', type: 'EXPENSE'},
            { code: '780', name: 'Marketing Expenses', type: 'EXPENSE'},
            { code: '800', name: 'Rent Expense', type: 'EXPENSE'},
            { code: '810', name: 'Insurance Expense', type: 'EXPENSE'},
            { code: '900', name: 'Utilities', type: 'EXPENSE'},
            { code: '2000', name: 'Accounts Payable', type: 'LIABILITY'},
            { code: '4000', name: 'Sales Revenue', type: 'REVENUE'},
        ];
    };

    const saveSettingsMock = async (entityId: string, settings: Omit<EntitySettingsData, 'entityId' | 'entityName'>): Promise<void> => {
        console.log(`Saving settings for ${entityId}:`, settings);
        await new Promise(res => setTimeout(res, 1000));
        
        // Simulate potential error
        if (Math.random() < 0.2) {
            throw new Error("Simulated API error: Could not save settings.");
        }
        console.log("Settings saved successfully via mock API.");
    };

    const mockInitiateConnection = async (clientId: string, provider: string): Promise<void> => {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve();
            }, 2000);
        });
    };

    const mockFetchEntitySettings = async (entityId: string): Promise<EntitySettingsData> => {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    entityId,
                    entityName: `Entity ${entityId}`,
                    prepaymentAssetAccountCodes: ['1200', '1300'],
                    defaultExpenseAccountCode: '5000'
                });
            }, 1000);
        });
    };

    const mockFetchEntityAccounts = async (entityId: string): Promise<Account[]> => {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve([
                    { code: '1200', name: 'Prepaid Expenses', type: 'ASSET' },
                    { code: '1300', name: 'Other Prepaid Assets', type: 'ASSET' },
                    { code: '5000', name: 'General Expenses', type: 'EXPENSE' },
                    { code: '5100', name: 'Software Expenses', type: 'EXPENSE' },
                    { code: '5200', name: 'Marketing Expenses', type: 'EXPENSE' }
                ]);
            }, 800);
        });
    };

    const mockSaveEntitySettings = async (entityId: string, settings: EntitySettingsData): Promise<void> => {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve();
            }, 1500);
        });
    };

    return (
        <div className="min-h-screen bg-gray-50 p-8">
            <div className="max-w-4xl mx-auto space-y-8">
                <div className="text-center">
                    <h1 className="text-3xl font-bold text-gray-900">Entity Components Demo</h1>
                    <p className="text-gray-600 mt-2">Demonstration of the ConnectNewEntityModal and EntitySettingsManagement components</p>
                </div>

                <Separator />

                {/* Connect New Entity Modal Demo */}
                <Card>
                    <CardHeader>
                        <CardTitle>Connect New Entity Modal</CardTitle>
                        <CardDescription>
                            Modal for connecting new Xero or QuickBooks Online entities to clients
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Button 
                            onClick={() => setIsModalOpen(true)}
                            className="mb-4"
                        >
                            Open Connect Entity Modal
                        </Button>
                        
                        <ConnectNewEntityModal
                            isOpen={isModalOpen}
                            onClose={() => {
                                setIsModalOpen(false);
                                setConnectionError(null);
                                setIsConnecting(false);
                            }}
                            clientId="client-demo-123"
                            clientName="Demo Business Solutions"
                            onInitiateConnection={handleInitiateConnection}
                            isConnecting={isConnecting}
                            connectionError={connectionError}
                        />
                    </CardContent>
                </Card>

                <Separator />

                {/* Entity Settings Management Demo */}
                <Card>
                    <CardHeader>
                        <CardTitle>Entity Settings Management</CardTitle>
                        <CardDescription>
                            Component for configuring prepayment and expense accounts for entities
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <EntitySettingsManagement
                            entityId="entity-demo-456"
                            fetchSettings={fetchSettingsMock}
                            fetchChartOfAccounts={fetchChartOfAccountsMock}
                            saveSettings={saveSettingsMock}
                        />
                    </CardContent>
                </Card>
            </div>
        </div>
    );
} 