import * as React from "react"
import { useNavigate, useLocation } from "react-router-dom"
import {
  Calculator,
  Settings,
  LogOut,
} from "lucide-react"

import { useAuthStore } from "../../store/auth.store"
import { NavMain } from "./NavMain"
import { NavUser } from "./NavUser"
import { toast } from "sonner"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from "../ui/sidebar"

// Simplified navigation structure for DRCR
const navMainItems = [
  {
    title: "Prepayments",
    url: "/prepayments",
    icon: Calculator,
    items: [
      {
        title: "View All",
        url: "/prepayments",
      },
      {
        title: "Add New",
        url: "/prepayments/new",
      },
      {
        title: "Import",
        url: "/prepayments/import",
      },
    ],
  },
  {
    title: "Settings",
    url: "/settings",
    icon: Settings,
    items: [
      {
        title: "General",
        url: "/settings/general",
      },
      {
        title: "Users",
        url: "/settings/users",
      },
      {
        title: "Integrations",
        url: "/settings/integrations",
      },
    ],
  },
]

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const navigate = useNavigate()
  const location = useLocation()
  const { user, signOut } = useAuthStore()

  const handleSignOut = async () => {
    try {
      await signOut()
      toast.success('Logged out successfully')
      navigate('/login')
    } catch (error: any) {
      toast.error(error.message || 'Failed to log out')
    }
  }

  // Get user initials for avatar
  const getInitials = () => {
    if (user?.displayName) {
      return user.displayName
        .split(' ')
        .map(name => name[0])
        .join('')
        .toUpperCase()
    }
    return user?.email?.[0].toUpperCase() || 'U'
  }

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader className="border-b border-sidebar-border p-4">
        {/* Logo - Clickable to navigate to dashboard */}
        <div className="flex justify-center items-center">
          <button
            onClick={() => navigate('/dashboard')}
            className="h-12 w-12 rounded-lg hover:bg-sidebar-accent transition-colors duration-200 flex items-center justify-center group"
            title="Go to Dashboard"
          >
            <img
              src="/logo.png"
              alt="DRCR Logo"
              className="h-10 w-10 object-contain group-hover:scale-105 transition-transform duration-200"
            />
          </button>
        </div>
      </SidebarHeader>

      <SidebarContent className="px-3 py-4">
        {/* Main Navigation */}
        <NavMain items={navMainItems} />
      </SidebarContent>

      <SidebarFooter className="border-t border-sidebar-border p-3">
        {/* User Profile */}
        <NavUser user={{
          name: user?.displayName || 'User',
          email: user?.email || '<EMAIL>',
          avatar: '', // No avatar for now
        }} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
