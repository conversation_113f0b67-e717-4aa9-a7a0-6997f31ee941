# Router Configuration Guide

This guide explains how routers are configured in the DRCR API, including best practices for defining routes and organizing endpoints.

## Router Structure

The DRCR API uses FastAPI's `APIRouter` to organize endpoints into logical groups. Each router corresponds to a specific resource or functional area of the application.

### Directory Structure

```
rest_api/
├── app.py                # Main application entry point
├── routes/
│   ├── __init__.py       # Makes routes a package
│   ├── auth.py           # Authentication routes
│   ├── clients.py        # Client management routes
│   ├── entities.py       # Entity management routes
│   ├── invoices.py       # Invoice-related routes
│   ├── reports.py        # Reporting routes
│   ├── schedules.py      # Amortization schedule routes
│   ├── transactions.py   # Transaction routes
│   └── xero.py           # Xero integration routes
└── ...
```

## Router Definition Best Practices

### 1. Define Routers Without Prefixes in Route Files

In each route file (e.g., `routes/transactions.py`), define the router **without** a prefix:

```python
# CORRECT: In routes/transactions.py
from fastapi import APIRouter

# Define router without prefix
router = APIRouter(tags=["Transactions"])

@router.get("/{transaction_id}")
async def get_transaction(transaction_id: str):
    # This will be accessible at /transactions/{transaction_id}
    # NOT at /transactions/transactions/{transaction_id}
    ...
```

❌ **Incorrect Approach**:
```python
# INCORRECT: In routes/transactions.py
router = APIRouter(prefix="/transactions", tags=["Transactions"])
```

### 2. Apply Prefixes When Including Routers in app.py

In the main `app.py` file, include routers with their respective prefixes:

```python
# In app.py
from fastapi import FastAPI
from .routes import auth, clients, entities, transactions, xero

app = FastAPI()

# Include routers with prefixes
app.include_router(auth.router, prefix="/auth", tags=["Authentication"])
app.include_router(clients.router, prefix="/clients", tags=["Clients"])
app.include_router(entities.router, prefix="/entities", tags=["Entities"])
app.include_router(transactions.router, prefix="/transactions", tags=["Transactions"])
app.include_router(xero.router, prefix="/xero", tags=["Xero Integration"])
```

### 3. Avoid Duplicate Prefixes

A common mistake is defining prefixes in both the router file and when including the router, which leads to duplicate prefixes in URL paths:

❌ **Incorrect (leads to `/transactions/transactions/{id}`)**:
```python
# In routes/transactions.py
router = APIRouter(prefix="/transactions", tags=["Transactions"])

# In app.py
app.include_router(transactions.router, prefix="/transactions", tags=["Transactions"])
```

✅ **Correct (results in `/transactions/{id}`)**:
```python
# In routes/transactions.py
router = APIRouter(tags=["Transactions"])

# In app.py
app.include_router(transactions.router, prefix="/transactions", tags=["Transactions"])
```

#### Real-World Example: Reports Router

In our project, we identified and fixed this issue in the reports router:

❌ **Before (led to `/reports/reports/dashboard`)**:
```python
# In routes/reports.py
router = APIRouter(prefix="/reports", tags=["Reports"])

# In app.py
app.include_router(reports.router, prefix="/reports", tags=["Reports"])
```

✅ **After (results in `/reports/dashboard`)**:
```python
# In routes/reports.py
router = APIRouter(tags=["Reports"])

# In app.py
app.include_router(reports.router, prefix="/reports", tags=["Reports"])
```

We verified this fix by creating integration tests that confirm the endpoints are accessible at the correct paths:

```python
# In tests/integration/routes/test_reports_api.py
def test_get_dashboard_data(self):
    """Test GET /reports/dashboard endpoint."""
    # ... setup code ...

    # API call to the correct path
    response = self.client.get("/reports/dashboard?client_id=test_client_id")

    # Verify the response
    self.assertEqual(response.status_code, status.HTTP_200_OK)
    # ... additional assertions ...
```

The tests pass, confirming that the endpoints are accessible at `/reports/dashboard` and `/reports/amortization` (not `/reports/reports/dashboard` and `/reports/reports/amortization`).

## Route Organization

### Group Routes by Resource

Routes should be organized by resource or domain concept:

- `auth.py`: Authentication and user management
- `clients.py`: Client CRUD operations
- `entities.py`: Entity management
- `transactions.py`: Transaction-related endpoints
- etc.

### Route Naming Conventions

Follow these conventions for route paths:

1. **Collection endpoints**: Use plural nouns
   - `GET /clients` - List clients
   - `POST /clients` - Create a new client

2. **Singular resource endpoints**: Use plural collection name with ID
   - `GET /clients/{client_id}` - Get a specific client
   - `PUT /clients/{client_id}` - Update a client
   - `DELETE /clients/{client_id}` - Delete a client

3. **Sub-resources**: Nest under the parent resource
   - `GET /clients/{client_id}/entities` - List entities for a client
   - `GET /transactions/{transaction_id}/attachments` - Get attachments for a transaction

4. **Actions**: Use verbs for operations that don't fit CRUD
   - `POST /entities/{entity_id}/sync` - Trigger synchronization
   - `POST /auth/reset-password` - Reset a password

## Testing Routes

When writing tests for routes, ensure you're using the correct URL paths:

```python
# In tests/integration/routes/test_transactions_api.py
def test_get_transaction(self):
    # Use the correct path without duplicate prefixes
    response = self.client.get("/transactions/transaction_123")
    self.assertEqual(response.status_code, 200)
```

## Swagger Documentation

The API's Swagger documentation is automatically generated based on the router configuration. To ensure accurate documentation:

1. Add proper docstrings to route functions
2. Use appropriate response models
3. Include example values where helpful

Access the Swagger UI at `/docs` when running the API server.

## Conclusion

Following these router configuration best practices ensures consistent, clean URL paths throughout the API and prevents common issues like duplicate prefixes. It also makes the API more intuitive for consumers and easier to maintain for developers.
