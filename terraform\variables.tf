variable "project_id" {
  description = "The Google Cloud project ID"
  type        = string
}

variable "region" {
  description = "The Google Cloud region"
  type        = string
  default     = "us-central1"
}

variable "xero_client_id" {
  description = "Xero API client ID"
  type        = string
  sensitive   = true
}

variable "xero_client_secret" {
  description = "Xero API client secret"
  type        = string
  sensitive   = true
}

variable "xero_redirect_uri" {
  description = "Xero API redirect URI"
  type        = string
}

variable "api_secret_key" {
  description = "Secret key for the REST API"
  type        = string
  sensitive   = true
}

variable "api_domain" {
  description = "Domain for the REST API"
  type        = string
  default     = ""
}
