from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
from datetime import date, datetime
from enum import Enum

class XeroInvoiceStatus(str, Enum):
    """Enum for Xero invoice status."""
    DRAFT = "DRAFT"
    SUBMITTED = "SUBMITTED"
    AUTHORISED = "AUTHORISED"
    PAID = "PAID"
    VOIDED = "VOIDED"
    DELETED = "DELETED"

class XeroInvoiceType(str, Enum):
    """Enum for Xero invoice type."""
    ACCREC = "ACCREC"  # Accounts Receivable (Invoice)
    ACCPAY = "ACCPAY"  # Accounts Payable (Bill)

class XeroContact(BaseModel):
    """Model for a Xero contact."""
    ContactID: str
    Name: str
    EmailAddress: Optional[str] = None
    Phones: Optional[List[Dict[str, Any]]] = None
    Addresses: Optional[List[Dict[str, Any]]] = None
    
    class Config:
        extra = "allow"

class XeroLineItem(BaseModel):
    """Model for a Xero invoice line item."""
    Description: Optional[str] = None
    Quantity: Optional[float] = None
    UnitAmount: Optional[float] = None
    TaxAmount: Optional[float] = None
    LineAmount: Optional[float] = None
    AccountCode: Optional[str] = None
    TaxType: Optional[str] = None
    ItemCode: Optional[str] = None
    
    class Config:
        extra = "allow"

class XeroInvoice(BaseModel):
    """Model for a Xero invoice."""
    InvoiceID: str
    Type: XeroInvoiceType
    Contact: XeroContact
    Date: Optional[str] = None
    DueDate: Optional[str] = None
    Status: XeroInvoiceStatus
    LineAmountTypes: Optional[str] = None
    LineItems: List[XeroLineItem]
    SubTotal: Optional[float] = None
    TotalTax: Optional[float] = None
    Total: Optional[float] = None
    CurrencyCode: Optional[str] = None
    InvoiceNumber: Optional[str] = None
    Reference: Optional[str] = None
    AmountDue: Optional[float] = None
    AmountPaid: Optional[float] = None
    UpdatedDateUTC: Optional[str] = None
    
    class Config:
        extra = "allow"

class XeroInvoices(BaseModel):
    """Model for a list of Xero invoices."""
    Invoices: List[XeroInvoice]
    
    class Config:
        extra = "allow"

class XeroAccount(BaseModel):
    """Model for a Xero account."""
    AccountID: str
    Code: str
    Name: str
    Type: str
    TaxType: Optional[str] = None
    Description: Optional[str] = None
    
    class Config:
        extra = "allow"

class XeroAccounts(BaseModel):
    """Model for a list of Xero accounts."""
    Accounts: List[XeroAccount]
    
    class Config:
        extra = "allow"

class XeroTaxRate(BaseModel):
    """Model for a Xero tax rate."""
    Name: str
    TaxType: str
    DisplayTaxRate: float
    EffectiveRate: float
    
    class Config:
        extra = "allow"

class XeroTaxRates(BaseModel):
    """Model for a list of Xero tax rates."""
    TaxRates: List[XeroTaxRate]
    
    class Config:
        extra = "allow"
