from fastapi import APIRouter, Depends, Query
from typing import Optional

from ..core.firebase_auth import get_current_user, get_firm_user_with_client_access, AuthUser
from ..dependencies import get_db
from ..models.dashboard import DashboardResponse
from ..models.amortization import AmortizationReportResponse

router = APIRouter(tags=["Reports"])

@router.get("/dashboard", response_model=DashboardResponse)
async def get_dashboard_data(
    client_id: str = Query(..., description="Client ID"),
    entity_id: Optional[str] = Query(None, description="Entity ID (optional)"),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """
    Get dashboard data for a client/entity.

    This endpoint returns summary information about transactions for a client,
    including counts and totals for pending review, approved, and current month
    transactions, as well as a list of recent transactions.

    - **client_id**: ID of the client to get dashboard data for
    - **entity_id**: (Optional) ID of a specific entity within the client
    """
    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)

    # Placeholder for dashboard data
    return {
        "pending_review": {
            "count": 0,
            "total_amount": 0
        },
        "approved": {
            "count": 0,
            "total_amount": 0
        },
        "this_month": {
            "count": 0,
            "total_amount": 0
        },
        "recent_transactions": []
    }

@router.get("/amortization", response_model=AmortizationReportResponse)
async def get_amortization_report(
    client_id: str = Query(..., description="Client ID"),
    entity_id: Optional[str] = Query(None, description="Entity ID (optional)"),
    year: Optional[int] = Query(None, description="Filter by year"),
    month: Optional[int] = Query(None, description="Filter by month"),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """
    Get amortization report data for a client/entity.

    This endpoint returns detailed information about amortization schedules
    for a client, including monthly totals and transaction-level details.

    - **client_id**: ID of the client to get amortization data for
    - **entity_id**: (Optional) ID of a specific entity within the client
    - **year**: (Optional) Filter by year (e.g., 2023)
    - **month**: (Optional) Filter by month (1-12)
    """
    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)

    # Placeholder for amortization report data
    return {
        "monthly_totals": [],
        "transactions": []
    }