# Password Recovery with SendGrid Integration

## Overview

This document outlines the implementation of password recovery functionality for the DRCR application using SendGrid for email delivery. The system allows users to request password reset emails and securely reset their passwords through a token-based flow.

## Architecture

### Components
1. **Backend API Endpoints**: Handle password reset requests and token validation
2. **SendGrid Integration**: Email delivery service for reset emails
3. **Frontend Components**: Password reset request and reset forms
4. **Firebase Authentication**: Handles the actual password update
5. **Firestore Storage**: Stores password reset tokens with expiration

## Database Schema

### New Collection: `password_reset_tokens`
```javascript
{
  token_id: string,              // Auto-generated document ID (UUID)
  user_id: string,               // Firebase user UID
  email: string,                 // User's email address
  token: string,                 // Secure random token (hashed)
  expires_at: timestamp,         // Token expiration (24 hours from creation)
  used: boolean,                 // Whether token has been used
  created_at: timestamp,         // Token creation time
  used_at: timestamp,            // When token was used (if applicable)
  ip_address: string,            // IP address of requester (for security)
  user_agent: string             // User agent of requester (for security)
}
```

## API Endpoints

### 1. POST /api/auth/forgot-password
Request a password reset email.

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response (Success):**
```json
{
  "message": "If an account with this email exists, a password reset link has been sent.",
  "email": "<EMAIL>"
}
```

**Response (Error):**
```json
{
  "error": "invalid_email",
  "message": "Please provide a valid email address."
}
```

### 2. POST /api/auth/reset-password
Reset password using a valid token.

**Request Body:**
```json
{
  "token": "secure-reset-token",
  "new_password": "newSecurePassword123"
}
```

**Response (Success):**
```json
{
  "message": "Password has been reset successfully."
}
```

**Response (Error):**
```json
{
  "error": "invalid_token",
  "message": "Invalid or expired reset token."
}
```

### 3. GET /api/auth/verify-reset-token/{token}
Verify if a reset token is valid (for frontend validation).

**Response (Valid Token):**
```json
{
  "valid": true,
  "email": "<EMAIL>",
  "expires_at": "2024-12-16T10:30:00Z"
}
```

**Response (Invalid Token):**
```json
{
  "valid": false,
  "error": "Token is invalid or expired"
}
```

## SendGrid Configuration

### Environment Variables
```env
# SendGrid Configuration
SENDGRID_API_KEY=SG.your-sendgrid-api-key
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=DRCR Labs
SENDGRID_TEMPLATE_ID_PASSWORD_RESET=d-your-template-id

# Application URLs
FRONTEND_BASE_URL=https://app.drcrlabs.com
PASSWORD_RESET_URL_PATH=/reset-password
```

### Email Template
SendGrid dynamic template with the following variables:
- `{{user_name}}`: User's display name or email
- `{{reset_link}}`: Complete password reset URL
- `{{expiry_time}}`: Token expiration time (24 hours)
- `{{company_name}}`: DRCR Labs
- `{{support_email}}`: <EMAIL>

**Template Content:**
```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Password Reset - DRCR Labs</title>
</head>
<body>
    <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
        <h2>Password Reset Request</h2>
        
        <p>Hello {{user_name}},</p>
        
        <p>We received a request to reset your password for your DRCR Labs account. If you made this request, click the button below to reset your password:</p>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="{{reset_link}}" style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">Reset Password</a>
        </div>
        
        <p>This link will expire in {{expiry_time}}.</p>
        
        <p>If you didn't request a password reset, you can safely ignore this email. Your password will not be changed.</p>
        
        <p>If you're having trouble clicking the button, copy and paste the following URL into your browser:</p>
        <p style="word-break: break-all; color: #666;">{{reset_link}}</p>
        
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
        
        <p style="color: #666; font-size: 12px;">
            This email was sent by {{company_name}}. If you have questions, contact us at {{support_email}}.
        </p>
    </div>
</body>
</html>
```

## Security Features

### Token Security
- **Cryptographically Secure**: Tokens generated using `secrets.token_urlsafe(32)`
- **Hashed Storage**: Tokens stored as SHA-256 hashes in database
- **Time-Limited**: 24-hour expiration window
- **Single Use**: Tokens invalidated after successful password reset
- **Rate Limiting**: Maximum 3 reset requests per email per hour

### Additional Security
- **IP Address Logging**: Track request origins for security monitoring
- **User Agent Logging**: Additional request fingerprinting
- **Email Obfuscation**: Don't reveal if email exists in system
- **Secure Headers**: HTTPS-only, secure cookie settings

## Implementation Details

### Backend Dependencies
Add to `rest_api/requirements.txt`:
```
sendgrid==6.10.0
```

### Frontend Dependencies
No additional dependencies required - uses existing React Router and form libraries.

### Error Handling
- **Rate Limiting**: "Too many reset requests. Please try again later."
- **Invalid Email**: "Please provide a valid email address."
- **Expired Token**: "This reset link has expired. Please request a new one."
- **Used Token**: "This reset link has already been used."
- **Network Errors**: "Unable to send reset email. Please try again."

## Frontend Components

### 1. Forgot Password Page (`/forgot-password`)
- Email input form
- Submit button with loading state
- Success/error message display
- Link back to login page

### 2. Reset Password Page (`/reset-password/:token`)
- Token validation on page load
- New password input with strength indicator
- Confirm password field
- Submit button with loading state
- Success redirect to login

### 3. Navigation Updates
- Add "Forgot Password?" link to login page
- Add success message after password reset

## Testing Strategy

### Unit Tests
- Token generation and validation
- Email sending functionality
- Password reset flow
- Security validations

### Integration Tests
- End-to-end password reset flow
- Email delivery verification
- Token expiration handling
- Rate limiting enforcement

### Manual Testing
1. Request password reset for existing user
2. Verify email delivery and content
3. Click reset link and verify token validation
4. Reset password successfully
5. Verify old password no longer works
6. Test expired token handling
7. Test used token handling
8. Test rate limiting

## Monitoring and Analytics

### Metrics to Track
- Password reset request volume
- Email delivery success rate
- Token usage rate (successful resets)
- Failed reset attempts
- Time between request and reset

### Logging
- All password reset requests (with IP/user agent)
- Email sending success/failure
- Token validation attempts
- Successful password resets

## Deployment Checklist

### Backend
- [ ] Add SendGrid API key to environment variables
- [ ] Deploy updated API endpoints
- [ ] Configure SendGrid email template
- [ ] Set up monitoring for email delivery
- [ ] Test email delivery in production

### Frontend
- [ ] Deploy forgot password page
- [ ] Deploy reset password page
- [ ] Update login page with forgot password link
- [ ] Test complete flow in production
- [ ] Verify email links work with production URLs

### Security
- [ ] Verify HTTPS enforcement
- [ ] Test rate limiting
- [ ] Validate token security
- [ ] Review email template for security
- [ ] Set up monitoring for suspicious activity

## Future Enhancements

1. **Multi-language Support**: Localized email templates
2. **SMS Reset Option**: Alternative to email-based reset
3. **Account Lockout**: Temporary lockout after multiple failed attempts
4. **Admin Notifications**: Alert admins of suspicious reset activity
5. **Password History**: Prevent reuse of recent passwords
6. **Two-Factor Authentication**: Additional security layer

## Support and Troubleshooting

### Common Issues
1. **Emails not delivered**: Check SendGrid API key and template ID
2. **Reset links not working**: Verify frontend URL configuration
3. **Tokens expiring too quickly**: Check server time synchronization
4. **Rate limiting too aggressive**: Adjust limits based on usage patterns

### Support Contacts
- **Technical Issues**: <EMAIL>
- **SendGrid Issues**: Check SendGrid dashboard and logs
- **Security Concerns**: <EMAIL> 