#!/bin/sh
# Script for building backend Docker image

echo "Current directory: $(pwd)"
echo "Listing root directory:"
ls -la
mkdir -p backend
cp -r app/* backend/ || true
cp -r rest_api/* backend/ || true
cp requirements.txt backend/ || true
cp Dockerfile backend/ || true
cp app/Dockerfile backend/ || true
ls -la backend/
cd backend
echo "🐳 Building backend Docker image..."
docker build -t $CI_REGISTRY_IMAGE/backend:$CI_COMMIT_SHA .
docker build -t $CI_REGISTRY_IMAGE/backend:latest .
echo "✅ Backend Docker image built"