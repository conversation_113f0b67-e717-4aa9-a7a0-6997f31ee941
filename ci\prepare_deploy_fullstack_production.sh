#!/bin/sh
# Script for preparing fullstack deployment environment

echo "Current directory: $(pwd)"
echo "Listing root directory:"
ls -la
echo "Checking for app directory:"
ls -la app || echo "app directory not found"
echo "Checking for app/ci directory:"
ls -la app/ci || echo "app/ci directory not found"
echo "Checking for setup_gcp_auth.sh:"
ls -la app/ci/setup_gcp_auth.sh || echo "setup_gcp_auth.sh not found"
echo "Checking for setup_firebase_config.sh:"
ls -la app/ci/setup_firebase_config.sh || echo "setup_firebase_config.sh not found"
echo "Checking for deploy_fullstack_production.sh:"
ls -la app/ci/deploy_fullstack_production.sh || echo "deploy_fullstack_production.sh not found"
chmod +x app/ci/setup_gcp_auth.sh || echo "Failed to make setup_gcp_auth.sh executable"
chmod +x app/ci/setup_firebase_config.sh || echo "Failed to make setup_firebase_config.sh executable"
chmod +x app/ci/deploy_fullstack_production.sh || echo "Failed to make deploy_fullstack_production.sh executable"
./app/ci/setup_gcp_auth.sh || echo "Failed to run setup_gcp_auth.sh"
./app/ci/setup_firebase_config.sh || echo "Failed to run setup_firebase_config.sh"
apk add --no-cache nodejs npm