#!/usr/bin/env python3
"""
Test script to demonstrate Python module import behavior.
This shows that module-level code runs only ONCE per Python process.
"""

print("=== Testing Python Import Behavior ===")

print("\n1. First import of document_processor:")
from drcr_shared_logic.document_processor import extract_data_with_openai_direct
print("   First import completed")

print("\n2. Second import of document_processor (different function):")
from drcr_shared_logic.document_processor import process_pdf_with_mistral_ocr
print("   Second import completed")

print("\n3. Third import of document_processor (same function again):")
from drcr_shared_logic.document_processor import extract_data_with_openai_direct
print("   Third import completed")

print("\n4. Import using different syntax:")
import drcr_shared_logic.document_processor as doc_proc
print("   Fourth import completed")

print("\n=== Notice: DEBUG_DP_INIT messages should only appear ONCE ===")
print("This proves module-level code runs only on first import!")
