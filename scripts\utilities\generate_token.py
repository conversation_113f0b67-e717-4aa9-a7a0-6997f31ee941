import firebase_admin
from firebase_admin import auth, credentials
import json
import requests
import os

# Path to your Firebase credentials file
CREDENTIALS_PATH = "drcr-d660a-firebase-adminsdk-fbsvc-f1d2dc57df.json"

# Firebase Web API Key (you'll need to get this from your Firebase project settings)
# Go to Project Settings > General > Web API Key
FIREBASE_WEB_API_KEY = input("Enter your Firebase Web API Key: ")

# User UID (you can get this from the Firebase console or from your database)
# If you don't have a specific user in mind, you can create one or use an existing one
USER_UID = input("Enter the User UID (or press Enter to use a default): ")
if not USER_UID:
    USER_UID = "bm43PZwmVAYFa1ifcSSlSS0nkIO2"  # Replace with a known user ID if you have one

try:
    # Initialize Firebase Admin SDK
    cred = credentials.Certificate(CREDENTIALS_PATH)
    firebase_admin.initialize_app(cred)
    print("Firebase Admin SDK initialized successfully")

    # Create a custom token
    custom_token = auth.create_custom_token(USER_UID).decode('utf-8')
    print(f"\nCustom token generated: {custom_token}")

    # Exchange custom token for ID token
    print("\nExchanging custom token for ID token...")
    response = requests.post(
        f"https://identitytoolkit.googleapis.com/v1/accounts:signInWithCustomToken",
        params={"key": FIREBASE_WEB_API_KEY},
        json={"token": custom_token, "returnSecureToken": True}
    )

    if response.status_code == 200:
        id_token = response.json()["idToken"]
        print("\n=== ID TOKEN ===")
        print(id_token)
        print("===============")
        
        # Save to a file
        with open("firebase_id_token_clean.txt", "w") as f:
            f.write(id_token)
        print("\nToken saved to firebase_id_token_clean.txt")
        
        # Get user info
        user = auth.get_user(USER_UID)
        print(f"\nUser info: {user.email} (UID: {user.uid})")
        
    else:
        print(f"Error exchanging token: {response.status_code}")
        print(response.text)

except Exception as e:
    print(f"Error: {e}")
