#!/usr/bin/env python3
"""
Test script to send a real password reset email using SendGrid.

This script will send an actual email to test the SendGrid integration.
"""

import os
import sys
import asyncio
import logging
from datetime import datetime

# Add the rest_api directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'rest_api'))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def send_test_password_reset_email():
    """Send a test password reset <NAME_EMAIL>"""
    
    try:
        # Check if SendGrid API key is set
        api_key = os.getenv('SENDGRID_API_KEY')
        if not api_key:
            logger.error("SENDGRID_API_KEY environment variable not set")
            return False
        
        # Check if template ID is set
        template_id = os.getenv('SENDGRID_TEMPLATE_ID_PASSWORD_RESET')
        if not template_id:
            logger.warning("SENDGRID_TEMPLATE_ID_PASSWORD_RESET not set, will use a simple HTML email instead")
        
        from rest_api.services.email_service import EmailService
        
        # Create email service instance
        email_service = EmailService()
        
        # Test email details
        test_email = "<EMAIL>"
        test_user_name = "Test User"
        test_reset_link = "https://app.drcrlabs.com/reset-password?token=test_token_123456789"
        
        logger.info(f"Sending test password reset email to {test_email}...")
        
        if template_id:
            # Use SendGrid template
            success = await email_service.send_password_reset_email(
                to_email=test_email,
                user_name=test_user_name,
                reset_link=test_reset_link,
                expiry_time="24 hours"
            )
        else:
            # Use custom HTML email
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>Password Reset - DRCR Labs</title>
            </head>
            <body>
                <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
                    <h2>Password Reset Request</h2>
                    
                    <p>Hello {test_user_name},</p>
                    
                    <p>We received a request to reset your password for your DRCR Labs account. If you made this request, click the button below to reset your password:</p>
                    
                    <div style="text-align: center; margin: 30px 0;">
                        <a href="{test_reset_link}" style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">Reset Password</a>
                    </div>
                    
                    <p>This link will expire in 24 hours.</p>
                    
                    <p>If you didn't request a password reset, you can safely ignore this email. Your password will not be changed.</p>
                    
                    <p>If you're having trouble clicking the button, copy and paste the following URL into your browser:</p>
                    <p style="word-break: break-all; color: #666;">{test_reset_link}</p>
                    
                    <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
                    
                    <p style="color: #666; font-size: 12px;">
                        This email was sent by DRCR Labs. If you have questions, contact <NAME_EMAIL>.
                    </p>
                    
                    <p style="color: #666; font-size: 12px;">
                        <strong>Note:</strong> This is a test email sent at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} to verify the password reset functionality.
                    </p>
                </div>
            </body>
            </html>
            """
            
            plain_text = f"""
            Password Reset Request
            
            Hello {test_user_name},
            
            We received a request to reset your password for your DRCR Labs account.
            
            Reset your password by visiting this link:
            {test_reset_link}
            
            This link will expire in 24 hours.
            
            If you didn't request a password reset, you can safely ignore this email.
            
            This is a test email sent at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} to verify the password reset functionality.
            
            DRCR Labs
            """
            
            success = await email_service.send_custom_email(
                to_email=test_email,
                subject="Password Reset - DRCR Labs (Test)",
                html_content=html_content,
                plain_text_content=plain_text
            )
        
        if success:
            logger.info(f"✅ Test email sent successfully to {test_email}")
            logger.info("Check your inbox for the password reset email")
            return True
        else:
            logger.error(f"❌ Failed to send test email to {test_email}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error sending test email: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main function"""
    
    logger.info("DRCR Password Reset Email Test")
    logger.info("=" * 50)
    
    # Check environment variables
    logger.info("Checking environment variables...")
    
    api_key = os.getenv('SENDGRID_API_KEY')
    if api_key:
        logger.info(f"✅ SENDGRID_API_KEY: {api_key[:10]}...")
    else:
        logger.error("❌ SENDGRID_API_KEY not set")
        return False
    
    from_email = os.getenv('SENDGRID_FROM_EMAIL', '<EMAIL>')
    logger.info(f"📧 From email: {from_email}")
    
    template_id = os.getenv('SENDGRID_TEMPLATE_ID_PASSWORD_RESET')
    if template_id:
        logger.info(f"📋 Template ID: {template_id}")
    else:
        logger.warning("⚠️  No template ID set, will use custom HTML")
    
    logger.info("\nSending test email...")
    success = await send_test_password_reset_email()
    
    if success:
        logger.info("\n🎉 Test completed successfully!")
        logger.info("<NAME_EMAIL> for the password reset email.")
    else:
        logger.error("\n❌ Test failed. Please check your SendGrid configuration.")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1) 