import { api } from '@/lib/api';

export interface PrepaymentsFilters {
  client_id: string;
  entity_id?: string;
  page?: number;
  limit?: number;
  supplier_filter?: string;
  show_only_pending?: boolean;
  status_filter?: string;
}

export interface AmortizableLineItem {
  lineItemId: string;
  description: string;
  lineAmount: number;
  scheduleId: string;
  prepaymentAccountCode: string;
  expenseAccountCode: string | null;
  overallStatus: 'proposed' | 'confirmed' | 'validation_failed' | 'action_needed' | 'excluded' | 'partially_posted' | 'fully_posted' | 'skipped' | 'error_posting';
  monthlyBreakdown: Record<string, {
    status: 'proposed' | 'confirmed' | 'posted' | 'posting_error' | 'matched_manual' | 'skipped' | 'posting';
    amount: number;
    journalId?: string;
    error?: string;
  }>;
}

export interface InvoiceData {
  invoiceId: string;
  reference: string;
  invoiceDate: string;
  totalAmount: number;
  currencyCode: string;
  hasAttachment: boolean;
  attachmentId?: string;
  ocrWarningMessage?: string | null;
  overallStatus: 'action_needed' | 'proposed' | 'confirmed' | 'validation_error' | 'fully_posted' | 'skipped' | 'processing' | 'partially_posted';
  isPartialApplication: boolean;
  amortizableLineItems: AmortizableLineItem[];
}

export interface SupplierData {
  supplierId: string;
  supplierName: string;
  overallStatus: 'action_needed' | 'proposed' | 'confirmed' | 'validation_error' | 'ok';
  invoices: InvoiceData[];
}

export interface PaginationData {
  currentPage: number;
  pageSize?: number;
  totalItems?: number;
  totalPages?: number;
  itemsPerPage?: number;
}

export interface PrepaymentsResponse {
  suppliers: SupplierData[];
  pagination: PaginationData;
}

export interface ScheduleData {
  scheduleId: string;
  amortizationStartDate: string;
  amortizationEndDate: string;
  numberOfPeriods: number;
  prepaymentAccountCode: string;
  expenseAccountCode: string | null;
}

export class PrepaymentsService {
  /**
   * Fetch prepayments dashboard data from the backend
   */
  static async getPrepaymentsData(filters: PrepaymentsFilters): Promise<PrepaymentsResponse> {
    try {
      console.log('DEBUG: Calling getDashboardTransactions with filters:', filters);

      // Quick validation
      if (!filters.client_id) {
        return {
          suppliers: [],
          pagination: { currentPage: 1, totalItems: 0, totalPages: 0 }
        };
      }

      // Use the dashboard transactions endpoint which includes schedules
      const response = await api.getDashboardTransactions({
        client_id: filters.client_id,
        entity_id: filters.entity_id,
        transaction_type: 'ACCPAY_INVOICE', // Focus on invoices for prepayments
        require_action: filters.show_only_pending,
        page: filters.page || 1,
        limit: filters.limit || 10,
      });

      // Only log in development
      if (import.meta.env.DEV) {
        console.log('DEBUG: Dashboard API response - transactions:', response.transactions?.length || 0);
      }

      if (!response) {
        throw new Error('No response from API');
      }

      // For now, let's return empty data to see if the issue is in the transformation
      if (!response.transactions || response.transactions.length === 0) {
        console.log('DEBUG: No transactions found, returning empty data');
        return {
          suppliers: [],
          pagination: {
            currentPage: response.page || 1,
            pageSize: response.limit || 10,
            totalItems: response.total || 0,
            totalPages: response.total_pages || 0,
          },
        };
      }

      // Transform the backend response to match the frontend interface
      const suppliers = this.transformToSupplierData(response.transactions, filters.supplier_filter);

      return {
        suppliers,
        pagination: {
          currentPage: response.page || 1,
          pageSize: response.limit || 10,
          totalItems: response.total || 0,
          totalPages: response.total_pages || 0,
        },
      };
    } catch (error) {
      console.error('Error fetching prepayments data:', error);
      throw new Error('Failed to fetch prepayments data');
    }
  }

  /**
   * Transform backend dashboard items to supplier-grouped data
   */
  private static transformToSupplierData(dashboardItems: any[], supplierFilter?: string): SupplierData[] {
    const supplierMap = new Map<string, SupplierData>();

    if (!dashboardItems || !Array.isArray(dashboardItems)) {
      console.warn('Dashboard items is not an array:', dashboardItems);
      return [];
    }

    dashboardItems.forEach(item => {
      if (!item || !item.transaction) {
        console.warn('Invalid dashboard item:', item);
        return;
      }

      const transaction = item.transaction;
      const schedules = item.schedules || [];

      // Extract supplier information from transaction
      const supplierId = transaction.contact?.contact_id || transaction.contact_id || 'unknown';
      const supplierName = transaction.contact?.name || transaction.contact_name || 'Unknown Supplier';

      // Skip if supplier filter is applied and doesn't match
      if (supplierFilter && !supplierName.toLowerCase().includes(supplierFilter.toLowerCase())) {
        return;
      }

      // Get or create supplier entry
      if (!supplierMap.has(supplierId)) {
        supplierMap.set(supplierId, {
          supplierId,
          supplierName,
          overallStatus: 'proposed',
          invoices: [],
        });
      }

      const supplier = supplierMap.get(supplierId)!;

      // Transform transaction to invoice data
      const invoiceData: InvoiceData = {
        invoiceId: transaction.transaction_id,
        reference: transaction.invoice_number || transaction.reference || 'N/A',
        invoiceDate: transaction.date,
        totalAmount: transaction.total || 0,
        currencyCode: transaction.currency_code || 'USD',
        hasAttachment: Boolean(transaction.has_attachments),
        attachmentId: transaction.attachment_id,
        ocrWarningMessage: transaction.ocr_warning,
        overallStatus: this.determineInvoiceStatus(schedules),
        isPartialApplication: false,
        amortizableLineItems: this.transformSchedulesToLineItems(schedules),
      };

      supplier.invoices.push(invoiceData);

      // Update supplier overall status based on invoice statuses
      supplier.overallStatus = this.determineSupplierStatus(supplier.invoices);
    });

    return Array.from(supplierMap.values());
  }

  /**
   * Transform amortization schedules to line items
   */
  private static transformSchedulesToLineItems(schedules: any[]): AmortizableLineItem[] {
    return schedules.map(schedule => ({
      lineItemId: schedule.schedule_id,
      description: schedule.description || 'Amortization Schedule',
      lineAmount: schedule.original_amount || 0,
      scheduleId: schedule.schedule_id,
      prepaymentAccountCode: schedule.asset_account_code || '',
      expenseAccountCode: schedule.expense_account_code || null,
      overallStatus: this.mapScheduleStatus(schedule.status),
      monthlyBreakdown: this.transformMonthlyEntries(schedule.monthly_entries || []),
    }));
  }

  /**
   * Transform monthly entries to breakdown format
   */
  private static transformMonthlyEntries(monthlyEntries: any[]): Record<string, { status: 'proposed' | 'confirmed' | 'posted' | 'posting_error' | 'matched_manual' | 'skipped' | 'posting'; amount: number; journalId?: string; error?: string }> {
    const breakdown: Record<string, { status: 'proposed' | 'confirmed' | 'posted' | 'posting_error' | 'matched_manual' | 'skipped' | 'posting'; amount: number; journalId?: string; error?: string }> = {};

    monthlyEntries.forEach(entry => {
      const monthKey = entry.date ? entry.date.substring(0, 7) : ''; // Extract YYYY-MM
      if (monthKey) {
        breakdown[monthKey] = {
          status: this.mapEntryStatus(entry.status),
          amount: entry.amount || 0,
          journalId: entry.journal_id,
          error: entry.error_message,
        };
      }
    });

    return breakdown;
  }

  /**
   * Determine invoice status based on schedules
   */
  private static determineInvoiceStatus(schedules: any[]): 'action_needed' | 'proposed' | 'confirmed' | 'validation_error' | 'fully_posted' | 'skipped' | 'processing' | 'partially_posted' {
    if (!schedules.length) return 'validation_error';

    const hasErrors = schedules.some(s => s.status === 'validation_failed');
    if (hasErrors) return 'validation_error';

    const hasActionNeeded = schedules.some(s => s.status === 'pending_review' || s.status === 'requires_action');
    if (hasActionNeeded) return 'action_needed';

    const allPosted = schedules.every(s => s.status === 'posted' || s.status === 'fully_posted');
    if (allPosted) return 'fully_posted';

    const somePosted = schedules.some(s => s.status === 'posted' || s.status === 'partially_posted');
    if (somePosted) return 'partially_posted';

    const allSkipped = schedules.every(s => s.status === 'skipped');
    if (allSkipped) return 'skipped';

    const allConfirmed = schedules.every(s => s.status === 'confirmed');
    if (allConfirmed) return 'confirmed';

    return 'proposed';
  }

  /**
   * Determine supplier status based on invoices
   */
  private static determineSupplierStatus(invoices: InvoiceData[]): 'action_needed' | 'proposed' | 'confirmed' | 'validation_error' | 'ok' {
    if (!invoices.length) return 'proposed';

    const hasErrors = invoices.some(i => i.overallStatus === 'validation_error');
    if (hasErrors) return 'validation_error';

    const hasActionNeeded = invoices.some(i => i.overallStatus === 'action_needed' || i.overallStatus === 'partially_posted');
    if (hasActionNeeded) return 'action_needed';

    const allPosted = invoices.every(i => i.overallStatus === 'fully_posted');
    if (allPosted) return 'ok';

    const allConfirmed = invoices.every(i => i.overallStatus === 'confirmed');
    if (allConfirmed) return 'confirmed';

    return 'proposed';
  }

  /**
   * Map backend schedule status to frontend status
   */
  private static mapScheduleStatus(status: string): 'proposed' | 'confirmed' | 'validation_failed' | 'action_needed' | 'excluded' | 'partially_posted' | 'fully_posted' | 'skipped' | 'error_posting' {
    switch (status) {
      case 'confirmed':
        return 'confirmed';
      case 'posted':
      case 'fully_posted':
        return 'fully_posted';
      case 'partially_posted':
        return 'partially_posted';
      case 'validation_failed':
      case 'error':
        return 'validation_failed';
      case 'pending_review':
      case 'requires_action':
        return 'action_needed';
      case 'skipped':
        return 'skipped';
      case 'excluded':
        return 'excluded';
      case 'error_posting':
        return 'error_posting';
      default:
        return 'proposed';
    }
  }

  /**
   * Map backend entry status to frontend status
   */
  private static mapEntryStatus(status: string): 'proposed' | 'confirmed' | 'posted' | 'posting_error' | 'matched_manual' | 'skipped' | 'posting' {
    switch (status) {
      case 'posted':
        return 'posted';
      case 'confirmed':
        return 'confirmed';
      case 'posting_error':
        return 'posting_error';
      case 'matched_manual':
        return 'matched_manual';
      case 'skipped':
        return 'skipped';
      case 'posting':
        return 'posting';
      default:
        return 'proposed';
    }
  }

  /**
   * Confirm a schedule
   */
  static async confirmSchedule(scheduleId: string): Promise<void> {
    try {
      await api.confirmSchedule(scheduleId);
    } catch (error) {
      console.error('Error confirming schedule:', error);
      throw new Error('Failed to confirm schedule');
    }
  }

  /**
   * Skip a schedule
   */
  static async skipSchedule(scheduleId: string, reason: string): Promise<void> {
    try {
      await api.skipSchedule(scheduleId, reason);
    } catch (error) {
      console.error('Error skipping schedule:', error);
      throw new Error('Failed to skip schedule');
    }
  }

  /**
   * Update a schedule
   */
  static async updateSchedule(scheduleId: string, scheduleData: Partial<ScheduleData>): Promise<void> {
    try {
      await api.updateSchedule(scheduleId, scheduleData);
    } catch (error) {
      console.error('Error updating schedule:', error);
      throw new Error('Failed to update schedule');
    }
  }

  /**
   * Get schedule details
   */
  static async getSchedule(scheduleId: string): Promise<any> {
    try {
      return await api.getSchedule(scheduleId);
    } catch (error) {
      console.error('Error fetching schedule:', error);
      throw new Error('Failed to fetch schedule details');
    }
  }
}
