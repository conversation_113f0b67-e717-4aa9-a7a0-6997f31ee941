# Fast startup script for DRCR Backend
# This disables auto-reload for better performance during testing

Write-Host "Starting DRCR Backend in FAST mode (no auto-reload)..." -ForegroundColor Green

# Set environment variable to disable reload
$env:DISABLE_RELOAD = "true"
$env:GOOGLE_CLOUD_PROJECT = "drcr-d660a"
$env:GCP_PROJECT_ID = "drcr-d660a"

# Change to rest_api directory and start server
Set-Location "rest_api"
python run_server.py 