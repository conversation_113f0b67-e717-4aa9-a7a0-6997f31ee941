import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { useAuthStore } from '../store/auth.store';
import { toast } from 'sonner';

// Add global styles to prevent scrolling
const GlobalStyles = () => {
  useEffect(() => {
    // Save original styles
    const originalStyle = window.getComputedStyle(document.body).overflow;

    // Apply no-scroll style
    document.body.style.overflow = 'hidden';
    document.documentElement.style.overflow = 'hidden';

    // Cleanup function to restore original styles
    return () => {
      document.body.style.overflow = originalStyle;
      document.documentElement.style.overflow = originalStyle;
    };
  }, []);

  return null;
};

// Import Shadcn UI components with relative paths to ensure they're found
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from '../components/ui/card';
import { Alert, AlertDescription, AlertTitle } from "../components/ui/alert";

// Import Lucide icons
import {
  Loader2,
  Mail,
  Lock,
  AlertCircle,
  LogIn,
} from 'lucide-react';

// Import the DRCR logo
import drcrLogo from '../assets/logo.png';

export default function ShadcnLoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [emailError, setEmailError] = useState<string | null>(null);
  const [passwordError, setPasswordError] = useState<string | null>(null);

  const navigate = useNavigate();
  const location = useLocation();
  const { signIn, user } = useAuthStore();

  // Redirect if already logged in
  useEffect(() => {
    if (user) {
      const from = location.state?.from?.pathname || '/dashboard';
      navigate(from, { replace: true });
    }
  }, [user, navigate, location]);

  const validateEmail = (emailToValidate: string) => {
    if (!emailToValidate) {
      setEmailError("Email is required.");
      return false;
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(emailToValidate)) {
      setEmailError("Invalid email format.");
      return false;
    }
    setEmailError(null);
    return true;
  };

  const validatePassword = (passwordToValidate: string) => {
    if (!passwordToValidate) {
      setPasswordError("Password is required.");
      return false;
    }
    if (passwordToValidate.length < 8) {
      setPasswordError("Password must be at least 8 characters long.");
      return false;
    }
    setPasswordError(null);
    return true;
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    const isEmailValid = validateEmail(email);
    const isPasswordValid = validatePassword(password);

    if (!isEmailValid || !isPasswordValid) {
      return;
    }

    setIsLoading(true);
    try {
      await signIn(email, password);
      toast.success('Logged in successfully!');
      // Navigation will be handled by the useEffect above when user state changes
    } catch (err: any) {
      // The AuthService already handles error formatting
      const errorMessage = err.message || "Failed to sign in. Please try again.";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <GlobalStyles />
      <div className="h-screen flex flex-col items-center justify-center bg-background p-0 m-0 overflow-hidden font-sans">
        <Card className="w-full max-w-lg shadow-sm bg-card rounded-xl border">
          <CardHeader className="text-center pt-4 pb-2">
            <div className="w-32 h-auto mx-auto mb-1">
              <img
                src={drcrLogo}
                alt="DRCR Logo"
                className="w-full h-auto"
              />
            </div>
          </CardHeader>
          <CardContent className="px-10 pb-3">
          <form onSubmit={handleLogin} className="flex flex-col space-y-4">
            <div className={`h-${error ? 'auto' : '0'} mb-${error ? '4' : '0'} transition-all duration-200`}>
              {error && (
                <Alert variant="destructive" className="p-3 text-sm">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle className="text-sm font-medium">Login Failed</AlertTitle>
                  <AlertDescription className="text-xs">{error}</AlertDescription>
                </Alert>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="email" className="text-base font-medium text-foreground block mb-1">
                Email Address
              </Label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => { setEmail(e.target.value); if (emailError) validateEmail(e.target.value); }}
                  onBlur={() => validateEmail(email)}
                  className={`w-full pl-10 h-10 text-base ${emailError ? 'border-destructive focus:ring-destructive' : 'border-border focus:ring-primary'}`}
                  autoComplete="email"
                />
              </div>
              <div className="h-5 mt-1">
                {emailError && <p className="text-xs text-destructive m-0">{emailError}</p>}
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="password" className="text-base font-medium text-foreground block mb-1">
                Password
              </Label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="password"
                  type="password"
                  placeholder="••••••••"
                  value={password}
                  onChange={(e) => { setPassword(e.target.value); if (passwordError) validatePassword(e.target.value); }}
                  onBlur={() => validatePassword(password)}
                  className={`w-full pl-10 h-10 text-base ${passwordError ? 'border-destructive focus:ring-destructive' : 'border-border focus:ring-primary'}`}
                  autoComplete="current-password"
                />
              </div>
              <div className="h-5 mt-1">
                {passwordError && <p className="text-xs text-destructive m-0">{passwordError}</p>}
              </div>
            </div>
            <Button
              type="submit"
              className="w-full h-10 text-base font-medium flex items-center justify-center gap-2 rounded-md disabled:opacity-70 disabled:cursor-not-allowed transition-colors"
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="h-5 w-5 animate-spin" />
              ) : (
                <LogIn className="h-5 w-5" />
              )}
              {isLoading ? 'Signing In...' : 'Sign In'}
            </Button>
            
            {/* Forgot Password Link */}
            <div className="text-center pt-2">
              <Link 
                to="/forgot-password" 
                className="text-sm text-muted-foreground hover:text-primary transition-colors underline"
              >
                Forgot your password?
              </Link>
            </div>
          </form>
        </CardContent>
        <CardFooter className="flex flex-col items-center text-xs text-muted-foreground pt-4 pb-6">
          <p>&copy; {new Date().getFullYear()} DRCR Labs. All rights reserved.</p>
        </CardFooter>
      </Card>
    </div>
    </>
  );
}
