# Data Relationships and Flows

Understanding the relationships between Firestore collections is crucial for comprehending how data is organized and utilized within the DRCR application. This document outlines the key relationships and data flows.

## Core Data Hierarchy

The DRCR data model follows a hierarchical structure, primarily rooted in the `FIRMS` collection:

1.  **`FIRMS`**: The top-level organizational unit. Each firm is distinct.
    *   **`FIRM_USERS`**: Many-to-one relationship with `FIRMS` (a firm can have multiple users; a user belongs to one firm for their primary context). User authentication is handled by Firebase Auth, and `FIRM_USERS` links the Firebase UID to a firm and role.
    *   **`CLIENTS`**: Many-to-one relationship with `FIRMS` (a firm manages multiple clients; a client belongs to one firm).

2.  **`CLIENTS`**: Represents the businesses or individuals whose financial data is managed.
    *   **`ENTITIES`**: Many-to-one relationship with `CLIENTS` (a client can have multiple entities, e.g., connections to different Xero organizations or distinct business units; an entity belongs to one client).

3.  **`ENTITIES`**: Represents specific accounting instances, often tied to an external platform like Xero.
    *   **`ENTITY_SETTINGS`**: One-to-one relationship with `ENTITIES` (each entity has its own settings document, sharing the same ID).
    *   **`XERO_APP_TENANT_CONNECTIONS`**: One-to-one relationship with `ENTITIES` (for entities where `platform` is "XERO", this stores the OAuth tokens, sharing the same ID as the Xero Tenant ID/`entity_id`).
    *   **`TRANSACTIONS`**: Many-to-one relationship with `ENTITIES` (an entity can have many transactions; a transaction belongs to one entity). Also linked to `CLIENTS`.
    *   **`COUNTERPARTIES`**: Many-to-one relationship with `ENTITIES` (an entity can have many counterparties; a counterparty is defined within the context of an entity and client). Also linked to `CLIENTS`.

## Key Data Flows

### 1. Xero Data Synchronization

This flow describes how data from a connected Xero organization is brought into DRCR:

*   **Authentication**: A Xero organization is connected via OAuth2. Tokens are stored in `XERO_APP_TENANT_CONNECTIONS`, linked to an `ENTITIES` record (which uses the Xero Tenant ID as its document ID) and a `CLIENTS` record.
*   **Data Fetching**: The `xero_sync_consumer` cloud function (or similar mechanism) periodically fetches data (Contacts, Invoices, Bills, Payments, etc.) from Xero using the stored tokens.
*   **Data Transformation & Storage**:
    *   Xero Contacts are transformed and stored as `COUNTERPARTIES` documents, linked to the relevant `client_id` and `entity_id`.
    *   Xero Invoices, Bills, Payments are transformed into standardized `TRANSACTIONS` documents, linked to `client_id`, `entity_id`, and relevant `counterparty_id` (from `COUNTERPARTIES`).
    *   Raw Xero data may be stored within `TRANSACTIONS` or `COUNTERPARTIES` for reference.
*   **Settings Update**: Sync timestamps and relevant settings in `ENTITY_SETTINGS` are updated (e.g., `_system_lastSyncTimestampUtc_Contacts`).

### 2. Amortization Process Flow

This flow describes how transactions are processed for amortization:

*   **Transaction Identification**: A `TRANSACTIONS` document (e.g., a bill with services to be amortized) is identified, either manually or through automated criteria.
*   **Amortization Schedule Creation**:
    *   Based on the transaction's line items, amount, and user-defined or default amortization parameters (start date, end date, expense/asset accounts from `ENTITY_SETTINGS`), one or more `AMORTIZATION_SCHEDULES` documents are created.
    *   Each schedule is linked to the `transaction_id`, `client_id`, and `entity_id`.
    *   The schedule details `monthlyEntries` for recognizing portions of the expense/revenue over time.
*   **Proposed Journal Generation**:
    *   A scheduled process (e.g., a cloud function) queries `AMORTIZATION_SCHEDULES` for `monthlyEntries` that are due for the current or upcoming period and have a status like "proposed" or "active".
    *   For each due entry, a `PROPOSED_JOURNALS` document is created. This document contains the debit/credit lines based on the schedule's `assetAccountCode` and `expenseAccountCode`, and the `amount` from the `monthlyEntry`.
    *   The `PROPOSED_JOURNALS` document is linked back to the `amortization_schedule_id` and includes the `client_id` and `entity_id`.
*   **Journal Posting (to Xero)**:
    *   `PROPOSED_JOURNALS` can be reviewed by users.
    *   Upon approval (manual or automatic via `ENTITY_SETTINGS`), the journal is formatted and posted to the corresponding Xero organization via the Xero API.
    *   The `xero_manual_journal_id` and `posted_to_xero_at` fields in `PROPOSED_JOURNALS` are updated. The status of the corresponding `monthlyEntry` in `AMORTIZATION_SCHEDULES` is also updated (e.g., to "posted").

### 3. Audit Trail

*   **`AUDIT_LOG`**: Most significant create, update, delete operations, system events (like sync start/end), and errors across the application should result in an entry in the `AUDIT_LOG`. These logs include relevant IDs (`client_id`, `entity_id`, `user_id`) to trace actions back to their context.

## Visual Diagrams

For a more visual representation of these relationships and flows, please refer to the diagrams in the `docs/data_model/diagrams/` directory (to be added). These diagrams will include:

*   An Entity-Relationship Diagram (ERD-like) showing collection structures and links.
*   Data Flow Diagrams (DFDs) illustrating the Xero sync and Amortization processes.

This textual description provides a foundation for understanding the dynamic interactions within the DRCR data model. 