#!/bin/sh
# Script for installing backend dependencies

echo "Current directory: $(pwd)"
echo "Listing root directory:"
ls -la
apt-get update && apt-get install -y gcc
mkdir -p backend
cp -r app/* backend/ || true
cp -r rest_api/* backend/ || true
cp requirements.txt backend/ || true
cp Dockerfile backend/ || true
ls -la backend/
cd backend
pwd
ls -la
python -m venv .venv
source .venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt