from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException # Import Starlette's for broader compatibility
from typing import Union
from datetime import datetime

from ..models.errors import ErrorResponse, ErrorDetail # Adjust path if models are elsewhere

import logging
logger = logging.getLogger(__name__)

async def http_exception_handler(request: Request, exc: Union[HTTPException, StarletteHTTPException]) -> JSONResponse:
    """Handles FastAPI's HTTPException and Starlette's HTTPException."""
    error_model_instance = ErrorResponse(
        status_code=exc.status_code,
        detail=exc.detail,
        code=getattr(exc, "code", None), # Custom attribute we might add to HTTPExceptions later
        timestamp=datetime.utcnow(),
        path=request.url.path,
        validation_errors=None
    )
    content_dict = error_model_instance.model_dump(exclude_none=True)
    if isinstance(content_dict.get("timestamp"), datetime):
        content_dict["timestamp"] = content_dict["timestamp"].isoformat().replace("+00:00", "Z")
    
    headers = getattr(exc, "headers", None)
    return JSONResponse(
        status_code=exc.status_code,
        content=content_dict,
        headers=headers
    )

async def request_validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """Handles Pydantic RequestValidationError (e.g., invalid request body)."""
    validation_details = []
    for error in exc.errors():
        validation_details.append(
            ErrorDetail(
                loc=[str(loc_item) for loc_item in error.get("loc", [])],
                msg=error.get("msg", "Validation error"),
                type=error.get("type", "value_error")
            )
        )
    
    error_model_instance = ErrorResponse(
        status_code=422, # HTTP 422 Unprocessable Entity
        detail="Request validation failed. Please check the input data.",
        code="VALIDATION_ERROR",
        timestamp=datetime.utcnow(),
        path=request.url.path,
        validation_errors=validation_details
    )
    content_dict = error_model_instance.model_dump(exclude_none=True)
    if isinstance(content_dict.get("timestamp"), datetime):
        content_dict["timestamp"] = content_dict["timestamp"].isoformat().replace("+00:00", "Z")

    return JSONResponse(
        status_code=422,
        content=content_dict
    )

async def generic_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handles any other unhandled Exception."""
    logger.error(f"Unhandled exception for request {request.url.path}: {exc}", exc_info=True)
    error_model_instance = ErrorResponse(
        status_code=500, # HTTP 500 Internal Server Error
        detail="An unexpected internal server error occurred.",
        code="INTERNAL_SERVER_ERROR",
        timestamp=datetime.utcnow(),
        path=request.url.path,
        validation_errors=None
    )
    content_dict = error_model_instance.model_dump(exclude_none=True)
    if isinstance(content_dict.get("timestamp"), datetime):
        content_dict["timestamp"] = content_dict["timestamp"].isoformat().replace("+00:00", "Z")
        
    return JSONResponse(
        status_code=500,
        content=content_dict
    )

# We can add more specific handlers here later, e.g., for custom application exceptions. 