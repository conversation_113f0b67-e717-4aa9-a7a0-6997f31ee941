import React from 'react';
import { useNavigate } from 'react-router-dom';
import { AppSidebar } from '../components/layout/AppSidebar';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import { Button } from '../components/ui/button';
import { Separator } from '../components/ui/separator';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '../components/ui/breadcrumb';
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from '../components/ui/sidebar';
import { CreditCard, Clock, AlertCircle, CheckCircle } from 'lucide-react';

export function BillingPage() {
  const navigate = useNavigate();

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset className="flex-1 overflow-hidden">
        <header className="sticky top-0 z-10 flex h-16 shrink-0 items-center gap-2 border-b bg-background px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    navigate('/dashboard');
                  }}
                  className="cursor-pointer"
                >
                  DRCR
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator className="hidden md:block" />
              <BreadcrumbItem>
                <BreadcrumbPage>Billing</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </header>
        <div className="flex-1 overflow-auto">
          <div className="p-8 max-w-4xl">
            <div className="mb-8">
              <h1 className="text-2xl font-bold mb-2">Credits & Billing</h1>
              <p className="text-muted-foreground">
                Manage your credit balance and purchase additional credits for document processing.
              </p>
            </div>

            <div className="space-y-6">
          {/* Credit Balance Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Credit Balance
              </CardTitle>
              <CardDescription>
                Your current credit balance and usage overview.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between mb-6">
                <div>
                  <div className="text-4xl font-bold text-primary">500</div>
                  <p className="text-muted-foreground">Credits Remaining</p>
                </div>
                <div className="text-right">
                  <Badge variant="default" className="flex items-center gap-1 mb-2">
                    <CheckCircle className="h-3 w-3" />
                    Free Trial
                  </Badge>
                  <p className="text-sm text-muted-foreground">1 credit = 1 page processed</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="text-center p-4 bg-muted rounded-lg">
                  <div className="text-2xl font-bold">247</div>
                  <div className="text-sm text-muted-foreground">Pages Processed</div>
                </div>
                <div className="text-center p-4 bg-muted rounded-lg">
                  <div className="text-2xl font-bold">0</div>
                  <div className="text-sm text-muted-foreground">Credits Purchased</div>
                </div>
                <div className="text-center p-4 bg-muted rounded-lg">
                  <div className="text-2xl font-bold">253</div>
                  <div className="text-sm text-muted-foreground">Credits Used</div>
                </div>
              </div>

              <div className="flex gap-2">
                <Button disabled className="bg-primary">
                  Buy 1,000 Credits - $10 (Coming Soon)
                </Button>
                <Button variant="outline" disabled>
                  <Clock className="h-4 w-4 mr-2" />
                  View Usage History (Coming Soon)
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Credit Packages Card */}
          <Card>
            <CardHeader>
              <CardTitle>Credit Packages</CardTitle>
              <CardDescription>
                Purchase additional credits for document processing.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border rounded-lg bg-primary/5 border-primary/20">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-semibold">Standard Package</h3>
                    <Badge variant="default">Most Popular</Badge>
                  </div>
                  <div className="text-3xl font-bold mb-2">$10</div>
                  <div className="text-muted-foreground mb-4">1,000 Credits</div>
                  <ul className="text-sm space-y-1 mb-4">
                    <li>• Process up to 1,000 pages</li>
                    <li>• Credits never expire</li>
                    <li>• Instant activation</li>
                  </ul>
                  <Button disabled className="w-full">
                    Purchase (Coming Soon)
                  </Button>
                </div>

                <div className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-semibold">Free Trial</h3>
                    <Badge variant="secondary">Current</Badge>
                  </div>
                  <div className="text-3xl font-bold mb-2">$0</div>
                  <div className="text-muted-foreground mb-4">500 Credits</div>
                  <ul className="text-sm space-y-1 mb-4">
                    <li>• Perfect for testing</li>
                    <li>• No payment required</li>
                    <li>• Full feature access</li>
                  </ul>
                  <Button variant="outline" disabled className="w-full">
                    Active
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Usage History Card */}
          <Card>
            <CardHeader>
              <CardTitle>Usage History</CardTitle>
              <CardDescription>
                Recent credit transactions and document processing history.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {/* Sample usage history items */}
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">Invoice_2024_001.pdf</p>
                    <p className="text-sm text-muted-foreground">Dec 15, 2024 • 3 pages</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-red-600">-3 credits</p>
                    <p className="text-xs text-muted-foreground">497 remaining</p>
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">Receipt_Dec_14.jpg</p>
                    <p className="text-sm text-muted-foreground">Dec 14, 2024 • 1 page</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-red-600">-1 credit</p>
                    <p className="text-xs text-muted-foreground">500 remaining</p>
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 border rounded-lg bg-green-50">
                  <div>
                    <p className="font-medium">Free Trial Credits</p>
                    <p className="text-sm text-muted-foreground">Dec 1, 2024 • Account created</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-green-600">+500 credits</p>
                    <p className="text-xs text-muted-foreground">500 remaining</p>
                  </div>
                </div>
              </div>

              <div className="mt-4">
                <Button variant="outline" disabled>
                  View Full History (Coming Soon)
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Coming Soon Notice */}
          <Card className="border-amber-200 bg-amber-50">
            <CardContent className="pt-6">
              <div className="flex items-start gap-3">
                <AlertCircle className="h-5 w-5 text-amber-600 mt-0.5" />
                <div>
                  <h3 className="font-semibold text-amber-800">Credit System Coming Soon</h3>
                  <p className="text-sm text-amber-700 mt-1">
                    The prepaid credit billing system is currently in development. Features include
                    Stripe payment integration, real-time credit tracking, automatic page counting,
                    and detailed usage analytics. You're currently enjoying free trial credits!
                  </p>
                  <div className="mt-3 text-sm text-amber-700">
                    <strong>Planned Features:</strong>
                    <ul className="list-disc list-inside mt-1 space-y-1">
                      <li>Secure Stripe payment processing</li>
                      <li>Real-time credit balance updates</li>
                      <li>Automatic page counting and credit deduction</li>
                      <li>Detailed usage history and analytics</li>
                      <li>Low credit warnings and notifications</li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
