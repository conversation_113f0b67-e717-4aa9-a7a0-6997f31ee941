/**
 * Test utility to verify authentication integration between frontend and backend
 * This is for development/testing purposes only
 */

import { AuthService } from '../services/auth.service';
import { api } from '../lib/api';

export interface TestResult {
  success: boolean;
  message: string;
  details?: any;
}

export class AuthIntegrationTester {
  /**
   * Test Firebase authentication with email/password
   */
  static async testFirebaseAuth(email: string, password: string): Promise<TestResult> {
    try {
      console.log('Testing Firebase authentication...');
      const user = await AuthService.signIn(email, password);
      
      return {
        success: true,
        message: 'Firebase authentication successful',
        details: {
          uid: user.uid,
          email: user.email,
          displayName: user.displayName
        }
      };
    } catch (error: any) {
      return {
        success: false,
        message: `Firebase authentication failed: ${error.message}`,
        details: error
      };
    }
  }

  /**
   * Test API call with Firebase token
   */
  static async testApiWithToken(): Promise<TestResult> {
    try {
      console.log('Testing API call with Firebase token...');
      
      // Try to make a simple API call that requires authentication
      // Since we don't have a specific user profile endpoint, we'll test with a generic endpoint
      const response = await api.get('/health');
      
      return {
        success: true,
        message: 'API call successful',
        details: response
      };
    } catch (error: any) {
      return {
        success: false,
        message: `API call failed: ${error.message}`,
        details: {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data
        }
      };
    }
  }

  /**
   * Test complete authentication flow
   */
  static async testCompleteFlow(email: string, password: string): Promise<TestResult[]> {
    const results: TestResult[] = [];

    // Test 1: Firebase Authentication
    const authResult = await this.testFirebaseAuth(email, password);
    results.push(authResult);

    // Test 2: API call with token (only if auth succeeded)
    if (authResult.success) {
      const apiResult = await this.testApiWithToken();
      results.push(apiResult);
    }

    // Test 3: Sign out
    try {
      await AuthService.signOut();
      results.push({
        success: true,
        message: 'Sign out successful'
      });
    } catch (error: any) {
      results.push({
        success: false,
        message: `Sign out failed: ${error.message}`,
        details: error
      });
    }

    return results;
  }

  /**
   * Test token retrieval
   */
  static async testTokenRetrieval(): Promise<TestResult> {
    try {
      const token = await AuthService.getCurrentUserToken();
      
      if (token) {
        return {
          success: true,
          message: 'Token retrieved successfully',
          details: {
            tokenLength: token.length,
            tokenPreview: token.substring(0, 20) + '...'
          }
        };
      } else {
        return {
          success: false,
          message: 'No token available (user not authenticated)'
        };
      }
    } catch (error: any) {
      return {
        success: false,
        message: `Token retrieval failed: ${error.message}`,
        details: error
      };
    }
  }

  /**
   * Print test results to console
   */
  static printResults(results: TestResult[]): void {
    console.log('\n=== Authentication Integration Test Results ===');
    
    results.forEach((result, index) => {
      const status = result.success ? '✅ PASS' : '❌ FAIL';
      console.log(`\n${index + 1}. ${status}: ${result.message}`);
      
      if (result.details) {
        console.log('   Details:', result.details);
      }
    });

    const passCount = results.filter(r => r.success).length;
    const totalCount = results.length;
    
    console.log(`\n=== Summary: ${passCount}/${totalCount} tests passed ===\n`);
  }
}

// Export a simple test function for use in browser console
(window as any).testAuth = async (email: string, password: string) => {
  const results = await AuthIntegrationTester.testCompleteFlow(email, password);
  AuthIntegrationTester.printResults(results);
  return results;
};

// Export individual test functions
(window as any).testFirebaseAuth = AuthIntegrationTester.testFirebaseAuth;
(window as any).testApiCall = AuthIntegrationTester.testApiWithToken;
(window as any).testToken = AuthIntegrationTester.testTokenRetrieval;
