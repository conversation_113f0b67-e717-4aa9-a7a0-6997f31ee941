import { create } from 'zustand';
import {
  onAuthStateChanged,
  type User as FirebaseUser
} from 'firebase/auth';
import { auth } from '../lib/firebase';
import { AuthService } from '../services/auth.service';

// Define user profile interface
interface UserProfile {
  uid: string;
  email: string;
  displayName?: string;
  firmId?: string;
  clientId?: string;
  role?: string;
  assignedClientIds?: string[];
}

// FirebaseUser type is now imported from firebase/auth

// Define auth state interface
interface AuthState {
  user: UserProfile | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: AuthState = {
  user: null,
  isLoading: true,
  error: null
};

export const useAuthStore = create<
  AuthState & {
    initialize: () => Promise<void>;
    signUp: (email: string, password: string) => Promise<void>;
    signIn: (email: string, password: string) => Promise<void>;
    signOut: () => Promise<void>;
    resetError: () => void;
    getIdToken: () => Promise<string | null>;
  }
>((set) => ({
  ...initialState,

  initialize: async () => {
    set({ isLoading: true });

    return new Promise<void>((resolve) => {
      const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
        if (firebaseUser) {
          try {
            const userProfile = await fetchUserProfile(firebaseUser);
            set({ user: userProfile, isLoading: false, error: null });
          } catch (error) {
            console.error('Error fetching user profile:', error);
            set({
              user: mapFirebaseUserToProfile(firebaseUser),
              isLoading: false,
              error: 'Failed to fetch user profile'
            });
          }
        } else {
          set({ user: null, isLoading: false, error: null });
        }
        resolve();
      });

      // Clean up subscription on unmount
      return () => unsubscribe();
    });
  },

  signUp: async (email, password) => {
    set({ isLoading: true, error: null });

    try {
      const firebaseUser = await AuthService.signUp(email, password);
      const userProfile = mapFirebaseUserToProfile(firebaseUser);
      set({ user: userProfile, isLoading: false });
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.message || 'Failed to sign up'
      });
      throw error;
    }
  },

  signIn: async (email, password) => {
    set({ isLoading: true, error: null });

    try {
      const firebaseUser = await AuthService.signIn(email, password);
      const userProfile = await fetchUserProfile(firebaseUser);
      set({ user: userProfile, isLoading: false });
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.message || 'Failed to sign in'
      });
      throw error;
    }
  },

  signOut: async () => {
    set({ isLoading: true, error: null });

    try {
      await AuthService.signOut();
      set({ user: null, isLoading: false });
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.message || 'Failed to sign out'
      });
      throw error;
    }
  },

  resetError: () => {
    set({ error: null });
  },

  getIdToken: async () => {
    try {
      const currentUser = auth.currentUser;
      if (currentUser) {
        return await currentUser.getIdToken();
      }
      return null;
    } catch (error) {
      console.error('Error getting ID token:', error);
      return null;
    }
  }
}));

// Helper functions
async function fetchUserProfile(firebaseUser: FirebaseUser): Promise<UserProfile> {
  try {
    // Get the Firebase ID token to authenticate with backend
    const token = await firebaseUser.getIdToken();

    // Call the backend /auth/me endpoint to get user profile with firm info
    const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081';
    const response = await fetch(`${apiBaseUrl}/auth/me`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const backendProfile = await response.json();
      return {
        uid: backendProfile.uid,
        email: backendProfile.email,
        displayName: backendProfile.display_name,
        firmId: backendProfile.firm_id,
        clientId: backendProfile.client_id,
        role: backendProfile.role,
        assignedClientIds: backendProfile.assigned_client_ids || []
      };
    } else {
      console.warn('Failed to fetch user profile from backend, using Firebase data only');
      return mapFirebaseUserToProfile(firebaseUser);
    }
  } catch (error) {
    console.error('Error fetching user profile:', error);
    // Fall back to basic profile from Firebase
    return mapFirebaseUserToProfile(firebaseUser);
  }
}

function mapFirebaseUserToProfile(firebaseUser: FirebaseUser): UserProfile {
  return {
    uid: firebaseUser.uid,
    email: firebaseUser.email || '',
    displayName: firebaseUser.displayName || undefined
  };
}
