# DRCR Backend Documentation Index

## 📚 Main Documentation
- **Main README**: `docs/README.md` - Complete documentation overview
- **Codebase Structure**: `docs/development/codebase_structure.md` - Project organization guide
- **API Guide**: `docs/api_guide/README.md` - REST API documentation
- **Development Setup**: `docs/development/setup.md` - Environment setup instructions

## 🔄 Scheduled Sync System (Complete Implementation)

### Core Documentation
- **Comprehensive Guide**: `docs/SCHEDULED_SYNC_SYSTEM.md` - Full technical documentation
- **Quick Reference**: `docs/SCHEDULED_SYNC_QUICK_REFERENCE.md` - Developer cheat sheet
- **Test Results**: `docs/SCHEDULED_SYNC_TEST_RESULTS.md` - Verification and testing summary

### Infrastructure Components
- **Cloud Scheduler**: `terraform/scheduler.tf` - Automated sync triggers (hourly/daily/weekly)
- **Cloud Functions**: `terraform/functions.tf` - Function definitions and configuration
- **Pub/Sub Topics**: `terraform/pubsub.tf` - Message handling infrastructure
- **Main Terraform**: `terraform/main.tf` - Core infrastructure with GCS backend

### Cloud Functions
- **Scheduled Sync Processor**: `cloud_functions/scheduled_sync_processor/`
  - `main.py` - Queries entities by frequency and triggers syncs
  - `requirements.txt` - Dependencies for Firestore and Pub/Sub
- **Xero Sync Consumer**: `cloud_functions/xero_sync_consumer/`
  - `main.py` - Processes actual sync requests with organisation sync
  - `financial_doc_adapter.py` - Enhanced with async URL generation
  - `requirements.txt` - Xero API and Firestore dependencies

### REST API Integration
- **Entity Settings**: `rest_api/routes/entities.py` - Enhanced with automated sync triggers
  - Fixed transactionSyncStartDate inclusion
  - Automated sync on settings changes
  - Configuration status endpoints

## 🤖 LLM Integration System (Complete Implementation)

### Core Components
- **Document Processor**: `drcr_shared_logic/document_processor.py` - LLM analysis functions
  - OpenAI direct processing with total validation
  - Mistral OCR fallback for complex documents
  - Service period extraction and confidence scoring
- **Xero Client Enhancement**: `drcr_shared_logic/clients/xero_client.py`
  - `get_attachments()` method for fetching attachment metadata
  - `download_attachment()` method for downloading content
  - Proper transaction type handling

### Sync Integration
- **Combined Analysis**: GL coding + LLM prepayment detection
- **Attachment Processing**: Automatic fetching and analysis during sync
- **Service Period Extraction**: LLM-based service date detection
- **Confidence Scoring**: Quality control for LLM results
- **Entity Configuration**: `enable_llm_prepayment_detection` setting

### Testing & Verification
- **LLM Integration Tests**: `tests/test_llm_integration.py` - Comprehensive LLM testing
- **Simple Verification**: `tests/simple_llm_verification.py` - Quick LLM function tests
- **Full Sync Testing**: Verified with real Pub/Sub messages and 41 bills processed

## 🏗️ Infrastructure Overview

### Terraform Files
```
terraform/
├── main.tf           # Core infrastructure, GCS backend, APIs
├── scheduler.tf      # Cloud Scheduler jobs
├── functions.tf      # Cloud Functions definitions
├── pubsub.tf         # Pub/Sub topics and subscriptions
├── api.tf            # Cloud Run API configuration
└── variables.tf      # Variable definitions
```

### Cloud Functions
```
cloud_functions/
├── scheduled_sync_processor/     # Automated sync processing
│   ├── main.py                  # Entity querying and sync triggering
│   └── requirements.txt         # Firestore + Pub/Sub dependencies
└── xero_sync_consumer/          # Main sync processor with LLM integration
    ├── main.py                  # Organisation sync + LLM processing
    ├── financial_doc_adapter.py # Async URL generation with caching
    └── requirements.txt         # Xero API + LLM dependencies
```

### Testing Structure
```
tests/
├── scheduled_sync/              # Dedicated sync testing
│   └── test_scheduled_sync.py   # Comprehensive test suite
├── test_llm_integration.py      # LLM integration testing
└── simple_llm_verification.py   # Quick LLM verification
```

### Scripts Organization
```
scripts/
├── deploy_scheduled_sync.py     # Infrastructure deployment
└── utilities/                   # Helper scripts (organized)
    ├── create_firestore_indexes.py
    ├── check_prepayment_data.py
    ├── pull_xero_invoices_for_testing.py
    └── sync_invoices_for_prepayment_testing.py
```

## 🎯 Key Features Implemented

### Automated Sync System
- **Cloud Scheduler Jobs**: Hourly (`0 * * * *`), Daily (`0 2 * * *`), Weekly (`0 3 * * 1`)
- **Smart Sync Logic**: Checks last sync timestamps to avoid unnecessary syncing
- **Entity Configuration**: Respects sync_frequency and auto_sync_enabled settings
- **Document Type Control**: Selective syncing based on entity preferences

### Enhanced Xero Integration
- **Organisation Sync**: Automatically fetches and stores Xero organisation details
- **Proper Deep Links**: Generates correct Xero URLs with organisation shortcode
- **URL Generation**: Async caching for performance
- **Comprehensive Sync**: All 6 document types (Bills, Invoices, SpendMoney, etc.)

### LLM-Powered Prepayment Detection
- **Dual Detection**: GL coding + LLM attachment analysis
- **Attachment Processing**: Automatic fetching and analysis from Xero
- **Service Period Extraction**: AI-powered date detection from invoices
- **Quality Control**: Total validation and confidence scoring
- **Fallback Logic**: OpenAI → Mistral OCR for complex documents

### Infrastructure Improvements
- **GCS Backend**: Terraform state management in Google Cloud Storage
- **Pub/Sub Architecture**: Reliable message handling with dead letter queues
- **Error Handling**: Comprehensive retry policies and error tracking
- **Monitoring**: Cloud Function logs and Pub/Sub metrics

## 🚀 Quick Commands

### Deploy Everything
```bash
python scripts/deploy_scheduled_sync.py
```

### Test System
```bash
python tests/scheduled_sync/test_scheduled_sync.py
python tests/test_llm_integration.py
```

### Monitor Logs
```bash
gcloud functions logs read scheduled-sync-processor --limit=20
gcloud functions logs read xero-sync-consumer --limit=20
```

### Check Infrastructure
```bash
gcloud scheduler jobs list
gcloud pubsub topics list
gcloud functions list
```

## 🔧 Configuration Files

### Entity Settings (Firestore)
```json
{
  "sync_frequency": "daily|hourly|weekly|manual",
  "auto_sync_enabled": true,
  "sync_bills": true,
  "sync_invoices": false,
  "enable_llm_prepayment_detection": true,
  "prepayment_asset_account_codes": ["620"],
  "transaction_sync_start_date": "2025-02-01",
  "_system_lastSyncTimestampUtc_Bills": "2025-06-03T13:30:07.783169+00:00"
}
```

### Environment Variables
```bash
GCP_PROJECT_ID=drcr-d660a
PUBSUB_TOPIC_XERO_SYNC=xero-sync-topic
OPENAI_API_KEY=your_openai_key
MISTRAL_API_KEY=your_mistral_key
```

## 📋 File Status Summary

### ✅ Implemented & Tested
- Complete scheduled sync infrastructure
- Cloud Scheduler integration
- Enhanced Xero sync with organisation support
- LLM-powered prepayment detection with attachment processing
- Proper URL generation for all document types
- Comprehensive testing suite
- Automated deployment scripts

### 🧹 Project Organization
- **Root Directory**: Clean and organized, only essential files
- **Tests**: Organized in `tests/` directory with dedicated subdirectories
- **Utilities**: Organized in `scripts/utilities/`
- **Documentation**: Complete with quick reference guides
- **Temporary Files**: All cleaned up and removed

### 📁 Clean Directory Structure
```
DRCR Backend/
├── cloud_functions/         # Cloud Functions code
├── drcr_shared_logic/       # Shared business logic
├── docs/                    # Complete documentation
├── rest_api/               # REST API implementation
├── scripts/                # Deployment and utility scripts
├── terraform/              # Infrastructure as code
├── tests/                  # All test files organized
├── README.md               # Main project documentation
└── [core project files]    # Essential configuration files only
```

For questions about documentation or to suggest improvements, contact the development team. 