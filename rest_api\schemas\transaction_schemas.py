"""
Transaction Schemas - Pydantic models for transaction API endpoints
Extracted from rest_api/routes/transactions.py for better organization
"""
from pydantic import BaseModel
from typing import List, Optional

from ..models import invoice as invoice_models, schedule as schedule_models


class PaginatedTransactionsResponse(BaseModel):
    """Response model for paginated transactions"""
    transactions: List[invoice_models.Invoice]
    total: int
    page: int
    limit: int
    total_pages: int


class DashboardTransactionItem(BaseModel):
    """Dashboard item containing transaction and its schedules"""
    transaction: invoice_models.Invoice
    schedules: List[schedule_models.Schedule]
    # We can add other specific dashboard-related computed fields here if needed in future


class PaginatedDashboardResponse(BaseModel):
    """Response model for paginated dashboard transactions"""
    transactions: List[DashboardTransactionItem]
    total: int
    page: int
    limit: int
    total_pages: int


class TransactionFilters(BaseModel):
    """Common filters for transaction queries"""
    client_id: Optional[str] = None
    entity_id: Optional[str] = None
    transaction_type: Optional[str] = None
    status: Optional[str] = None


class DashboardFilters(TransactionFilters):
    """Extended filters for dashboard queries"""
    require_action: bool = False


class PaginationParams(BaseModel):
    """Pagination parameters"""
    page: int = 1
    limit: int = 50
    
    class Config:
        json_schema_extra = {
            "example": {
                "page": 1,
                "limit": 50
            }
        } 