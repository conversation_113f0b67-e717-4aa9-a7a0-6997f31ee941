import React, { useState, useEffect, useMemo, useCallback } from 'react';

// --- Shadcn/UI & Lucide Imports ---
import { Button } from '@/components/ui/button';
import {
    Card,
    CardContent,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import {
    Loader2,
    Save,
    Settings,
    AlertCircle,
    Building,
    BookOpen,
    Clock,
    RefreshCw,
    DollarSign,
} from 'lucide-react';

// --- API Types ---
import type { Account } from '@/lib/api';

// --- Type Definitions ---

export type EntitySettingsData = {
    entityId: string;
    entityName: string;
    prepaymentAssetAccountCodes: string[];
    defaultExpenseAccountCode?: string | null;
    defaultAmortizationMonths?: number;
    autoSyncEnabled?: boolean;
    syncFrequency?: 'hourly' | 'daily' | 'weekly' | 'manual';
    syncSpendMoney?: boolean;
    excludedPnlAccountCodes?: string[];
    // Transaction sync settings
    transactionSyncStartDate?: string;
    // Individual sync data type toggles
    syncInvoices?: boolean;
    syncBills?: boolean;
    syncPayments?: boolean;
    syncBankTransactions?: boolean;
    syncJournalEntries?: boolean;
    // Additional settings
    autoPostProposedJournals?: boolean;
    baseCurrencyCode?: string;
    initialSyncCompleted?: boolean;
    lastFullSyncDate?: string;
};

// --- Component Props ---
interface EntitySettingsProps {
    entityId: string;
    // Function to fetch current settings for the entity
    fetchSettings: (entityId: string) => Promise<EntitySettingsData>;
    // Function to fetch the chart of accounts for the entity
    fetchChartOfAccounts: (entityId: string) => Promise<Account[]>;
    // Function to save updated settings
    saveSettings: (entityId: string, settings: Omit<EntitySettingsData, 'entityId' | 'entityName'>) => Promise<void>;
    onClose?: () => void; // Optional close handler if used in a modal
    hideFooter?: boolean; // New prop to hide the footer when used in dialog
    onSaveAction?: (saveHandler: () => Promise<void>, canSave: boolean, isSaving: boolean) => void; // Expose save handler
}

// --- Entity Settings Component ---
export function EntitySettingsManagement({
    entityId,
    fetchSettings,
    fetchChartOfAccounts,
    saveSettings,
    onClose,
    hideFooter = false,
    onSaveAction
}: EntitySettingsProps) {

    const [settings, setSettings] = useState<EntitySettingsData | null>(null);
    const [chartOfAccounts, setChartOfAccounts] = useState<Account[]>([]);
    const [selectedPrepaymentCodes, setSelectedPrepaymentCodes] = useState<Set<string>>(new Set());
    const [selectedDefaultExpenseCode, setSelectedDefaultExpenseCode] = useState<string | null>(null);
    const [defaultAmortizationMonths, setDefaultAmortizationMonths] = useState<number>(12);
    const [autoSyncEnabled, setAutoSyncEnabled] = useState<boolean>(true);
    const [syncFrequency, setSyncFrequency] = useState<'hourly' | 'daily' | 'weekly' | 'manual'>('daily');
    const [syncSpendMoney, setSyncSpendMoney] = useState<boolean>(true);
    const [selectedExcludedPnlCodes, setSelectedExcludedPnlCodes] = useState<Set<string>>(new Set());

    // Transaction sync settings
    const [transactionSyncStartDate, setTransactionSyncStartDate] = useState<string>('');

    // Individual sync data type toggles
    const [syncInvoices, setSyncInvoices] = useState<boolean>(true);
    const [syncBills, setSyncBills] = useState<boolean>(true);
    const [syncPayments, setSyncPayments] = useState<boolean>(true);
    const [syncBankTransactions, setSyncBankTransactions] = useState<boolean>(true);
    const [syncJournalEntries, setSyncJournalEntries] = useState<boolean>(true);

    // Additional settings
    const [autoPostProposedJournals, setAutoPostProposedJournals] = useState<boolean>(false);
    const [baseCurrencyCode, setBaseCurrencyCode] = useState<string>('USD');

    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [isSaving, setIsSaving] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);

    // Fetch initial data
    useEffect(() => {
        const loadData = async () => {
            setIsLoading(true);
            setError(null);
            try {
                console.log(`🔍 EntitySettings: Loading data for entity ${entityId}`);
                const [fetchedSettings, fetchedAccounts] = await Promise.all([
                    fetchSettings(entityId),
                    fetchChartOfAccounts(entityId)
                ]);
                console.log(`📊 EntitySettings: Got ${fetchedAccounts.length} accounts`);
                console.log(`📊 EntitySettings: First account:`, fetchedAccounts[0]);
                console.log(`📊 EntitySettings: All accounts:`, fetchedAccounts);
                setSettings(fetchedSettings);
                setChartOfAccounts(fetchedAccounts);
                setSelectedPrepaymentCodes(new Set(fetchedSettings.prepaymentAssetAccountCodes || []));
                setSelectedDefaultExpenseCode(fetchedSettings.defaultExpenseAccountCode || null);
                setDefaultAmortizationMonths(fetchedSettings.defaultAmortizationMonths || 12);
                setAutoSyncEnabled(fetchedSettings.autoSyncEnabled ?? true);
                setSyncFrequency(fetchedSettings.syncFrequency || 'daily');
                setSyncSpendMoney(fetchedSettings.syncSpendMoney ?? true);
                setSelectedExcludedPnlCodes(new Set(fetchedSettings.excludedPnlAccountCodes || []));
                setTransactionSyncStartDate(fetchedSettings.transactionSyncStartDate || '');
                setSyncInvoices(fetchedSettings.syncInvoices ?? true);
                setSyncBills(fetchedSettings.syncBills ?? true);
                setSyncPayments(fetchedSettings.syncPayments ?? true);
                setSyncBankTransactions(fetchedSettings.syncBankTransactions ?? true);
                setSyncJournalEntries(fetchedSettings.syncJournalEntries ?? true);
                setAutoPostProposedJournals(fetchedSettings.autoPostProposedJournals ?? false);
                setBaseCurrencyCode(fetchedSettings.baseCurrencyCode || 'USD');
            } catch (err: any) {
                console.error("Error loading entity settings:", err);
                setError(err.message || "Failed to load settings and accounts.");
            } finally {
                setIsLoading(false);
            }
        };
        loadData();
    }, [entityId, fetchSettings, fetchChartOfAccounts]);

    // Filter accounts for dropdowns - check raw_xero_data.Class for assets
    const assetAccounts = useMemo(() => {
        console.log(`🔍 EntitySettings: Filtering ${chartOfAccounts.length} accounts for assets`);
        console.log(`📊 EntitySettings: Sample account:`, chartOfAccounts[0]);
        console.log(`📊 EntitySettings: Sample account keys:`, chartOfAccounts[0] ? Object.keys(chartOfAccounts[0]) : 'No accounts');

        const assets = chartOfAccounts.filter(acc => {
            // Check both flattened class field and raw_xero_data.Class
            const hasClassField = acc.class === 'ASSET';
            const hasRawXeroClass = acc.raw_xero_data?.Class === 'ASSET';
            const hasReportingCodeAsset = acc.raw_xero_data?.ReportingCode?.startsWith('ASS');

            console.log(`Account ${acc.code}: class=${acc.class}, raw_xero_data.Class=${acc.raw_xero_data?.Class}, ReportingCode=${acc.raw_xero_data?.ReportingCode}`);

            return hasClassField || hasRawXeroClass || hasReportingCodeAsset;
        });

        console.log(`📊 EntitySettings: Found ${assets.length} asset accounts`);
        return assets;
    }, [chartOfAccounts]);

    const expenseAccounts = useMemo(() => {
        const expenses = chartOfAccounts.filter(acc =>
            acc.type === 'EXPENSE' || acc.raw_xero_data?.Class === 'EXPENSE'
        );
        console.log(`📊 EntitySettings: Found ${expenses.length} expense accounts`);
        return expenses;
    }, [chartOfAccounts]);

    // Handle Prepayment Account Selection (Multi-select simulation)
    const handlePrepaymentAccountToggle = (code: string, checked: boolean | 'indeterminate') => {
        setSelectedPrepaymentCodes(prev => {
            const newSet = new Set(prev);
            if (checked === true) {
                newSet.add(code);
            } else {
                newSet.delete(code);
            }
            return newSet;
        });
    };

    // Handle Save - wrap in useCallback to prevent infinite loops
    const handleSave = useCallback(async () => {
        setIsSaving(true);
        setError(null);
        const settingsToSave = {
            prepaymentAssetAccountCodes: Array.from(selectedPrepaymentCodes),
            defaultExpenseAccountCode: selectedDefaultExpenseCode,
            defaultAmortizationMonths: defaultAmortizationMonths,
            autoSyncEnabled: autoSyncEnabled,
            syncFrequency: syncFrequency,
            syncSpendMoney: syncSpendMoney,
            excludedPnlAccountCodes: Array.from(selectedExcludedPnlCodes),
            transactionSyncStartDate: transactionSyncStartDate,
            syncInvoices: syncInvoices,
            syncBills: syncBills,
            syncPayments: syncPayments,
            syncBankTransactions: syncBankTransactions,
            syncJournalEntries: syncJournalEntries,
            autoPostProposedJournals: autoPostProposedJournals,
            baseCurrencyCode: baseCurrencyCode,
        };
        try {
            await saveSettings(entityId, settingsToSave);
            // Optionally refetch settings or just update local state if API returns updated data
            setSettings(prev => prev ? { ...prev, ...settingsToSave } : null);
            onClose?.(); // Close dialog immediately after successful save
        } catch (err: any) {
             console.error("Error saving entity settings:", err);
             setError(err.message || "Failed to save settings.");
        } finally {
            setIsSaving(false);
        }
    }, [entityId, selectedPrepaymentCodes, selectedDefaultExpenseCode, defaultAmortizationMonths, autoSyncEnabled, syncFrequency, syncSpendMoney, selectedExcludedPnlCodes, transactionSyncStartDate, syncInvoices, syncBills, syncPayments, syncBankTransactions, syncJournalEntries, autoPostProposedJournals, baseCurrencyCode, saveSettings, onClose]);

    // Expose save handler to parent - remove handleSave from dependencies to prevent infinite loop
    useEffect(() => {
        if (onSaveAction) {
            onSaveAction(handleSave, selectedPrepaymentCodes.size > 0, isSaving);
        }
    }, [onSaveAction, selectedPrepaymentCodes.size, isSaving, handleSave]);

    if (isLoading) {
        if (hideFooter) {
            return (
                <div className="space-y-6">
                    <Skeleton className="h-6 w-1/2" />
                    <Skeleton className="h-4 w-1/3" />
                    <Skeleton className="h-5 w-1/4" />
                    <Skeleton className="h-10 w-full" />
                    <Skeleton className="h-5 w-1/4" />
                    <Skeleton className="h-10 w-full" />
                </div>
            );
        }
        return (
            <Card className="w-full max-w-2xl mx-auto mt-6">
                <CardHeader>
                    <Skeleton className="h-6 w-1/2" />
                    <Skeleton className="h-4 w-1/3" />
                </CardHeader>
                <CardContent className="space-y-6">
                    <Skeleton className="h-5 w-1/4" />
                    <Skeleton className="h-10 w-full" />
                    <Skeleton className="h-5 w-1/4" />
                    <Skeleton className="h-10 w-full" />
                </CardContent>
                <CardFooter className="flex justify-end">
                    <Skeleton className="h-9 w-24" />
                </CardFooter>
            </Card>
        );
    }

     if (error && !settings) { // Show error only if loading completely failed
        if (hideFooter) {
            return (
                <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Error Loading Settings</AlertTitle>
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            );
        }
        return (
             <Card className="w-full max-w-2xl mx-auto mt-6">
                 <CardHeader>
                     <CardTitle className="flex items-center text-destructive">
                         <AlertCircle className="h-5 w-5 mr-2"/> Error Loading Settings
                     </CardTitle>
                 </CardHeader>
                 <CardContent>
                      <Alert variant="destructive">
                        <AlertDescription>{error}</AlertDescription>
                    </Alert>
                 </CardContent>
             </Card>
        );
    }

     if (!settings) {
         // Should not happen if loading and error states are handled, but as a fallback
         return <p>Could not load entity settings.</p>;
     }

    const contentSection = (
        <div className="space-y-6">
             {/* Prepayment Asset Accounts Selection */}
             <div className="space-y-2">
                <Label className="font-semibold flex items-center">
                    <BookOpen className="h-4 w-4 mr-2"/> Prepayment Asset Accounts
                </Label>
                <p className="text-sm text-muted-foreground">
                    Select the Balance Sheet accounts used for recording prepayments.
                    (Used for v1 identification and journal postings).
                </p>
                <ScrollArea className="h-40 w-full rounded-md border p-2">
                    <div className="space-y-1">
                         {assetAccounts.length === 0 && <p className="text-xs text-muted-foreground text-center p-2">No asset accounts found in Chart of Accounts.</p>}
                         {assetAccounts.map((account) => (
                            <div key={account.code} className="flex items-center space-x-2 p-1 rounded hover:bg-muted">
                                <Checkbox
                                    id={`asset-${account.code}`}
                                    checked={selectedPrepaymentCodes.has(account.code)}
                                    onCheckedChange={(checked) => handlePrepaymentAccountToggle(account.code, checked)}
                                />
                                <label
                                    htmlFor={`asset-${account.code}`}
                                    className="text-xs font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex-grow"
                                >
                                    {account.code} - {account.name}
                                </label>
                            </div>
                        ))}
                    </div>
                </ScrollArea>
                {selectedPrepaymentCodes.size === 0 && <p className="text-xs text-red-600 pt-1">At least one prepayment account must be selected.</p>}
             </div>

            {/* Default Expense Account Selection */}
            <div className="space-y-2">
                <Label htmlFor="defaultExpenseAccount" className="font-semibold flex items-center">
                     <BookOpen className="h-4 w-4 mr-2"/> Default Expense Account (Optional)
                </Label>
                 <p className="text-sm text-muted-foreground">
                    Select a default P&L account to use if the expense account cannot be determined automatically.
                </p>
                <Select
                    value={selectedDefaultExpenseCode || ''}
                    onValueChange={(value) => setSelectedDefaultExpenseCode(value === 'none' ? null : value)}
                >
                    <SelectTrigger id="defaultExpenseAccount">
                        <SelectValue placeholder="Select default expense account..." />
                    </SelectTrigger>
                    <SelectContent>
                         <SelectItem value="none">(None)</SelectItem>
                         {expenseAccounts.map(acc => (
                            <SelectItem key={acc.code} value={acc.code}>
                                {acc.code} - {acc.name}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
            </div>

            {/* Sync Data Types */}
            <div className="space-y-4">
                <Label className="font-semibold flex items-center">
                    <RefreshCw className="h-4 w-4 mr-2"/> Data Synchronization Settings
                </Label>
                <p className="text-sm text-muted-foreground">
                    Configure which types of data to synchronize from your accounting system.
                </p>

                {/* Transaction Sync Start Date */}
                <div className="space-y-2">
                    <Label htmlFor="sync-start-date" className="font-medium">
                        Transaction Sync Start Date
                    </Label>
                    <p className="text-sm text-muted-foreground">
                        Only transactions from this date onwards will be synchronized. Leave empty to sync all historical data.
                    </p>
                    <Input
                        id="sync-start-date"
                        type="date"
                        value={transactionSyncStartDate}
                        onChange={(e) => setTransactionSyncStartDate(e.target.value)}
                        className="w-48"
                    />
                </div>

                {/* Individual Sync Data Type Toggles */}
                <div className="space-y-3">
                    <Label className="font-medium">Sync Data Types</Label>
                    <p className="text-sm text-muted-foreground">
                        Choose which types of transactions to synchronize from your accounting system.
                    </p>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {/* Sync Invoices */}
                        <div className="flex items-center justify-between p-3 border rounded-lg">
                            <div className="space-y-1">
                                <Label htmlFor="sync-invoices" className="font-medium text-sm">
                                    Invoices
                                </Label>
                                <p className="text-xs text-muted-foreground">
                                    Customer invoices and sales
                                </p>
                            </div>
                            <Switch
                                id="sync-invoices"
                                checked={syncInvoices}
                                onCheckedChange={setSyncInvoices}
                            />
                        </div>

                        {/* Sync Bills */}
                        <div className="flex items-center justify-between p-3 border rounded-lg">
                            <div className="space-y-1">
                                <Label htmlFor="sync-bills" className="font-medium text-sm">
                                    Bills
                                </Label>
                                <p className="text-xs text-muted-foreground">
                                    Vendor bills and purchases
                                </p>
                            </div>
                            <Switch
                                id="sync-bills"
                                checked={syncBills}
                                onCheckedChange={setSyncBills}
                            />
                        </div>

                        {/* Sync Payments */}
                        <div className="flex items-center justify-between p-3 border rounded-lg">
                            <div className="space-y-1">
                                <Label htmlFor="sync-payments" className="font-medium text-sm">
                                    Payments
                                </Label>
                                <p className="text-xs text-muted-foreground">
                                    Payment transactions
                                </p>
                            </div>
                            <Switch
                                id="sync-payments"
                                checked={syncPayments}
                                onCheckedChange={setSyncPayments}
                            />
                        </div>

                        {/* Sync Bank Transactions */}
                        <div className="flex items-center justify-between p-3 border rounded-lg">
                            <div className="space-y-1">
                                <Label htmlFor="sync-bank-transactions" className="font-medium text-sm">
                                    Bank Transactions
                                </Label>
                                <p className="text-xs text-muted-foreground">
                                    Bank account transactions
                                </p>
                            </div>
                            <Switch
                                id="sync-bank-transactions"
                                checked={syncBankTransactions}
                                onCheckedChange={setSyncBankTransactions}
                            />
                        </div>

                        {/* Sync Journal Entries */}
                        <div className="flex items-center justify-between p-3 border rounded-lg">
                            <div className="space-y-1">
                                <Label htmlFor="sync-journal-entries" className="font-medium text-sm">
                                    Journal Entries
                                </Label>
                                <p className="text-xs text-muted-foreground">
                                    Manual journal entries
                                </p>
                            </div>
                            <Switch
                                id="sync-journal-entries"
                                checked={syncJournalEntries}
                                onCheckedChange={setSyncJournalEntries}
                            />
                        </div>

                        {/* Sync Spend Money / Expenses */}
                        <div className="flex items-center justify-between p-3 border rounded-lg">
                            <div className="space-y-1">
                                <Label htmlFor="sync-spend-money" className="font-medium text-sm">
                                    Spend Money / Expenses
                                </Label>
                                <p className="text-xs text-muted-foreground">
                                    Spend Money (Xero) / Expenses (QuickBooks Online)
                                </p>
                            </div>
                            <Switch
                                id="sync-spend-money"
                                checked={syncSpendMoney}
                                onCheckedChange={setSyncSpendMoney}
                            />
                        </div>
                    </div>
                </div>

                {/* Auto Sync Settings */}
                <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="space-y-1">
                        <Label htmlFor="auto-sync" className="font-medium">
                            Auto Sync Enabled
                        </Label>
                        <p className="text-sm text-muted-foreground">
                            Automatically synchronize data on a schedule
                        </p>
                    </div>
                    <Switch
                        id="auto-sync"
                        checked={autoSyncEnabled}
                        onCheckedChange={setAutoSyncEnabled}
                    />
                </div>

                {/* Sync Frequency */}
                {autoSyncEnabled && (
                    <div className="space-y-2">
                        <Label htmlFor="sync-frequency" className="font-medium">
                            Sync Frequency
                        </Label>
                        <Select
                            value={syncFrequency}
                            onValueChange={(value: 'hourly' | 'daily' | 'weekly' | 'manual') => setSyncFrequency(value)}
                        >
                            <SelectTrigger id="sync-frequency">
                                <SelectValue placeholder="Select sync frequency..." />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="hourly">Hourly</SelectItem>
                                <SelectItem value="daily">Daily</SelectItem>
                                <SelectItem value="weekly">Weekly</SelectItem>
                                <SelectItem value="manual">Manual Only</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                )}
            </div>

            {/* Processing Settings */}
            <div className="space-y-4">
                <Label className="font-semibold flex items-center">
                    <Settings className="h-4 w-4 mr-2"/> Processing Settings
                </Label>

                {/* Default Amortization Period */}
                <div className="space-y-2">
                    <Label htmlFor="amortization-months" className="font-medium flex items-center">
                        <Clock className="h-4 w-4 mr-2"/> Default Amortization Period
                    </Label>
                    <p className="text-sm text-muted-foreground">
                        Default number of months to amortize prepayments over when period cannot be determined automatically.
                    </p>
                    <div className="flex items-center space-x-2">
                        <Input
                            id="amortization-months"
                            type="number"
                            min="1"
                            max="60"
                            value={defaultAmortizationMonths}
                            onChange={(e) => setDefaultAmortizationMonths(parseInt(e.target.value) || 12)}
                            className="w-24"
                        />
                        <span className="text-sm text-muted-foreground">months</span>
                    </div>
                </div>

                {/* Excluded P&L Accounts */}
                <div className="space-y-2">
                    <Label className="font-medium flex items-center">
                        <DollarSign className="h-4 w-4 mr-2"/> Excluded P&L Accounts (Optional)
                    </Label>
                    <p className="text-sm text-muted-foreground">
                        Select P&L accounts to exclude from prepayment analysis and processing.
                    </p>
                    <ScrollArea className="h-32 w-full rounded-md border p-2">
                        <div className="space-y-1">
                            {expenseAccounts.length === 0 && <p className="text-xs text-muted-foreground text-center p-2">No expense accounts found in Chart of Accounts.</p>}
                            {expenseAccounts.map((account) => (
                                <div key={account.code} className="flex items-center space-x-2 p-1 rounded hover:bg-muted">
                                    <Checkbox
                                        id={`exclude-${account.code}`}
                                        checked={selectedExcludedPnlCodes.has(account.code)}
                                        onCheckedChange={(checked) => {
                                            setSelectedExcludedPnlCodes(prev => {
                                                const newSet = new Set(prev);
                                                if (checked === true) {
                                                    newSet.add(account.code);
                                                } else {
                                                    newSet.delete(account.code);
                                                }
                                                return newSet;
                                            });
                                        }}
                                    />
                                    <label
                                        htmlFor={`exclude-${account.code}`}
                                        className="text-xs font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex-grow"
                                    >
                                        {account.code} - {account.name}
                                    </label>
                                </div>
                            ))}
                        </div>
                    </ScrollArea>
                </div>

                {/* Base Currency */}
                <div className="space-y-2">
                    <Label htmlFor="base-currency" className="font-medium">
                        Base Currency
                    </Label>
                    <p className="text-sm text-muted-foreground">
                        The base currency for this entity's financial data.
                    </p>
                    <Select
                        value={baseCurrencyCode}
                        onValueChange={setBaseCurrencyCode}
                    >
                        <SelectTrigger id="base-currency" className="w-48">
                            <SelectValue placeholder="Select currency..." />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="USD">USD - US Dollar</SelectItem>
                            <SelectItem value="EUR">EUR - Euro</SelectItem>
                            <SelectItem value="GBP">GBP - British Pound</SelectItem>
                            <SelectItem value="CAD">CAD - Canadian Dollar</SelectItem>
                            <SelectItem value="AUD">AUD - Australian Dollar</SelectItem>
                            <SelectItem value="NZD">NZD - New Zealand Dollar</SelectItem>
                            <SelectItem value="JPY">JPY - Japanese Yen</SelectItem>
                            <SelectItem value="CHF">CHF - Swiss Franc</SelectItem>
                        </SelectContent>
                    </Select>
                </div>

                {/* Auto Post Proposed Journals */}
                <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="space-y-1">
                        <Label htmlFor="auto-post-journals" className="font-medium">
                            Auto Post Proposed Journals
                        </Label>
                        <p className="text-sm text-muted-foreground">
                            Automatically post generated journal entries to your accounting system
                        </p>
                    </div>
                    <Switch
                        id="auto-post-journals"
                        checked={autoPostProposedJournals}
                        onCheckedChange={setAutoPostProposedJournals}
                    />
                </div>
            </div>

             {/* Display Save Error */}
             {error && (
                <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Save Error</AlertTitle>
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
             )}
        </div>
    );

    return (
        <Card className="w-full max-w-2xl mx-auto mt-6">
            <CardHeader>
                <CardTitle className="flex items-center">
                    <Settings className="h-5 w-5 mr-2" /> Entity Settings: {settings.entityName}
                </CardTitle>
                <CardDescription>
                    Configure prepayment and expense accounts for this entity.
                </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
                {contentSection}
            </CardContent>
            {!hideFooter && (
                <CardFooter className="flex justify-end space-x-2">
                     {onClose && <Button variant="outline" onClick={onClose} disabled={isSaving}>Cancel</Button>}
                     <Button onClick={handleSave} disabled={isSaving || selectedPrepaymentCodes.size === 0}>
                        {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                        Save Settings
                     </Button>
                </CardFooter>
            )}
        </Card>
    );
}