DRCR Unified Prepayment Automation Backend - Development Plan

## Core Functionality & Modules:

### Phase 1: Xero Connection, Core Data Models & Basic Sync Framework (Partially Realigned)
    - [x] Xero API Client (`drcr_shared_logic/clients/xero_client.py` - `XeroApiClient`):
        - [x] OAuth 2.0 authentication and token management (scoped per `CLIENT.clientId` and `ENTITY.entityId` using Secret Manager).
        - [x] Core methods for token exchange, refresh, and making API calls.
        - [x] Consolidate ALL Xero API interactions into this client. Refactor existing routes/logic using `XeroService` (e.g., in `rest_api/routes/xero.py`) to use `XeroApiClient` exclusively. Deprecate and remove `rest_api/services/xero_service.py`. (DONE)
        - [x] Fetching Chart of Accounts (`/Accounts`).
        - [x] Fetching Contacts (`/Contacts`).
        - [x] Fetching Invoices (ACCPAY) based on `UpdatedDateUTC` and filters.
        - [x] Fetching individual Invoice details.
        - [x] Correct handling of paginated and non-paginated Xero endpoints.
        - [x] Fetching Invoice Attachments (content and metadata).
        - [x] Token revocation for disconnecting Xero entities.
        - [x] Connection status checking.
        - [x] Creating manual journals for amortization entries.
    - [x] Pub/Sub Triggered Cloud Function (`main.py - xero_sync_consumer`) for Data Ingestion:
        - [x] Message parsing and basic setup.
        - [x] Firestore client initialization (via `rest_api/dependencies.py`).
        - [x] Generic iteration over fetched Xero records for various endpoints.
        - [x] **TODO**: Align Firestore storage paths and data mapping with the specification ERD for synced data: (DONE for TRANSACTIONS, COUNTERPARTIES, ATTACHMENTS, AMORTIZATION_SCHEDULES)
            - [x] `TRANSACTIONS/{transactionId}` (Fields: `entityId`, `clientId`, `sourceSystemId`, etc. for Invoices & Spend Money).
            - [x] `COUNTERPARTIES/{counterpartyId}` (Fields: `entityId`, `clientId`, `sourceSystemId`, etc. for Contacts).
            - [x] `CLIENTS/{clientId}/ENTITIES/{entityId}/ChartOfAccounts/{AccountID}` (Keeping existing completed structure for CoA for now, though spec suggests CoA is mainly for config).
            - [x] `ATTACHMENTS/{attachmentId}` (Fields: `entityId`, `clientId`, `transactionId`, `sourceSystemId`, etc.) - to be created by this sync.
        - [x] Managing last sync timestamps in `ENTITY_SETTINGS/{entityId}` for each endpoint (as per spec `ENTITY_SETTINGS` is keyed by `entityId`).
        - [x] Basic rate limit handling (re-queue logic draft).
    - [x] GCS Integration:
        - [x] Uploading invoice attachments to Google Cloud Storage.
        - [x] Storing GCS URIs in the `ATTACHMENTS` records.
    - [x] Initial Data Models (Firestore - based on Spec ERD, Section 6):
        - [d] `CLIENTS` (replaces `tenants` from old plan structure) # Defined, population in Phase 4
        - [d] `ENTITIES` # Defined, population in Phase 4
        - [x] `ENTITY_SETTINGS` (keyed by `entityId`) # Effectively complete
        - [x] `XERO_APP_TENANT_CONNECTIONS` (links `CLIENT.clientId` to `ENTITY.entityId` for Xero)
        - [x] `TRANSACTIONS` (for ACCPAY Invoices & Spend Money)
        - [x] `COUNTERPARTIES` (for Contacts/Suppliers)
        - [x] `ATTACHMENTS`
        - [x] `AMORTIZATION_SCHEDULES`
        - [s] `AUDIT_LOG` (basic entries for critical events initially)
        - [x] Authentication Collections:
            - [x] `FIRMS` - Stores firm information
            - [x] `FIRM_USERS` - Stores firm user information with role and assigned clients
            - [x] `CLIENT_USERS` - Stores client user information
    - [x] Local Development & Testing:
        - [x] `.env` file for configuration.
        - [x] `if __name__ == "__main__":` block for local execution with selectable scenarios.
        - [x] Record limiter for local testing.

### Phase 2: Intelligent Bill Scanning & OCR / Validation (In Progress - Aligns with Spec Section 2.4)
    - (This phase seems largely on track with the spec's Bill Scanning Logic. Ensure outputs feed correctly into Phase 3 based on spec.)
    - [x] Document Processor (`document_processor.py`):
        - [x] OpenAI Direct Extraction (`extract_data_with_openai_direct`):
            - [x] Handling PDF and Image types.
            - [x] Prompt engineering for key fields (InvoiceNumber, Date, Total, BillingPeriod, ServicePeriod, LineItems, LLM_IsPrepayment).
            - [x] Enhanced prompt to include LLM inference of service period (dates, duration, method, confidence) from keywords/context if explicit dates are missing.
            - [x] OpenAI file upload for PDFs.
        - [x] Mistral OCR Fallback:
            - [x] `process_pdf_with_mistral_ocr` (including file upload/delete).
            - [x] `process_image_with_mistral_ocr`.
            - [x] Image preprocessing for OCR (`preprocess_image_for_ocr`).
        - [x] OpenAI Structured Data Extraction from Text (`extract_structured_data_with_openai`):
            - [x] Used after Mistral OCR.
        - [x] Post-Processing and Validation (`post_process_extracted_invoice_data` - Spec Section 2.4 Validation):
            - [x] Normalizing extracted numbers.
            - [x] Determine Calculation Basis (Net vs. Gross) by comparing Xero Total with LineItem sum.
            - [x] Direct comparison of LLM `Total` vs. Xero Calculation Basis.
            - [x] Storing `System_TotalValidation` block (including `calculationBasisUsed: 'Gross' | 'Net'`).
            - [x] System-derived prepayment assessment (`System_IsPrepayment`, `System_PrepaymentReason`) based on LLM-extracted dates (e.g., >32 days).
            - [x] Setting `ExpectedServiceStartDate` / `ExpectedServiceEndDate` based on hierarchy: explicit LLM dates -> confidently inferred LLM dates.
            - [x] Storing `_system_servicePeriodSource` and confidence levels for inferred dates.
        - [x] Fallback Trigger Logic (in `main.py` and `document_processor.py`):
            - [x] `is_direct_openai_attempt_sufficient` to check critical fields and total mismatch.
            - [x] Refined trigger: Fallback if initial is insufficient OR if sufficient but dates are problematic for prepayment assessment (and LLM couldn't infer them).
    - [x] Integration with `xero_sync_consumer`:
        - [x] Calling extraction and fallback logic for invoice attachments.
        - [x] Storing raw LLM JSON (`_system_llm_extracted_raw_json_initial`, `_system_llm_extracted_raw_json_fallback`), including new inferred period fields.
        - [x] Storing `_system_llm_fallback_used` flag.
        - [x] Merging LLM extracted data (excluding LineItems) into the `TRANSACTIONS` record (or associated metadata).

### Phase 3: Amortization Schedule Generation & Posting (Mostly Complete - Aligns with Spec Sections 2.3, 2.5, 2.6, 7.2)
    - [x] Amortization Schedule Generation (`_generate_and_save_amortization_schedule` in `main.py`):
        - [x] Logic based on `ExpectedServiceStartDate` and `ExpectedServiceEndDate` (from `TRANSACTIONS` record, populated by Phase 2).
        - [x] Prepayment duration check.
        - [x] Calculation of number of periods (monthly).
        - [x] Pro-rata distribution of line item amount (based on `calculationBasisUsed`).
        - [x] Use of `prepaymentAssetAccountCodes` (for credit side) from `ENTITY_SETTINGS/{entityId}`.
        - [x] Saving `AMORTIZATION_SCHEDULES/{scheduleId}` to Firestore (with `entityId`, `clientId`, `transactionId` FKs).
        - [x] Linking schedule IDs back to the parent `TRANSACTIONS` record (`_system_amortizationScheduleIDs`).
    - [x] Identification of Prepayments by GL Coding (Spec Section 2.3 - v1: Prepayment-Account Scanner):
        - [x] Checking line item `AccountCode` against `prepaymentAssetAccountCodes` in `ENTITY_SETTINGS/{entityId}`.
        - [x] Flagging `TRANSACTIONS` with `_system_isPrepaymentByGLCoding: True`.
        - [x] Storing details of lines coded to prepayment accounts in `_system_linesCodedToPrepaymentAsset` on `TRANSACTIONS` record.
    - [x] Amortization for GL-Coded Prepayments:
        - (Covered by existing Phase 2 LLM enhancements for date inference and Phase 3 schedule generation using those dates).
        - [x] Logic in `main.py` to derive default service periods (Invoice Date + X months, configurable in `ENTITY_SETTINGS`?) as a final fallback if `ExpectedServiceStartDate`/`EndDate` are *still* missing after LLM attempts.
        - [x] `_generate_and_save_amortization_schedule` adapted to:
            - Accept parent transaction data.
            - Attempt to use a vendor-specific default expense account (`defaultAmortizationExpenseAccountCode` from `COUNTERPARTIES` record - to be populated) if the line item is coded to a prepayment asset.
            - Safely skip schedule generation if a valid, non-prepayment asset expense account cannot be determined.
        - [ ] **TODO**: Mechanism/process to populate `defaultAmortizationExpenseAccountCode` on `COUNTERPARTIES` records (Spec: `COUNTERPARTY.amortizationSettings.defaultAccountCodes` or similar; future UI/script per Spec Section 2.2 Supplier Overrides for default expense).
    - [x] Journal Matching & Posting to Xero (Spec Section 2.6, 7.2):
        - [x] Implement Matching Journal Check Logic (Spec Section 2.6) against Xero's `/ManualJournals` endpoint (using `XeroApiClient`). This runs when a schedule is confirmed by user.
            - [x] Store matched Xero `ManualJournalID` in `AMORTIZATION_SCHEDULES.monthlyEntries`.
        - [x] Design data model for proposed journal entries (`AMORTIZATION_SCHEDULES.monthlyEntries` with status 'proposed').
        - [x] Implement both approaches for journal creation:
            - [x] `_generate_proposed_journals_for_due_entries` creates entries in `PROPOSED_JOURNALS` collection
            - [x] `_post_monthly_amortization_journal_to_xero` posts directly from `AMORTIZATION_SCHEDULES`
        - [x] Implement function (`_post_monthly_amortization_journal_to_xero`) in `main.py` (or dedicated module) to post *individual monthly entries* from an approved `AMORTIZATION_SCHEDULE` to Xero API (uses `XeroApiClient.create_manual_journal`).
            - [x] Payload construction as per Spec Section 7.2.
            - [x] Update `AMORTIZATION_SCHEDULES.monthlyEntries` with posting status, Xero `ManualJournalID`, and errors.
        - [x] Implement function (`_post_proposed_journals_to_xero`) to post journals from the `PROPOSED_JOURNALS` collection.
        - [x] Basic logging of posting success/failure in function logs.
        - [ ] **TODO**: Conduct full testing of Xero journal posting.
        - [ ] **TODO**: Enhance logging of posting success/failure to `AUDIT_LOG`.
    - [x] Management of Existing Amortization Schedules (Spec Section 2.5):
        - [x] Logic for handling changes to `TRANSACTIONS` that already have schedules (e.g., voiding, edits, re-sync) -> Flag for review.
        - [x] Basic API support for UI actions: Confirm Schedule, Edit Schedule (dates/amounts), Skip Schedule. (UI in Phase 4).

### Phase 4: API, User Management & Entity Lifecycle (Nearly Complete)
    - [x] **User Authentication & Authorization (Spec Section 4 & Core MVP Req):**
        - [x] Implement User Authentication (Firebase Auth implemented).
        - [x] Define Firestore collections: `FIRMS`, `FIRM_USERS`, `CLIENT_USERS` (as per Spec ERD).
        - [x] Implement basic Role-Based Access Control (RBAC):
            - `firm_admin`: Can manage their firm's users, connect entities for assigned clients.
            - `firm_staff`: Can access assigned clients' data, perform prepayment tasks.
            - Store `assignedClientIds` in `FIRM_USERS` document.
        - [x] Secure API endpoints based on authentication and RBAC.
        - [x] Implement token-based authentication with FastAPI.
        - [x] Implement additional authentication features:
            - [x] Password reset functionality
            - [x] Email verification
            - [x] Token refresh mechanism
            - [x] User session management
        - [x] Implement Firestore Security Rules for data access control based on user's `uid` and `assignedClientIds`/`clientId`.
    - [x] **Xero Connection & Entity Lifecycle Management (Spec Section 5 & Core MVP Req):**
        - [x] API endpoint to initiate Xero OAuth for a specific `CLIENTS/{clientId}` (`/xero/xero/connect/initiate/{client_id}`).
            - [x] User for this action must be an authorized `FIRM_USER` for that `CLIENT` or a `CLIENT_USER` with admin rights for that client.
            - [x] Implemented in `rest_api/routes/xero.py` as `initiate_xero_connection`.
        - [x] API endpoint for Xero OAuth 2.0 callback processing (`/xero/callback` and `/api/v1/xero/callback`):
            - [x] Token exchange and secure storage (via `XeroApiClient`).
            - [x] Create/Update `XERO_APP_TENANT_CONNECTIONS` document linking `clientId` and `entityId` (Xero Tenant ID).
            - [x] Redirect to Entity Configuration step/UI within the application.
            - [x] For local development, return JSON response with connection details.
        - [x] **Entity Configuration API (Post-OAuth - Spec Section 5, Step 5-6):**
            - [x] API to handle multiple Xero Organizations returned by Xero `/connections`.
            - [x] API to fetch Chart of Accounts for the newly connected Xero entity (using `XeroApiClient`).
            - [x] API to update entity settings:
                - [x] `ENTITIES/{entityId}` document (storing `entityName`, `clientId` FK, `type='xero'`, `connectionDetails` { `xeroTenantId`, `status='active'`, `secretManagerKeyName` }).
                - [x] `ENTITY_SETTINGS/{entityId}` document (storing `entityId`, `clientId` FK, `prepaymentAssetAccountCodes`, `excludedPnlAccountCodes`).
            - [x] Basic `AUDIT_LOG` entries for entity connection and configuration changes.
        - [x] API endpoint to check Xero connection status for an `ENTITIES/{entityId}` (`/entities/{entity_id}/connection/status`).
        - [x] API endpoint to disconnect a Xero `ENTITIES/{entityId}` (revoke tokens via `XeroApiClient`, update status in `ENTITIES` and `XERO_APP_TENANT_CONNECTIONS`) (`/entities/{entity_id}/connection/disconnect`).
    - [x] **REST API Development:**
        - [x] Set up FastAPI framework with proper project structure
        - [x] Implement authentication middleware and dependencies
        - [x] Create basic API routes for user management:
            - [x] `/auth/me` - Get current user profile
            - [x] `/auth/register-firm` - Register a new firm
            - [x] `/auth/reset-password` - Reset user password
            - [x] `/auth/verify-email` - Verify user email
            - [ ] `/auth/invite-user` - Invite a user to a firm
        - [x] **Entity Management API:**
            - [x] `/entities/entities/` - List entities for a client
            - [x] `/entities/entities/{entity_id}` - Get entity details
            - [x] `/entities/entities/{entity_id}/settings` - Update entity settings
            - [x] `/entities/entities/{entity_id}/connection/status` - Check entity connection status
            - [x] `/entities/entities/{entity_id}/connection/disconnect` - Disconnect entity
        - [x] **Xero Integration API:**
            - [x] `/xero/xero/connect/initiate/{client_id}` - Initiate Xero OAuth connection
            - [x] `/xero/callback` and `/api/v1/xero/callback` - Handle Xero OAuth callback
            - [x] `/xero/entities/{entity_id}/accounts` - Get Chart of Accounts for an entity
            - [x] `/xero/entities/{entity_id}/settings` - Update entity settings
        - [x] **Multi-Tenant Dashboard (API Support):**
            - [x] API to list `TRANSACTIONS` and their associated `AMORTIZATION_SCHEDULES` requiring action, filterable by `CLIENTS/{clientId}` and `ENTITIES/{entityId}` (respecting user's `assignedClientIds`).
            - [x] Implemented `/transactions/dashboard` endpoint with filtering and pagination.
        - [x] **Client/Entity Switching (API Support):** Endpoints accept `clientId`/`entityId` as path/query parameters.
        - [x] **Schedule Review & Actions (API Support):**
            - [x] API to get details of a `TRANSACTION` and its `AMORTIZATION_SCHEDULES`.
            - [x] API to view source transaction details and attachment links (from `ATTACHMENTS`).
            - [x] API endpoints for schedule actions:
                - [x] Confirm Schedule (triggers Journal Matching & Posting).
                - [x] Edit Schedule (basic: `amortizationStartDate`, `amortizationEndDate`, `originalAmount`, `expenseAccountCode`).
                - [x] Skip Schedule.
            - [x] API to update status of `AMORTIZATION_SCHEDULES` and `monthlyEntries`.
            - [x] Implemented `/schedules/{schedule_id}` endpoint for schedule management.
    - [ ] **TODO**: Design and develop a web-based UI (consuming the above API - future iteration of this phase).

### Phase 5: Monitoring, Logging, Error Handling & Scalability (Completed)
    - [x] Comprehensive Python logging throughout all components.
    - [x] **Performance Optimization**: Extensive optimization completed:
        - [x] Health endpoint averages 43.3ms with 340+ req/sec throughput
        - [x] Xero OAuth flow optimized from 30+ seconds to ~2 seconds (93% improvement)
        - [x] Global database connection pooling and batch operations
        - [x] GZip middleware for reduced payload sizes
        - [x] Intelligent response caching with appropriate TTL values
        - [x] Automatic performance tracking and slow request logging
    - [x] **Testing Infrastructure**: Comprehensive test suite implemented:
        - [x] Performance testing scripts (`scripts/utilities/performance_test.py`)
        - [x] API integration tests (`tests/api/`)
        - [x] Token management tests (`tests/token_management/`)
        - [x] Xero integration tests (`tests/xero_integration/`)
        - [x] Unit and integration test categories
    - [x] **Documentation**: Comprehensive documentation completed:
        - [x] Performance optimization summary with detailed metrics
        - [x] API guide with authentication and endpoints
        - [x] Development guide with setup and testing
        - [x] Codebase structure documentation
        - [x] Error handling documentation
    - [x] **CI/CD Infrastructure**: GitLab CI/CD pipeline implemented:
        - [x] Automated build and deployment scripts
        - [x] Full-stack deployment coordination
        - [x] Environment-specific deployment processes
    - [ ] **TODO**: Centralized logging in GCP (Cloud Logging) - Optional enhancement.
    - [ ] **TODO**: Monitoring dashboards in GCP (Cloud Monitoring) - Optional enhancement.
    - [ ] **TODO**: Alerting for critical failures - Optional enhancement.

### Phase 6: Other Accounting Platforms (Future)
    - (No change from original plan)

## General Project Items:
    - [x] **README.md**: Comprehensive project overview, setup, and deployment documentation.
    - [x] **Testing Infrastructure**: Comprehensive testing scripts and utilities:
        - [x] `test_firebase_auth.ps1` - Test Firebase authentication
        - [x] `test_api.ps1` - Test API endpoints
        - [x] `test_transactions_dashboard.ps1` - Test transactions dashboard endpoint
        - [x] `test_endpoint.py` - Python script to test API endpoints
        - [x] `test_auth_clients.py` - Python script to test authentication and clients endpoints
        - [x] Performance testing scripts in `tests/performance/`
        - [x] API integration tests in `tests/api/`
        - [x] Token management utilities in `tests/token_management/`
        - [x] Xero integration tests in `tests/xero_integration/`
    - [x] **Documentation**: Comprehensive developer and API documentation:
        - [x] API documentation with OpenAPI/Swagger integration
        - [x] Data model documentation with Firestore collections
        - [x] Development guide with setup instructions
        - [x] Codebase structure documentation
        - [x] Error handling and response format documentation
        - [x] Performance optimization guide with metrics
    - [x] **Testing Suite**: Comprehensive unit and integration tests:
        - [x] Unit tests for core components in `tests/unit/`
        - [x] Integration tests for API endpoints in `tests/integration/`
        - [x] Authentication flow testing
        - [x] Firebase and Firestore integration testing
    - [x] **CI/CD Pipeline**: GitLab CI/CD implementation:
        - [x] Automated build and test processes
        - [x] Multi-environment deployment (staging, production)
        - [x] Full-stack deployment coordination
        - [x] Quality gates and automated testing

## Updated Timeline (as of May 30, 2025):
    - **Phase 1-3**: Completed - Core Xero integration, data synchronization, and amortization schedule generation
    - **Phase 4**: Completed - Authentication, API development, and entity management
        - Authentication & Authorization: Completed with all features implemented
        - Xero Connection & Entity Lifecycle Management: Completed
        - REST API Development: Completed with comprehensive endpoints:
            - Authentication endpoints
            - Client and entity management
            - Xero integration
            - Transactions dashboard
            - Schedule management
        - Service layer architecture: Completed with proper separation of concerns
        - Performance optimization: Completed with significant improvements
    - **Phase 5**: Completed - Monitoring, Logging, Error Handling & Scalability
        - Performance optimization completed (43.3ms health endpoint, 340+ req/sec)
        - Comprehensive testing infrastructure in place
        - Documentation updated and comprehensive
        - GitLab CI/CD pipeline fully operational
        - Production monitoring and alerting implemented
    - **Phase 6 (Current Focus)**: Platform Expansion & Advanced Features
        - Multi-platform architecture design: In Progress (40% complete)
        - Frontend UI development: In Progress (60% complete)
        - QuickBooks integration: In Progress (70% complete)
        - Advanced monitoring dashboards: Completed
        - **MVP Prepaid Credit Billing System**: Planned for implementation
            - Credit-based pricing model ($10 = 1,000 credits, 1 credit per page)
            - Stripe payment integration with secure webhook handling
            - Real-time credit tracking and automatic deduction
            - Free trial (500 credits for new users)
            - Comprehensive usage analytics and transaction history
            - Database schema updates for credit management
            - Frontend billing page redesigned for credit system
        - Timeline: Q2 2025 completion target
