#!/bin/sh
# Script for preparing frontend build environment

echo "Current directory: $(pwd)"
echo "Listing root directory:"
ls -la

# Проверяем, находимся ли мы в директории app
if [ -d "../drcr_front" ]; then
  echo "Found drcr_front in parent directory, moving up..."
  cd ..
  echo "New current directory: $(pwd)"
fi

# Проверяем наличие директории drcr_front
if [ ! -d "drcr_front" ]; then
  echo "Error: drcr_front directory not found in $(pwd)!"
  echo "Listing all directories:"
  find . -type d -maxdepth 2
  exit 1
fi

cd drcr_front
echo "Current directory after cd: $(pwd)"

# Создаем .env файл для production сборки, если он не существует
if [ ! -f ".env.production" ]; then
  echo "Creating .env.production file..."
  echo "VITE_API_BASE_URL=https://drcr-backend-prod-dot-drcr-d660a.run.app" > .env.production
  echo "VITE_FIREBASE_PROJECT_ID=drcr-d660a" >> .env.production
  echo "VITE_ENVIRONMENT=production" >> .env.production
fi

echo "✅ Frontend build environment prepared successfully!"
