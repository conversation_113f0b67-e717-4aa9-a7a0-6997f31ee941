#!/bin/bash
# Firebase deployment script for GitLab CI

# Verify environment
echo "Node.js version:" && node --version
echo "npm version:" && npm --version
echo "Verifying environment files..."
ls -la
cat firebase.json
cat .firebaserc

# Install Firebase CLI
echo "Installing Firebase CLI..."
npm install -g firebase-tools

# Check Firebase token
echo "Checking if FIREBASE_TOKEN is set..."
if [ -z "$FIREBASE_TOKEN" ]; then 
  echo "FIREBASE_TOKEN is not set. Please add it in CI/CD Variables."
  exit 1
fi

# Deploy to Firebase
echo "Deploying to Firebase Hosting..."
firebase deploy --only hosting --token "$FIREBASE_TOKEN" --non-interactive

# Verify deployment
echo "Deployment completed!"
echo "Application is now live at: ${FIREBASE_HOSTING_URL}"