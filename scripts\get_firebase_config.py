#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to get Firebase web app configuration for the drcr-d660a project.
This will help us get the correct configuration for the frontend.
"""

import os
import json
import requests
from google.oauth2 import service_account
from google.auth.transport.requests import Request

def get_firebase_web_config():
    """Get Firebase web app configuration using service account credentials."""
    
    # Path to the Firebase service account credentials
    credentials_path = "drcr-d660a-firebase-adminsdk-fbsvc-f1d2dc57df.json"
    project_id = "drcr-d660a"
    
    if not os.path.exists(credentials_path):
        print(f"❌ Credentials file not found: {credentials_path}")
        print("Please ensure the Firebase service account key file exists.")
        return None
    
    try:
        # Load service account credentials
        credentials = service_account.Credentials.from_service_account_file(
            credentials_path,
            scopes=['https://www.googleapis.com/auth/firebase']
        )
        
        # Refresh credentials to get access token
        credentials.refresh(Request())
        access_token = credentials.token
        
        print(f"✅ Successfully authenticated with Firebase project: {project_id}")
        
        # Get Firebase project configuration
        url = f"https://firebase.googleapis.com/v1beta1/projects/{project_id}/webApps"
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            web_apps = response.json().get('apps', [])
            
            if not web_apps:
                print("❌ No web apps found in this Firebase project.")
                print("You need to create a web app in the Firebase Console:")
                print("1. Go to https://console.firebase.google.com/")
                print(f"2. Select project '{project_id}'")
                print("3. Go to Project Settings > General")
                print("4. Click 'Add app' and select 'Web'")
                return None
            
            # Get the first web app (or you can choose a specific one)
            web_app = web_apps[0]
            app_id = web_app['appId']
            
            print(f"✅ Found web app: {web_app.get('displayName', 'Unnamed')} ({app_id})")
            
            # Get the web app configuration
            config_url = f"https://firebase.googleapis.com/v1beta1/projects/{project_id}/webApps/{app_id}/config"
            config_response = requests.get(config_url, headers=headers)
            
            if config_response.status_code == 200:
                config_data = config_response.json()
                
                # Extract the configuration
                firebase_config = {
                    'apiKey': config_data.get('apiKey'),
                    'authDomain': config_data.get('authDomain'),
                    'projectId': config_data.get('projectId'),
                    'storageBucket': config_data.get('storageBucket'),
                    'messagingSenderId': config_data.get('messagingSenderId'),
                    'appId': config_data.get('appId')
                }
                
                print("\n🎉 Firebase Web App Configuration:")
                print("=" * 50)
                for key, value in firebase_config.items():
                    print(f"{key}: {value}")
                
                print("\n📋 Frontend .env configuration:")
                print("=" * 50)
                print("# Firebase Configuration")
                print(f"VITE_FIREBASE_API_KEY={firebase_config['apiKey']}")
                print(f"VITE_FIREBASE_AUTH_DOMAIN={firebase_config['authDomain']}")
                print(f"VITE_FIREBASE_PROJECT_ID={firebase_config['projectId']}")
                print(f"VITE_FIREBASE_STORAGE_BUCKET={firebase_config['storageBucket']}")
                print(f"VITE_FIREBASE_MESSAGING_SENDER_ID={firebase_config['messagingSenderId']}")
                print(f"VITE_FIREBASE_APP_ID={firebase_config['appId']}")
                
                return firebase_config
            else:
                print(f"❌ Failed to get web app config: {config_response.status_code}")
                print(config_response.text)
                return None
        else:
            print(f"❌ Failed to get web apps: {response.status_code}")
            print(response.text)
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def check_firebase_auth_users():
    """Check if there are any users in Firebase Authentication."""
    print("\n🔍 Checking Firebase Authentication users...")
    print("To check users, go to:")
    print(f"https://console.firebase.google.com/project/drcr-d660a/authentication/users")

if __name__ == "__main__":
    print("🔥 Firebase Configuration Extractor")
    print("=" * 50)
    
    config = get_firebase_web_config()
    
    if config:
        check_firebase_auth_users()
        print("\n✅ Next steps:")
        print("1. Copy the VITE_* environment variables to your frontend .env file")
        print("2. Ensure Firebase Authentication is enabled with Email/Password provider")
        print("3. Create test users in Firebase Console or use existing ones")
        print("4. Test the login flow!")
    else:
        print("\n❌ Could not retrieve Firebase configuration.")
        print("Please check your credentials and try again.")
