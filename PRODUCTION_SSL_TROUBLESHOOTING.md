# Production SSL Certificate Issue - Troubleshooting Guide

## Current Issues Identified

### 1. SSL Certificate Error
**Error**: `ERR_CERT_COMMON_NAME_INVALID` for `drcr-backend-prod-dot-drcr-d660a.run.app`

**Root Cause**: The SSL certificate for the Google Cloud Run service doesn't match the auto-generated domain name.

### 2. Missing Autocomplete Attributes
**Error**: DOM warning about missing autocomplete attributes on password inputs
**Status**: ✅ **FIXED** - Added `autoComplete="current-password"` and `autoComplete="email"` to login form

### 3. Environment Configuration
**Issue**: Frontend using localhost backend URL in production
**Status**: ✅ **FIXED** - Created `.env.production` with correct backend URL

## Solutions

### Immediate Fix Options

#### Option 1: Use Direct Cloud Run URL (Recommended)
1. Go to Google Cloud Console → Cloud Run
2. Find the `drcr-backend-prod` service
3. Copy the exact service URL (should be something like `https://drcr-backend-prod-[hash]-uc.a.run.app`)
4. Update `.env.production` with the correct URL

#### Option 2: Configure Custom Domain (Long-term solution)
1. Set up a custom domain for the backend (e.g., `api.drcr.com`)
2. Configure SSL certificate for the custom domain
3. Update DNS records
4. Update CORS configuration in backend

### Backend CORS Configuration Update

The backend CORS configuration needs to include the production frontend URL. Update `rest_api/main.py`:

```python
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5173",  # Vite dev server
        "http://localhost:3000",  # Alternative dev server
        "https://drcr-d660a.web.app",  # Firebase hosting
        "https://drcr-d660a.firebaseapp.com",  # Firebase hosting alternative
        "https://drcr-d660a--staging.web.app",  # Firebase staging (if used)
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)
```

### Testing Steps

1. **Build with production environment**:
   ```bash
   npm run build
   ```

2. **Deploy to Firebase**:
   ```bash
   firebase deploy
   ```

3. **Test the application**:
   - Check browser console for SSL errors
   - Verify API calls are working
   - Test authentication flow

### Verification Checklist

- [ ] SSL certificate error resolved
- [ ] API calls working from production frontend
- [ ] Authentication flow working
- [ ] No CORS errors in browser console
- [ ] No autocomplete warnings in browser console

## Additional Notes

- The current backend URL `drcr-backend-prod-dot-drcr-d660a.run.app` appears to be using a custom domain mapping
- If SSL issues persist, consider using the direct Cloud Run service URL
- Monitor Cloud Run logs for any backend errors during testing

## Next Steps

1. Apply the immediate fixes (autocomplete attributes - already done)
2. Get the correct backend URL from Google Cloud Console
3. Update `.env.production` with the correct URL
4. Rebuild and redeploy the frontend
5. Test the complete authentication and API flow
