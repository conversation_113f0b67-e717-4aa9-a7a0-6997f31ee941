import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Activity, 
  AlertCircle, 
  CheckCircle, 
  Clock, 
  Database, 
  RefreshCw, 
  Settings, 
  TrendingUp, 
  Wifi, 
  WifiOff,
  Zap,
  BarChart3,
  FileText,
  Users,
  Calendar,
  AlertTriangle,
  Info
} from 'lucide-react';
import { EntitiesService } from '@/services/entities.service';
import type { 
  EntityDashboardData, 
  EntityHealthMetrics, 
  SyncStatus, 
  ActivityItem,
  ConnectionStatus 
} from '@/types/entity.types';

interface EntityDashboardProps {
  entityId: string;
  onSettingsClick?: () => void;
  onSyncClick?: () => void;
}

export function EntityDashboard({ entityId, onSettingsClick, onSyncClick }: EntityDashboardProps) {
  const [dashboardData, setDashboardData] = useState<EntityDashboardData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSyncing, setIsSyncing] = useState(false);

  useEffect(() => {
    loadDashboardData();
    // Set up polling for real-time updates
    const interval = setInterval(loadDashboardData, 30000); // Poll every 30 seconds
    return () => clearInterval(interval);
  }, [entityId]);

  const loadDashboardData = async () => {
    try {
      setError(null);
      const data = await EntitiesService.getEntityDashboard(entityId);
      setDashboardData(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleManualSync = async () => {
    try {
      setIsSyncing(true);
      await EntitiesService.triggerSync(entityId);
      // Refresh dashboard data after triggering sync
      setTimeout(loadDashboardData, 2000);
      onSyncClick?.();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to trigger sync');
    } finally {
      setIsSyncing(false);
    }
  };

  const getConnectionStatusInfo = (status: ConnectionStatus) => {
    switch (status) {
      case 'connected':
        return { icon: <Wifi className="h-4 w-4" />, color: 'text-green-600', bg: 'bg-green-100', label: 'Connected' };
      case 'disconnected':
        return { icon: <WifiOff className="h-4 w-4" />, color: 'text-gray-600', bg: 'bg-gray-100', label: 'Disconnected' };
      case 'error':
        return { icon: <AlertCircle className="h-4 w-4" />, color: 'text-red-600', bg: 'bg-red-100', label: 'Error' };
      case 'syncing':
        return { icon: <RefreshCw className="h-4 w-4 animate-spin" />, color: 'text-blue-600', bg: 'bg-blue-100', label: 'Syncing' };
      case 'pending':
        return { icon: <Clock className="h-4 w-4" />, color: 'text-yellow-600', bg: 'bg-yellow-100', label: 'Pending' };
      case 'expired':
        return { icon: <AlertTriangle className="h-4 w-4" />, color: 'text-orange-600', bg: 'bg-orange-100', label: 'Expired' };
      default:
        return { icon: <AlertCircle className="h-4 w-4" />, color: 'text-gray-600', bg: 'bg-gray-100', label: 'Unknown' };
    }
  };

  const getHealthScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getActivityIcon = (type: ActivityItem['type']) => {
    switch (type) {
      case 'sync':
        return <RefreshCw className="h-4 w-4" />;
      case 'error':
        return <AlertCircle className="h-4 w-4" />;
      case 'connection':
        return <Wifi className="h-4 w-4" />;
      case 'settings':
        return <Settings className="h-4 w-4" />;
      case 'data':
        return <Database className="h-4 w-4" />;
      default:
        return <Info className="h-4 w-4" />;
    }
  };

  const getActivityStatusColor = (status: ActivityItem['status']) => {
    switch (status) {
      case 'success':
        return 'text-green-600';
      case 'error':
        return 'text-red-600';
      case 'warning':
        return 'text-yellow-600';
      case 'info':
        return 'text-blue-600';
      default:
        return 'text-gray-600';
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (!dashboardData) {
    return (
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>No dashboard data available</AlertDescription>
      </Alert>
    );
  }

  const { entity, health_metrics, sync_status, recent_activity, data_summary } = dashboardData;
  const connectionInfo = getConnectionStatusInfo(entity.connection_details.status);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">{entity.entity_name}</h2>
          <p className="text-gray-500">
            {entity.type.toUpperCase()} Entity • Created {new Date(entity.created_at || '').toLocaleDateString()}
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={onSettingsClick}>
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
          <Button 
            onClick={handleManualSync} 
            disabled={isSyncing || sync_status.is_syncing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${(isSyncing || sync_status.is_syncing) ? 'animate-spin' : ''}`} />
            {isSyncing || sync_status.is_syncing ? 'Syncing...' : 'Sync Now'}
          </Button>
        </div>
      </div>

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Connection Status */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Connection</p>
                <p className="text-2xl font-bold">{connectionInfo.label}</p>
              </div>
              <div className={`p-3 rounded-full ${connectionInfo.bg}`}>
                <div className={connectionInfo.color}>
                  {connectionInfo.icon}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Health Score */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Health Score</p>
                <p className={`text-2xl font-bold ${getHealthScoreColor(health_metrics.data_quality_score)}`}>
                  {health_metrics.data_quality_score}%
                </p>
              </div>
              <div className="p-3 rounded-full bg-blue-100">
                <TrendingUp className="h-4 w-4 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Pending Items */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Pending Items</p>
                <p className="text-2xl font-bold">{health_metrics.pending_items_count}</p>
              </div>
              <div className="p-3 rounded-full bg-yellow-100">
                <Clock className="h-4 w-4 text-yellow-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Error Count */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Errors</p>
                <p className="text-2xl font-bold text-red-600">{health_metrics.error_items_count}</p>
              </div>
              <div className="p-3 rounded-full bg-red-100">
                <AlertCircle className="h-4 w-4 text-red-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Sync Status */}
      {sync_status.is_syncing && (
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <RefreshCw className="h-5 w-5 animate-spin text-blue-600 mr-2" />
                <div>
                  <h3 className="font-semibold">Sync in Progress</h3>
                  <p className="text-sm text-gray-500">{sync_status.current_operation}</p>
                </div>
              </div>
              <Badge variant="outline">
                {sync_status.sync_progress}% Complete
              </Badge>
            </div>
            <Progress value={sync_status.sync_progress} className="mb-2" />
            {sync_status.estimated_completion && (
              <p className="text-xs text-gray-500">
                Estimated completion: {new Date(sync_status.estimated_completion).toLocaleTimeString()}
              </p>
            )}
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Health Metrics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Activity className="h-5 w-5 mr-2" />
              Health Metrics
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-500">Connection Uptime</span>
                <span className="font-medium">{health_metrics.connection_uptime}%</span>
              </div>
              <Progress value={health_metrics.connection_uptime} />
            </div>

            <Separator />

            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-gray-500">Last Sync</p>
                <p className="font-medium">
                  {health_metrics.last_successful_sync 
                    ? new Date(health_metrics.last_successful_sync).toLocaleString()
                    : 'Never'
                  }
                </p>
              </div>
              <div>
                <p className="text-gray-500">Sync Errors</p>
                <p className="font-medium">{health_metrics.sync_error_count}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Data Summary */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="h-5 w-5 mr-2" />
              Data Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="flex items-center justify-center mb-2">
                  <Database className="h-5 w-5 text-blue-600" />
                </div>
                <p className="text-2xl font-bold text-blue-600">{data_summary.accounts_count}</p>
                <p className="text-sm text-gray-500">Accounts</p>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="flex items-center justify-center mb-2">
                  <FileText className="h-5 w-5 text-green-600" />
                </div>
                <p className="text-2xl font-bold text-green-600">{data_summary.transactions_count}</p>
                <p className="text-sm text-gray-500">Transactions</p>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="flex items-center justify-center mb-2">
                  <FileText className="h-5 w-5 text-purple-600" />
                </div>
                <p className="text-2xl font-bold text-purple-600">{data_summary.invoices_count}</p>
                <p className="text-sm text-gray-500">Invoices</p>
              </div>
              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <div className="flex items-center justify-center mb-2">
                  <Calendar className="h-5 w-5 text-orange-600" />
                </div>
                <p className="text-sm font-bold text-orange-600">
                  {new Date(data_summary.last_updated).toLocaleDateString()}
                </p>
                <p className="text-sm text-gray-500">Last Updated</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="h-5 w-5 mr-2" />
            Recent Activity
          </CardTitle>
          <CardDescription>Latest events and operations</CardDescription>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-64">
            <div className="space-y-3">
              {recent_activity.length === 0 ? (
                <p className="text-center text-gray-500 py-8">No recent activity</p>
              ) : (
                recent_activity.map((activity) => (
                  <div key={activity.id} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50">
                    <div className={`p-2 rounded-full bg-gray-100 ${getActivityStatusColor(activity.status)}`}>
                      {getActivityIcon(activity.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium">{activity.title}</p>
                        <p className="text-xs text-gray-500">
                          {new Date(activity.timestamp).toLocaleTimeString()}
                        </p>
                      </div>
                      <p className="text-sm text-gray-500">{activity.description}</p>
                      {activity.status === 'error' && (
                        <Badge variant="destructive" className="mt-1 text-xs">
                          Error
                        </Badge>
                      )}
                    </div>
                  </div>
                ))
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
} 