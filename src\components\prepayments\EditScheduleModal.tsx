import React, { useState, useEffect, useMemo } from 'react';

// --- Shadcn/UI & Lucide Imports ---
import { Button } from '@/components/ui/button';
import {
    DraggableDialog,
    DraggableDialogContent,
    DraggableDialogDescription,
    DraggableDialogFooter,
    DraggableDialogHeader,
    DraggableDialogTitle,
    DraggableDialogClose,
} from '@/components/ui/draggable-dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Checkbox } from '@/components/ui/checkbox';
import {
    Loader2,
    Save,
    AlertCircle,
    List,
    Lock,
} from 'lucide-react';

// --- Type Definitions ---
type SourceLineItemStatus = 'available' | 'selected' | 'released' | 'assigned_other';

type SourceLineItem = {
    lineItemId: string;
    description: string;
    amount: number;
    status: SourceLineItemStatus;
    linkedScheduleId?: string | null;
};

export type ScheduleEditData = {
    scheduleId?: string | null;
    originalAmount?: number;
    currencyCode: string;
    amortizationStartDate: string;
    amortizationEndDate: string;
    numberOfPeriods: number;
    prepaymentAccountCode: string;
    expenseAccountCode: string | null;
};

// --- Component Props ---
interface EditScheduleModalProps {
    isOpen: boolean;
    onClose: () => void;
    initialScheduleData: Omit<ScheduleEditData, 'originalAmount' | 'currencyCode'> | null;
    allSourceLineItems: SourceLineItem[];
    currencyCode: string;
    availablePrepaymentAccounts: { code: string; name: string }[];
    availableExpenseAccounts: { code: string; name: string }[];
    onSave: (scheduleDetails: Omit<ScheduleEditData, 'scheduleId' | 'currencyCode'>, selectedLineIds: string[]) => Promise<void>;
    isSaving: boolean;
    saveError: string | null;
}

// --- Helper Function to Calculate End Date ---
const calculateEndDate = (startDateIso: string, periods: number): string => {
    try {
        const startDate = new Date(startDateIso);
        const endDate = new Date(startDate.getFullYear(), startDate.getMonth() + periods - 1, startDate.getDate());
        endDate.setUTCHours(0, 0, 0, 0);
        return endDate.toISOString();
    } catch (e) {
        console.error("Error calculating end date:", e);
        return startDateIso;
    }
};

// --- Edit Schedule Modal Component ---
export function EditScheduleModal({
    isOpen,
    onClose,
    initialScheduleData,
    allSourceLineItems = [],
    currencyCode,
    availablePrepaymentAccounts = [],
    availableExpenseAccounts = [],
    onSave,
    isSaving,
    saveError,
}: EditScheduleModalProps) {

    const [scheduleParams, setScheduleParams] = useState<Omit<ScheduleEditData, 'scheduleId' | 'originalAmount' | 'currencyCode'>>({
        amortizationStartDate: '',
        numberOfPeriods: 0,
        prepaymentAccountCode: '',
        expenseAccountCode: null,
        amortizationEndDate: '',
    });
    
    const [selectedLineIds, setSelectedLineIds] = useState<Set<string>>(new Set());

    // Initialize form state and selections when data changes
    useEffect(() => {
        if (initialScheduleData) {
            setScheduleParams({
                amortizationStartDate: initialScheduleData.amortizationStartDate,
                numberOfPeriods: initialScheduleData.numberOfPeriods,
                prepaymentAccountCode: initialScheduleData.prepaymentAccountCode,
                expenseAccountCode: initialScheduleData.expenseAccountCode,
                amortizationEndDate: initialScheduleData.amortizationEndDate,
            });
            const initialSelected = new Set<string>(
                allSourceLineItems
                    .filter(line => line.linkedScheduleId === initialScheduleData.scheduleId)
                    .map(line => line.lineItemId)
            );
            setSelectedLineIds(initialSelected);
        } else {
            setScheduleParams({
                amortizationStartDate: '',
                numberOfPeriods: 12,
                prepaymentAccountCode: availablePrepaymentAccounts[0]?.code || '',
                expenseAccountCode: null,
                amortizationEndDate: '',
            });
            setSelectedLineIds(new Set());
        }
    }, [initialScheduleData, allSourceLineItems, availablePrepaymentAccounts]);

    // Calculate total amount based on selected lines
    const currentAmortizableAmount = useMemo(() => {
        return allSourceLineItems.reduce((sum, line) => {
            return selectedLineIds.has(line.lineItemId) ? sum + line.amount : sum;
        }, 0);
    }, [allSourceLineItems, selectedLineIds]);

    // Handle schedule parameter input changes
    const handleParamChange = (field: keyof typeof scheduleParams, value: string | number | null) => {
        setScheduleParams(prev => ({ ...prev, [field]: value }));
    };

    // Handle line item selection change
    const handleLineSelectionChange = (lineItemId: string, checked: boolean | 'indeterminate') => {
        setSelectedLineIds(prev => {
            const newSet = new Set(prev);
            if (checked === true) {
                newSet.add(lineItemId);
            } else {
                newSet.delete(lineItemId);
            }
            return newSet;
        });
    };

    // Handle save action
    const handleSaveClick = async () => {
        if (selectedLineIds.size === 0) {
            alert("Please select at least one line item to include in the schedule.");
            return;
        }
        if (!scheduleParams.amortizationStartDate || !scheduleParams.numberOfPeriods || scheduleParams.numberOfPeriods < 1) {
            alert("Please provide a valid Start Date and Number of Periods.");
            return;
        }
        if (!scheduleParams.expenseAccountCode) {
            alert("Please select an Expense Account.");
            return;
        }
        if (!scheduleParams.prepaymentAccountCode) {
            alert("Please select a Prepayment Account.");
            return;
        }

        const calculatedEndDate = calculateEndDate(scheduleParams.amortizationStartDate, scheduleParams.numberOfPeriods);

        const scheduleDetailsToSave: Omit<ScheduleEditData, 'scheduleId' | 'currencyCode'> = {
            originalAmount: currentAmortizableAmount,
            amortizationStartDate: scheduleParams.amortizationStartDate,
            numberOfPeriods: Number(scheduleParams.numberOfPeriods),
            prepaymentAccountCode: scheduleParams.prepaymentAccountCode,
            expenseAccountCode: scheduleParams.expenseAccountCode,
            amortizationEndDate: calculatedEndDate,
        };

        try {
            await onSave(scheduleDetailsToSave, Array.from(selectedLineIds));
        } catch (err) {
            console.error("Save failed:", err);
        }
    };

    // Helper to format date for display
    const formatDateForInput = (isoDate: string | undefined | null): string => {
        if (!isoDate) return '';
        try {
            return isoDate.substring(0, 10);
        } catch {
            return '';
        }
    };

    // Calculate display end date based on current form state
    const displayEndDate = scheduleParams.amortizationStartDate && scheduleParams.numberOfPeriods
        ? calculateEndDate(scheduleParams.amortizationStartDate, scheduleParams.numberOfPeriods)
        : '';

    return (
        <DraggableDialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
            <DraggableDialogContent className="flex flex-col p-0 gap-0">
                <DraggableDialogHeader className="p-4 border-b flex-shrink-0">
                    <DraggableDialogTitle>{initialScheduleData?.scheduleId ? 'Edit' : 'Create'} Amortization Schedule</DraggableDialogTitle>
                    <DraggableDialogDescription>
                        {initialScheduleData?.scheduleId
                          ? `Adjust the details and included lines for schedule ID: ${initialScheduleData.scheduleId}`
                          : `Select line items and define the schedule parameters.`}
                    </DraggableDialogDescription>
                </DraggableDialogHeader>

                <div className="flex-grow grid grid-cols-1 md:grid-cols-2 gap-0 overflow-hidden">
                    {/* Left Column: Selectable Source Lines & Amount */}
                    <div className="flex flex-col border-r overflow-hidden">
                        <h3 className="text-sm font-semibold p-3 border-b bg-gray-50 flex-shrink-0 flex items-center">
                            <List className="h-4 w-4 mr-2" />
                            Select Source Line Items
                        </h3>
                        <div className="p-3 border-b flex-shrink-0 space-y-1">
                             <Label htmlFor="amortizableAmount" className="text-xs font-medium">
                                Amount to Amortize
                            </Label>
                            <Input
                                id="amortizableAmount"
                                value={`${currentAmortizableAmount.toFixed(2)} ${currencyCode}`}
                                disabled
                                className="bg-muted font-medium h-8 text-sm"
                            />
                        </div>
                        <ScrollArea className="flex-grow p-3">
                            {allSourceLineItems && allSourceLineItems.length > 0 ? (
                                <Table className="text-xs">
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead className="w-6"></TableHead>
                                            <TableHead>Description</TableHead>
                                            <TableHead className="text-right">Amount</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {allSourceLineItems.map((line) => {
                                            const isSelected = selectedLineIds.has(line.lineItemId);
                                            const isDisabled = line.status === 'released' || (line.status === 'assigned_other' && line.linkedScheduleId !== initialScheduleData?.scheduleId);

                                            return (
                                                <TableRow key={line.lineItemId} data-state={isSelected ? 'selected' : ''} className={isDisabled ? 'opacity-50' : ''}>
                                                    <TableCell className="py-1 align-top">
                                                        <Checkbox
                                                            id={`line-${line.lineItemId}`}
                                                            checked={isSelected}
                                                            disabled={isDisabled}
                                                            onCheckedChange={(checked) => handleLineSelectionChange(line.lineItemId, checked)}
                                                            aria-label={`Select line ${line.description}`}
                                                        />
                                                    </TableCell>
                                                    <TableCell className="py-1 align-top">
                                                        <label htmlFor={`line-${line.lineItemId}`} className={`block ${isDisabled ? 'cursor-not-allowed' : 'cursor-pointer'}`}>
                                                            {line.description || '(No description)'}
                                                            {isDisabled && <Lock className="h-3 w-3 inline-block ml-1 text-muted-foreground" />}
                                                        </label>
                                                    </TableCell>
                                                    <TableCell className="text-right py-1 align-top">
                                                        <label htmlFor={`line-${line.lineItemId}`} className={isDisabled ? 'cursor-not-allowed' : 'cursor-pointer'}>
                                                            {line.amount.toFixed(2)}
                                                        </label>
                                                    </TableCell>
                                                </TableRow>
                                            );
                                        })}
                                    </TableBody>
                                </Table>
                            ) : (
                                <p className="text-sm text-muted-foreground p-4 text-center">No source line items found for this transaction.</p>
                            )}
                        </ScrollArea>
                    </div>

                    {/* Right Column: Editable Form */}
                    <div className="flex flex-col overflow-hidden">
                        <h3 className="text-sm font-semibold p-3 border-b bg-gray-50 flex-shrink-0">Schedule Details</h3>
                        <ScrollArea className="flex-grow p-4">
                            <div className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                    <div className="space-y-1">
                                        <Label htmlFor="startDate">Start Date</Label>
                                        <Input
                                            id="startDate"
                                            type="date"
                                            value={formatDateForInput(scheduleParams.amortizationStartDate)}
                                            onChange={(e) => handleParamChange('amortizationStartDate', e.target.value ? e.target.value + 'T00:00:00Z' : null)}
                                        />
                                    </div>
                                    <div className="space-y-1">
                                        <Label htmlFor="periods">Periods</Label>
                                        <Input
                                            id="periods"
                                            type="number"
                                            min="1"
                                            value={scheduleParams.numberOfPeriods || ''}
                                            onChange={(e) => handleParamChange('numberOfPeriods', parseInt(e.target.value, 10) || null)}
                                        />
                                    </div>
                                </div>

                                <div className="space-y-1">
                                    <Label>Calculated End Date</Label>
                                    <Input
                                        value={formatDateForInput(displayEndDate)}
                                        disabled
                                        className="bg-muted text-sm"
                                    />
                                </div>

                                <div className="space-y-1">
                                    <Label htmlFor="prepaymentAccount">Prepayment Acct.</Label>
                                    <Select
                                        value={scheduleParams.prepaymentAccountCode || ''}
                                        onValueChange={(value) => handleParamChange('prepaymentAccountCode', value)}
                                    >
                                        <SelectTrigger id="prepaymentAccount">
                                            <SelectValue placeholder="Select account..." />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {availablePrepaymentAccounts.map(acc => (
                                                <SelectItem key={acc.code} value={acc.code}>
                                                    {acc.code} - {acc.name}
                                                </SelectItem>
                                            ))}
                                            {initialScheduleData?.prepaymentAccountCode && !availablePrepaymentAccounts.some(a => a.code === initialScheduleData.prepaymentAccountCode) && (
                                                <SelectItem value={initialScheduleData.prepaymentAccountCode} disabled>
                                                    {initialScheduleData.prepaymentAccountCode} (Current)
                                                </SelectItem>
                                            )}
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="space-y-1">
                                    <Label htmlFor="expenseAccount">Expense Acct.</Label>
                                    <Select
                                        value={scheduleParams.expenseAccountCode || ''}
                                        onValueChange={(value) => handleParamChange('expenseAccountCode', value)}
                                    >
                                        <SelectTrigger id="expenseAccount" className={`${!scheduleParams.expenseAccountCode ? 'border-red-500' : ''}`}>
                                            <SelectValue placeholder="Select account..." />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {availableExpenseAccounts.map(acc => (
                                                <SelectItem key={acc.code} value={acc.code}>
                                                    {acc.code} - {acc.name}
                                                </SelectItem>
                                            ))}
                                            {initialScheduleData?.expenseAccountCode && !availableExpenseAccounts.some(a => a.code === initialScheduleData.expenseAccountCode) && (
                                                <SelectItem value={initialScheduleData.expenseAccountCode} disabled>
                                                    {initialScheduleData.expenseAccountCode} (Current)
                                                </SelectItem>
                                            )}
                                        </SelectContent>
                                    </Select>
                                    {!scheduleParams.expenseAccountCode && (
                                        <p className="text-xs text-red-600 pt-1">Expense account selection is required.</p>
                                    )}
                                </div>
                            </div>
                        </ScrollArea>
                    </div>
                </div>

                {saveError && (
                    <div className="px-4 pt-2 flex-shrink-0">
                        <Alert variant="destructive">
                            <AlertCircle className="h-4 w-4" />
                            <AlertTitle>Save Failed</AlertTitle>
                            <AlertDescription>{saveError}</AlertDescription>
                        </Alert>
                    </div>
                )}

                <DraggableDialogFooter className="p-4 border-t flex-shrink-0">
                    <DraggableDialogClose asChild>
                        <Button type="button" variant="outline">
                            Cancel
                        </Button>
                    </DraggableDialogClose>
                    <Button
                        type="button"
                        onClick={handleSaveClick}
                        disabled={isSaving || !scheduleParams.expenseAccountCode || selectedLineIds.size === 0}
                    >
                        {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                        {initialScheduleData?.scheduleId ? 'Save Changes' : 'Create Schedule'}
                    </Button>
                </DraggableDialogFooter>
            </DraggableDialogContent>
        </DraggableDialog>
    );
} 