import React from 'react';
import { render, screen } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { DashboardPagination } from '../DashboardPagination';
import type { PaginationData } from '../../types';

describe('DashboardPagination', () => {
  const mockOnPageChange = vi.fn();

  beforeEach(() => {
    mockOnPageChange.mockClear();
  });

  it('should not render when loading', () => {
    const pagination: PaginationData = {
      current_page: 1,
      total_pages: 2,
      total_items: 10,
      page_size: 5
    };

    const { container } = render(
      <DashboardPagination
        pagination={pagination}
        isLoading={true}
        onPageChange={mockOnPageChange}
      />
    );

    expect(container.firstChild).toBeNull();
  });

  it('should not render when pagination is null', () => {
    const { container } = render(
      <DashboardPagination
        pagination={null}
        isLoading={false}
        onPageChange={mockOnPageChange}
      />
    );

    expect(container.firstChild).toBeNull();
  });

  it('should not render when totalPages <= 1 (snake_case)', () => {
    const pagination: PaginationData = {
      current_page: 1,
      total_pages: 1,
      total_items: 2,
      page_size: 20
    };

    const { container } = render(
      <DashboardPagination
        pagination={pagination}
        isLoading={false}
        onPageChange={mockOnPageChange}
      />
    );

    expect(container.firstChild).toBeNull();
  });

  it('should not render when totalPages <= 1 (camelCase)', () => {
    const pagination: PaginationData = {
      currentPage: 1,
      totalPages: 1,
      totalItems: 2,
      pageSize: 20
    };

    const { container } = render(
      <DashboardPagination
        pagination={pagination}
        isLoading={false}
        onPageChange={mockOnPageChange}
      />
    );

    expect(container.firstChild).toBeNull();
  });

  it('should not render when totalItems <= 0', () => {
    const pagination: PaginationData = {
      current_page: 1,
      total_pages: 2,
      total_items: 0,
      page_size: 20
    };

    const { container } = render(
      <DashboardPagination
        pagination={pagination}
        isLoading={false}
        onPageChange={mockOnPageChange}
      />
    );

    expect(container.firstChild).toBeNull();
  });

  it('should render when there are multiple pages (snake_case)', () => {
    const pagination: PaginationData = {
      current_page: 1,
      total_pages: 3,
      total_items: 25,
      page_size: 10
    };

    render(
      <DashboardPagination
        pagination={pagination}
        isLoading={false}
        onPageChange={mockOnPageChange}
      />
    );

    expect(screen.getByText('Page 1 of 3 (25 items)')).toBeInTheDocument();
    expect(screen.getByRole('navigation')).toBeInTheDocument();
  });

  it('should render when there are multiple pages (camelCase)', () => {
    const pagination: PaginationData = {
      currentPage: 2,
      totalPages: 5,
      totalItems: 100,
      pageSize: 20
    };

    render(
      <DashboardPagination
        pagination={pagination}
        isLoading={false}
        onPageChange={mockOnPageChange}
      />
    );

    expect(screen.getByText('Page 2 of 5 (100 items)')).toBeInTheDocument();
    expect(screen.getByRole('navigation')).toBeInTheDocument();
  });

  it('should handle mixed field naming conventions', () => {
    const pagination: PaginationData = {
      current_page: 1,
      totalPages: 3,
      total_items: 25,
      pageSize: 10
    };

    render(
      <DashboardPagination
        pagination={pagination}
        isLoading={false}
        onPageChange={mockOnPageChange}
      />
    );

    expect(screen.getByText('Page 1 of 3 (25 items)')).toBeInTheDocument();
  });
});
