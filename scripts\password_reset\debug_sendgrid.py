#!/usr/bin/env python3
"""
Debug script for SendGrid 401 Unauthorized error.

This script will help diagnose SendGrid authentication issues.
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def load_env_file():
    """Load environment variables from .env file"""
    
    env_files = ['.env', '../.env', '../../.env']
    
    for env_file in env_files:
        env_path = Path(env_file)
        if env_path.exists():
            logger.info(f"📁 Loading .env file: {env_path.absolute()}")
            
            try:
                with open(env_path, 'r') as f:
                    for line in f:
                        line = line.strip()
                        if not line or line.startswith('#'):
                            continue
                        
                        if '=' in line:
                            key, value = line.split('=', 1)
                            key = key.strip()
                            value = value.strip().strip('"').strip("'")
                            
                            if not os.getenv(key):
                                os.environ[key] = value
                
                return True
                
            except Exception as e:
                logger.error(f"❌ Error reading .env file: {e}")
                return False
    
    return True

async def debug_sendgrid():
    """Debug SendGrid configuration and test basic API call"""
    
    try:
        # Load environment
        load_env_file()
        
        # Check API key
        api_key = os.getenv('SENDGRID_API_KEY')
        if not api_key:
            logger.error("❌ SENDGRID_API_KEY not found")
            return False
        
        logger.info(f"🔑 API Key found: {api_key[:10]}...{api_key[-4:]}")
        
        # Check if API key format is correct
        if not api_key.startswith('SG.'):
            logger.error("❌ API key doesn't start with 'SG.' - this might be incorrect")
            return False
        
        logger.info("✅ API key format looks correct")
        
        # Import SendGrid
        from sendgrid import SendGridAPIClient
        from sendgrid.helpers.mail import Mail, From, To
        
        # Create client
        sg = SendGridAPIClient(api_key=api_key)
        
        # Test 1: Check API key validity with a simple API call
        logger.info("\n🧪 Test 1: Checking API key validity...")
        try:
            # This is a simple API call to check if the key works
            response = sg.client.user.get()
            logger.info(f"✅ API key is valid. User info retrieved: {response.status_code}")
        except Exception as e:
            logger.error(f"❌ API key test failed: {e}")
            logger.info("💡 This usually means:")
            logger.info("   1. The API key is incorrect")
            logger.info("   2. The API key has been revoked")
            logger.info("   3. The SendGrid account is suspended")
            return False
        
        # Test 2: Check sender email verification
        logger.info("\n🧪 Test 2: Checking sender email configuration...")
        from_email = os.getenv('SENDGRID_FROM_EMAIL', '<EMAIL>')
        logger.info(f"📧 From email: {from_email}")
        
        # Test 3: Try to send a simple test email
        logger.info("\n🧪 Test 3: Attempting to send test email...")
        
        message = Mail(
            from_email=From(from_email, 'DRCR Labs Test'),
            to_emails=To('<EMAIL>'),
            subject='SendGrid Test Email',
            html_content='<p>This is a test email to verify SendGrid configuration.</p>'
        )
        
        try:
            response = sg.send(message)
            logger.info(f"✅ Email sent successfully! Status: {response.status_code}")
            logger.info(f"📧 Response headers: {dict(response.headers)}")
            
            if response.status_code in [200, 201, 202]:
                logger.info("🎉 SendGrid is working correctly!")
                logger.info("📬 Check <EMAIL> for the test email")
                return True
            else:
                logger.warning(f"⚠️  Unexpected status code: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Email sending failed: {e}")
            
            # Parse the error for more specific information
            error_str = str(e)
            if "401" in error_str:
                logger.error("🔍 401 Unauthorized Error Analysis:")
                logger.error("   This typically means:")
                logger.error("   1. ❌ Invalid API key")
                logger.error("   2. ❌ API key doesn't have permission to send emails")
                logger.error("   3. ❌ Sender email is not verified in SendGrid")
                logger.error("   4. ❌ SendGrid account is not active")
                
                logger.info("\n🔧 Troubleshooting steps:")
                logger.info("   1. Go to https://app.sendgrid.com/settings/api_keys")
                logger.info("   2. Verify your API key exists and has 'Mail Send' permissions")
                logger.info("   3. Go to https://app.sendgrid.com/settings/sender_auth")
                logger.info("   4. Verify your sender email or domain")
                logger.info("   5. Check your SendGrid account status")
                
            elif "403" in error_str:
                logger.error("🔍 403 Forbidden Error:")
                logger.error("   - Your API key doesn't have permission to send emails")
                logger.error("   - Check API key permissions in SendGrid dashboard")
                
            return False
            
    except Exception as e:
        logger.error(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main function"""
    
    logger.info("SendGrid Debug Tool")
    logger.info("=" * 40)
    
    success = await debug_sendgrid()
    
    if success:
        logger.info("\n🎉 SendGrid is configured correctly!")
        logger.info("You can now use the password reset functionality.")
    else:
        logger.error("\n❌ SendGrid configuration issues found.")
        logger.info("Please fix the issues above and try again.")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1) 