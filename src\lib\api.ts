import axios from 'axios';
import type { AxiosInstance, AxiosResponse, AxiosError, AxiosRequestConfig } from 'axios';
import { toast } from 'sonner';
import { auth } from './firebase';

// Extend AxiosRequestConfig to include our custom properties
interface ExtendedAxiosRequestConfig extends AxiosRequestConfig {
  cache?: boolean;
  cacheTtl?: number;
}

export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success: boolean;
}

// Type definitions for API responses
export interface EntitySummary {
  entity_id: string;
  entity_name: string;
  type: 'xero' | 'qbo' | 'manual';
  connection_status: 'active' | 'error' | 'disconnected' | 'syncing' | 'pending';
  last_sync?: string;
  error_message?: string;
  sync_status?: {
    is_syncing: boolean;
    current_step: string;
    progress_percentage: number;
    estimated_remaining?: string;
    user_message: string;
    last_sync_completed?: string;
    sync_duration_warning?: string;
  };
}

export interface ClientSummary {
  client_id: string;
  name: string; // Fix: API returns 'name', not 'client_name'
  status: string;
  entities?: EntitySummary[]; // Make optional since it's not always included
  pending_items_count?: number;
  error_count?: number;
  last_activity?: string;
  overall_status?: 'ok' | 'action_needed' | 'error';
  // Additional fields from actual API response
  client_type?: string;
  industry?: string;
  entities_count?: number;
  active_entities_count?: number;
}

export interface EntitySettings {
  entity_id: string;
  entity_name: string;
  prepayment_asset_account_codes: string[];
  excluded_pnl_account_codes?: string[];
  default_amortization_months?: number;
}

export interface Account {
  account_id?: string;
  code: string;
  name: string;
  type: 'ASSET' | 'EXPENSE' | 'LIABILITY' | 'EQUITY' | 'REVENUE' | 'CURRENT';
  class?: 'ASSET' | 'EXPENSE' | 'LIABILITY' | 'EQUITY' | 'REVENUE';
  status?: string;
  description?: string;
  raw_xero_data?: {
    Class?: string;
    ReportingCode?: string;
    [key: string]: any;
  };
}

export interface DashboardData {
  pending_review: {
    count: number;
    total_amount: number;
  };
  approved: {
    count: number;
    total_amount: number;
  };
  this_month: {
    count: number;
    total_amount: number;
  };
  recent_transactions: any[];
}

// Cache interface
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

// Request deduplication
interface PendingRequest {
  promise: Promise<any>;
  timestamp: number;
}

export class ApiClient {
  private client: AxiosInstance;
  private cache = new Map<string, CacheEntry<any>>();
  private pendingRequests = new Map<string, PendingRequest>();
  private readonly DEFAULT_CACHE_TTL = 5 * 60 * 1000; // 5 minutes
  private readonly REQUEST_TIMEOUT = 15000; // 15 seconds - increased timeout

  constructor(baseURL: string = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081') {
    console.log('DEBUG: ApiClient initialized with baseURL:', baseURL);

    // Clear any existing cache on initialization
    this.cache.clear();

    this.client = axios.create({
      baseURL,
      timeout: this.REQUEST_TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache', // Prevent browser caching
      },
      // Windows-specific optimizations
      maxRedirects: 3,
      validateStatus: (status) => status < 500, // Don't retry on 4xx errors
    });

    this.setupInterceptors();
    this.startCacheCleanup();
  }

  private setupInterceptors() {
    // Add request interceptor for auth token
    this.client.interceptors.request.use(
      async (config) => {
        const token = await this.getAuthToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => {
        // Log successful responses for debugging
        if (response.config.url?.includes('xero/connect/initiate')) {
          console.log('Response interceptor - successful response:', {
            status: response.status,
            statusText: response.statusText,
            data: response.data,
            url: response.config.url
          });
        }
        return response;
      },
      (error: AxiosError) => {
        // Log errors in response interceptor
        if (error.config?.url?.includes('xero/connect/initiate')) {
          console.error('Response interceptor - error:', {
            message: error.message,
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
            url: error.config?.url
          });
        }
        return this.handleApiError(error);
      }
    );
  }

  private async getAuthToken(): Promise<string | null> {
    const user = auth.currentUser;
    if (user) {
      const token = await user.getIdToken();
      return token;
    }
    return null;
  }

  private handleApiError(error: AxiosError) {
    console.log('handleApiError called with:', {
      status: error.response?.status,
      url: error.config?.url,
      message: error.message
    });

    if (error.response?.status === 401) {
      // Handle unauthorized errors
      console.log('Unauthorized access. Token may be expired or invalid.');

      // If we're not already on the login page, redirect
      if (window.location.pathname !== '/login') {
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }

  // Cache management
  private getCacheKey(url: string, config?: ExtendedAxiosRequestConfig): string {
    const params = config?.params ? JSON.stringify(config.params) : '';
    return `${url}${params}`;
  }

  private getCachedData<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (entry && Date.now() - entry.timestamp < entry.ttl) {
      return entry.data;
    }
    if (entry) {
      this.cache.delete(key); // Remove expired entry
    }
    return null;
  }

  private setCachedData<T>(key: string, data: T, ttl: number = this.DEFAULT_CACHE_TTL): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  private startCacheCleanup(): void {
    // Clean up expired cache entries every 2 minutes (more frequent for local dev)
    setInterval(() => {
      const now = Date.now();
      for (const [key, entry] of this.cache.entries()) {
        if (now - entry.timestamp > entry.ttl) {
          this.cache.delete(key);
        }
      }
    }, 2 * 60 * 1000);
  }

  // Request deduplication
  private async deduplicateRequest<T>(key: string, requestFn: () => Promise<T>): Promise<T> {
    const existing = this.pendingRequests.get(key);
    if (existing && Date.now() - existing.timestamp < 1000) { // 1 second deduplication window for faster local dev
      return existing.promise;
    }

    const promise = requestFn().finally(() => {
      this.pendingRequests.delete(key);
    });

    this.pendingRequests.set(key, {
      promise,
      timestamp: Date.now()
    });

    return promise;
  }

  async get<T>(url: string, config?: ExtendedAxiosRequestConfig): Promise<T> {
    const cacheKey = this.getCacheKey(url, config);

    // Check cache first (if caching is enabled)
    if (config?.cache !== false) {
      const cachedData = this.getCachedData<T>(cacheKey);
      if (cachedData) {
        console.log(`🚀 Cache HIT: ${url}`);
        return cachedData;
      }
    }

    // Deduplicate requests
    return this.deduplicateRequest(cacheKey, async () => {
      try {
        const startTime = performance.now();

        // Create a clean config object for axios, removing our custom properties
        const axiosConfig: AxiosRequestConfig = { ...config };
        delete (axiosConfig as any).cache;
        delete (axiosConfig as any).cacheTtl;

        const response = await this.client.get<T>(url, axiosConfig);

        const endTime = performance.now();
        const duration = Math.round(endTime - startTime);

        // Only log slow requests in development
        if (import.meta.env.DEV && duration > 1000) {
          console.warn(`🐌 SLOW API ${url}: ${duration}ms`);
        } else if (import.meta.env.DEV && duration > 500) {
          console.log(`⏱️ API ${url}: ${duration}ms`);
        }

        // Ensure we return the data, not the full response object
        const data = response.data;

        // Cache the response (if caching is enabled)
        if (config?.cache !== false) {
          this.setCachedData(cacheKey, data, config?.cacheTtl);
        }

        return data;
      } catch (error) {
        console.error(`API GET error for ${url}:`, error);

        // Log detailed error information
        if (error && typeof error === 'object') {
          const axiosError = error as any;
          console.error('Detailed error info:', {
            message: axiosError.message,
            code: axiosError.code,
            status: axiosError.response?.status,
            statusText: axiosError.response?.statusText,
            data: axiosError.response?.data,
            headers: axiosError.response?.headers,
            config: {
              url: axiosError.config?.url,
              method: axiosError.config?.method,
              baseURL: axiosError.config?.baseURL
            }
          });
        }

        throw error;
      }
    });
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig & { cache?: boolean; cacheTtl?: number }): Promise<T> {
    try {
      console.log(`ApiClient POST: ${url}`, data);

      // Create a clean config object for axios, removing our custom properties
      const axiosConfig: AxiosRequestConfig = { ...config };
      delete (axiosConfig as any).cache;
      delete (axiosConfig as any).cacheTtl;

      const response = await this.client.post<T>(url, data, axiosConfig);
      console.log(`ApiClient POST success: ${url}`, response.data);

      // Invalidate related cache entries on mutations
      this.invalidateCache(url);

      return response.data;
    } catch (error) {
      console.error(`ApiClient POST error: ${url}`, error);

      // Log detailed error information for debugging
      if (error && typeof error === 'object') {
        const axiosError = error as any;
        console.error('ApiClient POST detailed error:', {
          message: axiosError.message,
          code: axiosError.code,
          status: axiosError.response?.status,
          statusText: axiosError.response?.statusText,
          data: axiosError.response?.data,
          headers: axiosError.response?.headers,
          config: {
            url: axiosError.config?.url,
            method: axiosError.config?.method,
            baseURL: axiosError.config?.baseURL
          }
        });
      }

      throw error;
    }
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.put<T>(url, data, config);

    // Invalidate related cache entries on mutations
    this.invalidateCache(url);

    return response.data;
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.delete<T>(url, config);

    // Invalidate related cache entries on mutations
    this.invalidateCache(url);

    return response.data;
  }

  private invalidateCache(url: string): void {
    // Remove cache entries that might be affected by this mutation
    const keysToDelete: string[] = [];
    for (const key of this.cache.keys()) {
      if (key.includes(url.split('/')[1])) { // Match by resource type
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach(key => this.cache.delete(key));
  }

  // Client Management Methods with optimized caching
  async getClients(): Promise<{ clients: ClientSummary[] }> {
    return this.get<{ clients: ClientSummary[] }>('/clients/', {
      cache: true,
      cacheTtl: 10 * 60 * 1000 // Cache for 10 minutes - clients don't change often
    } as ExtendedAxiosRequestConfig);
  }

  async getClientsSummary(filters?: {
    page?: number;
    limit?: number;
    client_filter?: string;
    status_filter?: string;
  }): Promise<{
    clients: ClientSummary[];
    pagination: {
      current_page: number;
      page_size: number;
      total_items: number;
      total_pages: number;
    };
  }> {
    const params = new URLSearchParams();
    if (filters?.page) params.append('page', filters.page.toString());
    if (filters?.limit) params.append('limit', filters.limit.toString());
    if (filters?.client_filter) params.append('client_filter', filters.client_filter);
    if (filters?.status_filter) params.append('status_filter', filters.status_filter);

    const queryString = params.toString();
    return this.get(`/clients/summary${queryString ? `?${queryString}` : ''}`, {
      cache: false // Disable cache temporarily to ensure fresh data
    } as ExtendedAxiosRequestConfig);
  }

  async createClient(clientData: { name: string; [key: string]: any }): Promise<{ message: string; client_id: string; name: string }> {
    return this.post('/clients/', clientData);
  }

  async getClient(clientId: string): Promise<ClientSummary> {
    return this.get(`/clients/${clientId}`, {
      cache: true,
      cacheTtl: 60 * 1000
    } as ExtendedAxiosRequestConfig);
  }

  async updateClient(clientId: string, clientData: any): Promise<{ message: string }> {
    return this.put(`/clients/${clientId}`, clientData);
  }

  // Entity Management Methods
  async getEntitiesForClient(clientId: string): Promise<{ entities: EntitySummary[] }> {
    return this.get(`/entities/?client_id=${clientId}`, {
      cache: true,
      cacheTtl: 5 * 60 * 1000 // Cache for 5 minutes - entities don't change often
    } as ExtendedAxiosRequestConfig);
  }

  async getEntity(entityId: string): Promise<EntitySummary & { settings?: EntitySettings }> {
    return this.get(`/entities/${entityId}`, {
      cache: true,
      cacheTtl: 60 * 1000 // 1 minute cache
    } as ExtendedAxiosRequestConfig);
  }

  async updateEntitySettings(entityId: string, settings: Partial<EntitySettings>): Promise<{ message: string }> {
    return this.put(`/entities/${entityId}/settings`, settings);
  }

  async checkEntityConnectionStatus(entityId: string): Promise<{
    entity_id: string;
    entity_name: string;
    type: string;
    connection_status: {
      status: 'active' | 'error' | 'disconnected' | 'syncing' | 'pending';
      last_checked?: string;
      error_message?: string;
    };
  }> {
    return this.get(`/entities/${entityId}/connection/status`);
  }

  async disconnectEntity(entityId: string): Promise<{ message: string }> {
    return this.post(`/entities/${entityId}/connection/disconnect`);
  }

  // Xero Integration Methods
  async initiateXeroConnection(clientId: string): Promise<{ authorization_url: string }> {
    console.log(`ApiClient: Initiating Xero connection for client ${clientId}`);

    // Use longer timeout and disable caching for Xero connection initiation
    const response = await this.get<{ authorization_url: string }>(`/xero/connect/initiate/${clientId}`, {
      timeout: 45000, // 45 seconds for initiation
      cache: false // Disable caching for this request
    } as ExtendedAxiosRequestConfig);

    console.log('ApiClient: Raw response from Xero initiate:', response);
    console.log('ApiClient: Response type:', typeof response);
    if (response && typeof response === 'object') {
      console.log('ApiClient: Response keys:', Object.keys(response));
    }
    return response;
  }

  async getXeroConfiguration(clientId: string, entities?: string): Promise<{
    client: {
      client_id: string;
      name: string;
      status: string;
    };
    entities: Array<{
      entity_id: string;
      entity_name: string;
      type: string;
      status: string;
      connection_status: string;
      settings: any;
      requires_configuration: boolean;
    }>;
    has_xero_entities: boolean;
    configuration_complete: boolean;
  }> {
    const params = entities ? `?entities=${entities}` : '';
    return this.get(`/clients/${clientId}/xero/configure${params}`, {
      cache: true,
      cacheTtl: 30 * 1000 // 30 second cache
    } as ExtendedAxiosRequestConfig);
  }

  async getXeroAccounts(entityId: string): Promise<{ accounts: Account[] }> {
    console.log(`🔍 Fetching Xero accounts for entity: ${entityId}`);
    const result = await this.get(`/xero/entities/${entityId}/accounts`, {
      timeout: 30000, // 30 seconds for accounts fetch
      cache: false, // Disable cache for debugging
      cacheTtl: 5 * 60 * 1000 // Cache for 5 minutes
    } as ExtendedAxiosRequestConfig);

    console.log(`📊 Xero accounts response:`, JSON.stringify(result, null, 2));

    // Handle the response properly - the API returns { accounts: Account[] }
    const accounts = (result as any)?.accounts || [];
    console.log(`📊 Total accounts found: ${accounts.length}`);
    console.log(`📊 Asset accounts (by class): ${accounts.filter((acc: Account) => acc.class === 'ASSET').length}`);
    console.log(`📊 Asset accounts (by type): ${accounts.filter((acc: Account) => acc.type === 'ASSET').length}`);

    return { accounts };
  }

  async revokeXeroConnection(entityId: string): Promise<{ message: string }> {
    return this.post(`/xero/entities/${entityId}/revoke`, {}, {
      timeout: 30000 // 30 seconds for revoke operation
    });
  }

  // Organization Selection Methods
  async getAvailableXeroOrganizations(clientId: string): Promise<{
    client_id: string;
    organizations: Array<{
      tenant_id: string;
      tenant_name: string;
      is_already_connected: boolean;
      connection_type: string;
    }>;
    reconnections_processed?: number;
    message?: string;
  }> {
    return this.get(`/xero/clients/${clientId}/xero/available-organizations`);
  }

  async connectSelectedXeroOrganization(clientId: string, tenantId: string): Promise<{
    message: string;
    entity: {
      entity_id: string;
      entity_name: string;
      requires_configuration: boolean;
    };
  }> {
    console.log(`ApiClient: Connecting Xero organization ${tenantId} for client ${clientId}`);
    try {
      const result = await this.post<{
        message: string;
        entity: {
          entity_id: string;
          entity_name: string;
          requires_configuration: boolean;
        };
      }>(`/xero/clients/${clientId}/xero/connect-organization`, {
        tenant_id: tenantId
      }, {
        timeout: 60000 // 60 seconds for organization connection (restored)
      });
      console.log('ApiClient: Connect organization successful:', result);
      return result;
    } catch (error) {
      console.error('ApiClient: Connect organization failed:', error);
      throw error;
    }
  }

  // Dashboard Methods
  async getClientDashboard(clientId: string, entityId?: string): Promise<DashboardData> {
    const params = entityId ? `?client_id=${clientId}&entity_id=${entityId}` : `?client_id=${clientId}`;
    return this.get(`/reports/dashboard${params}`);
  }

  // Transaction Methods
  async getTransactions(filters: {
    client_id: string;
    entity_id?: string;
    transaction_type?: string;
    status?: string;
    page?: number;
    limit?: number;
  }): Promise<{
    transactions: any[];
    total: number;
    page: number;
    limit: number;
    total_pages: number;
  }> {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });
    return this.get(`/transactions/?${params.toString()}`);
  }

  async getDashboardTransactions(filters: {
    client_id?: string;
    entity_id?: string;
    status?: string;
    transaction_type?: string;
    require_action?: boolean;
    page?: number;
    limit?: number;
  }): Promise<{
    transactions: any[];
    total: number;
    page: number;
    limit: number;
    total_pages: number;
  }> {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });
    return this.get(`/transactions/dashboard?${params.toString()}`);
  }

  async getTransaction(transactionId: string): Promise<any> {
    return this.get(`/transactions/${transactionId}`);
  }

  // Schedule Methods
  async getSchedule(scheduleId: string): Promise<any> {
    return this.get(`/schedules/${scheduleId}`);
  }

  async updateSchedule(scheduleId: string, scheduleData: any): Promise<{ message: string }> {
    return this.put(`/schedules/${scheduleId}`, scheduleData);
  }

  async confirmSchedule(scheduleId: string): Promise<{ message: string }> {
    return this.post(`/schedules/${scheduleId}/confirm`);
  }

  async skipSchedule(scheduleId: string, reason: string): Promise<{ message: string }> {
    return this.post(`/schedules/${scheduleId}/skip`, { reason });
  }

  // Reports Methods
  async getAmortizationReport(filters: {
    client_id: string;
    entity_id?: string;
    year?: number;
    month?: number;
  }): Promise<any> {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });
    return this.get(`/reports/amortization?${params.toString()}`);
  }
}

export const api = new ApiClient();
