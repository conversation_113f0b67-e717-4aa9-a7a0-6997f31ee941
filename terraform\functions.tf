# Create a storage bucket for function source code
resource "google_storage_bucket" "function_source_bucket" {
  name     = "${var.project_id}-function-source"
  location = var.region
  
  uniform_bucket_level_access = true
  
  depends_on = [google_project_service.services]
}

# Create the Xero sync consumer function
resource "google_cloudfunctions2_function" "xero_sync_consumer" {
  name        = "xero-sync-consumer"
  location    = var.region
  description = "Processes Xero synchronization requests from Pub/Sub"
  
  build_config {
    runtime     = "python39"
    entry_point = "process_xero_sync"
    
    source {
      storage_source {
        bucket = google_storage_bucket.function_source_bucket.name
        object = google_storage_bucket_object.xero_sync_consumer_source.name
      }
    }
  }
  
  service_config {
    max_instance_count = 10
    available_memory   = "256Mi"
    timeout_seconds    = 60
    
    environment_variables = {
      GCP_PROJECT_ID = var.project_id
    }
    
    service_account_email = google_service_account.function_service_account.email
  }
  
  event_trigger {
    trigger_region = var.region
    event_type     = "google.cloud.pubsub.topic.v1.messagePublished"
    pubsub_topic   = google_pubsub_topic.xero_sync_topic.id
    retry_policy   = "RETRY_POLICY_RETRY"
  }
  
  depends_on = [
    google_project_service.services,
    google_pubsub_topic.xero_sync_topic,
    google_storage_bucket_object.xero_sync_consumer_source
  ]
}

# Create the scheduled sync processor function
resource "google_cloudfunctions2_function" "scheduled_sync_processor" {
  name        = "scheduled-sync-processor"
  location    = var.region
  description = "Processes scheduled sync requests from Cloud Scheduler"
  
  build_config {
    runtime     = "python39"
    entry_point = "scheduled_sync_processor"
    
    source {
      storage_source {
        bucket = google_storage_bucket.function_source_bucket.name
        object = google_storage_bucket_object.scheduled_sync_processor_source.name
      }
    }
  }
  
  service_config {
    max_instance_count = 5
    available_memory   = "512Mi"
    timeout_seconds    = 300  # 5 minutes for processing multiple entities
    
    environment_variables = {
      GCP_PROJECT_ID = var.project_id
      PUBSUB_TOPIC_XERO_SYNC = var.xero_sync_topic_name
    }
    
    service_account_email = google_service_account.function_service_account.email
  }
  
  event_trigger {
    trigger_region = var.region
    event_type     = "google.cloud.pubsub.topic.v1.messagePublished"
    pubsub_topic   = google_pubsub_topic.scheduled_sync_topic.id
    retry_policy   = "RETRY_POLICY_RETRY"
  }
  
  depends_on = [
    google_project_service.services,
    google_pubsub_topic.scheduled_sync_topic,
    google_storage_bucket_object.scheduled_sync_processor_source
  ]
}

# Create a placeholder for the function source code
# In a real deployment, this would be replaced by a CI/CD pipeline
resource "google_storage_bucket_object" "xero_sync_consumer_source" {
  name   = "xero-sync-consumer-source.zip"
  bucket = google_storage_bucket.function_source_bucket.name
  source = "../cloud_functions/xero_sync_consumer.zip"  # This file would be created by a build script
  
  # Use a placeholder if the file doesn't exist yet
  content = fileexists("../cloud_functions/xero_sync_consumer.zip") ? "" : "placeholder"
  
  depends_on = [google_storage_bucket.function_source_bucket]
}

# Create a placeholder for the scheduled sync processor source code
resource "google_storage_bucket_object" "scheduled_sync_processor_source" {
  name   = "scheduled-sync-processor-source.zip"
  bucket = google_storage_bucket.function_source_bucket.name
  source = "../cloud_functions/scheduled_sync_processor.zip"  # This file would be created by a build script
  
  # Use a placeholder if the file doesn't exist yet
  content = fileexists("../cloud_functions/scheduled_sync_processor.zip") ? "" : "placeholder"
  
  depends_on = [google_storage_bucket.function_source_bucket]
}
